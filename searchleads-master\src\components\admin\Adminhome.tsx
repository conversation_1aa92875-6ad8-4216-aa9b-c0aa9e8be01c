"use client";
// import { useAdminAuth } from "@/context/AdminAuthContext";

import UpdatePrice from "./adminTools/updatePrice";
import UpdateAutomation from "./adminTools/updateAutomation";
import UpdateStatus from "./adminTools/updateStatus";
import UpdateDNS from "./adminTools/updateDNS";
import ManagePricingPlans from "./adminTools/managePricingPlans";
export default function Adminhome() {
  // const auth = useAdminAuth();
  // console.log(auth);

  return (
    <div className="mx-0 ml-0 md:ml-8 mt-10 mb-20">
      <div className="flex justify-between">
        <h1 className="font-medium tracking-tighter text-3xl mb-5 ">
          Admin Home
        </h1>
      </div>

      <div className="border rounded-lg shadow-md md:h-[80vh]">
        <div className="mt-4 grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-5 px-4 items-center justify-center">
          <UpdatePrice />
          <ManagePricingPlans />
          <UpdateAutomation />
          <UpdateStatus />
          <UpdateDNS />
        </div>
      </div>
    </div>
  );
}
