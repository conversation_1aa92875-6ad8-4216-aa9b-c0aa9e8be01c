/* eslint-disable @typescript-eslint/no-explicit-any */
import * as React from "react";
import { useState, useEffect } from "react";
// import { DataGrid, GridColDef } from "@mui/x-data-grid";
import { DataGrid, GridColDef, Grid<PERSON>oolbar<PERSON>ontainer, GridToolbarQuickFilter } from '@mui/x-data-grid';

import axios from "@/axois";
import { useAdminAuth } from "@/context/AdminAuthContext";
import { Button } from "@/components/ui/button";
import { toast } from "react-toastify";
import { CopyToClipboard } from "react-copy-to-clipboard";
import { Copy } from "lucide-react";

// import { Badge } from "@/components/ui/badge";
import AddCredits from "./addCredits";
import DeductCredits from "./deductCredits";
import AddLogs from "./addLog";
import moment from 'moment-timezone'
const capitalizeFirstLetter = (string: string) => {
  return string.charAt(0).toUpperCase() + string.slice(1);
};

interface RowData {
  UserID: string;
  companyName: string;
  credits: number;
  email: string;
  location: string;
  name: string;
  phoneNumber: string;
  apiKey: string;
}

export default function DataTable() {
  const auth = useAdminAuth();
  // console.log(auth.token);
  const [rows, setRows] = useState<RowData[]>([]);

  const columns: GridColDef[] = [
    {
      field: 'date',
      headerName: 'Date (dd-mm-yy)',
      width: 160,
      renderCell: data => {
        const utcTime = data.value // assuming this is in 'YYYY-MM-DD HH:mm:ss.SSS' format
        // Parse the UTC time directly
        const momentUTC = moment.utc(utcTime, 'YYYY-MM-DD HH:mm:ss.SSS')
        // Convert IST time to the user's local timezone
        const userLocalTime = momentUTC.tz(moment.tz.guess())
        // Format the time in a readable format (optional)
        const formattedTime = userLocalTime.format('DD-MM-YY HH:mm z')
  
        return formattedTime
      },
    },
    {
      field: "name",
      headerName: "Name",
      width: 130,
      renderCell: (data) => <div>{capitalizeFirstLetter(data.value)}</div>,
    },
    {
      field: "UserID",
      headerName: "User ID",
      width: 200,
      renderCell: (data) => {
        return <div>{data.value}</div>;
      },
    },
    {
      field: "phoneNumber",
      headerName: "Phone Number",
      width: 120,
      renderCell: (data) => <div>{data.value}</div>,
    },
    {
      field: "companyName",
      headerName: "Company Name",
      width: 150,
      renderCell: (data) => <div>{capitalizeFirstLetter(data.value)}</div>,
    },
    {
      field: "credits",
      headerName: "Credits",
      type: "number",
      align: "left",
      headerAlign: "left",
      width: 70,
      renderCell: (data) => <div>{data.value}</div>,
    },
    {
      field: "email",
      headerName: "Email",
      width: 240,
      renderCell: (data) => <div>{data.value}</div>,
    },
    {
      field: "location",
      headerName: "Location",
      width: 110,
      renderCell: (data) => <div>{capitalizeFirstLetter(data.value)}</div>,
    },
    {
      field: "apikey",
      headerName: "Generate API Key",
      width: 170,
      renderCell: (data) => {
        return (
          <div>
            {data.value ? (
              <div className="flex items-center w-full justify-between">
                <div className=" w-full truncate">{data.value}</div>
                <CopyToClipboard text={data.value}>
                  <Button
                    onClick={() => toast.success("ApiKey copied to clipboard")}
                    className="bg-transparent  text-black hover:bg-black/10 p-2 h-fit"
                  >
                    <Copy />
                  </Button>
                </CopyToClipboard>
              </div>
            ) : (
              <Button onClick={() => GenerateAPIKey(data.row.UserID)}>
                Generate API Key
              </Button>
            )}
          </div>
        );
      },
    },
    {
      field: "Addcredits",
      headerName: "Add Credits",
      width: 140,
      renderCell: (data) => {
        return (
          <div>
            <AddCredits
              userID={data.row.UserID}
              onCreditsUpdate={getAllUser}
            />
            ,
          </div>
        );
      },
    },
    {
      field: "Deductcredits",
      headerName: "Deduct Credits",
      width: 140,
      renderCell: (data) => {
        return (
          <div>
            <DeductCredits
              userID={data.row.UserID}
              onCreditsUpdate={getAllUser}
            />
            ,
          </div>
        );
      },
    },
    {
      field: "updateApikey",
      headerName: "Revoke API Key",
      width: 150,
      renderCell: (data) => {
        return (
          <div>
            {/* {data.value ? (
              data.value
            ) : ( */}
            <Button
              onClick={() => RevokeAPIKey(data.row.UserID)}
              disabled={!data.row.apikey}
            >
              Revoke API Key
            </Button>
            {/* )} */}
          </div>
        );
      },
    },
    {
      field: "addLog",
      headerName: "Add Log",
      width: 150,
      renderCell: (data) => {
        return <div><AddLogs userID={data.row.UserID} onLogUpdate={getAllUser} /></div>;
      },
    },

  ];

  useEffect(() => {
    getAllUser();
  }, []);

  const getAllUser = async () => {
    try {
      const response = await axios.get("/api/admin/getAllUsers", {
        headers: {
          Authorization: `Bearer ${auth.token}`,
        },
      });
      if (response.status === 200) {
        setRows(response.data.resp);
      }
    } catch (e: any) {
      console.log(e);
    }
  };

  const GenerateAPIKey = async (userID: string) => {
    try {
      const response = await axios.post(
        "/api/admin/generateAPIkey",
        {
          userID,
        },
        {
          headers: {
            Authorization: "Bearer " + auth.token,
          },
        }
      );
      if (response.status === 200) {
        toast.success("API Key Generated");
        // After generating the API key, fetch all users to refresh the table
        getAllUser(); // This will refresh the data in the table with the new API key
      }
    } catch (e: any) {
      console.log(e);
      toast.error(e.response.data.error);
    }
  };

  const RevokeAPIKey = async (userID: string) => {
    try {
      const response = await axios.post(
        "/api/admin/revokeAPIkey",
        {
          userID,
        },
        {
          headers: {
            Authorization: "Bearer " + auth.token,
          },
        }
      );
      if (response.status === 200) {
        toast.success("API Key Revoked");
        // After generating the API key, fetch all users to refresh the table
        getAllUser(); // This will refresh the data in the table with the new API key
      }
    } catch (e: any) {
      console.log(e);
      toast.error(e.response.data.error);
    }
  };

  return (
    <div>
      <DataGrid
        checkboxSelection={false}
        rows={rows}
        columns={columns}
        getRowId={(row) => row.UserID} // Specify custom id for each row
        initialState={{
          pagination: {
            paginationModel: { page: 0, pageSize: 10 },
          },
        }}
        pageSizeOptions={[5, 10]}
        slots={{ toolbar: CustomToolbar }} // Use custom toolbar
        slotProps={{
          toolbar: {
            showQuickFilter: true,
            disableExport: true,
          },
        }}
        disableColumnFilter
        disableColumnSelector
        disableDensitySelector
      />
    </div>
  );
}

// Custom toolbar without export functionality
const CustomToolbar = () => (
  <GridToolbarContainer>
    <GridToolbarQuickFilter
      sx={{
        borderRadius: 2,
        padding: "5px",
        minWidth: "500px",
      }}
    />
  </GridToolbarContainer>
);
