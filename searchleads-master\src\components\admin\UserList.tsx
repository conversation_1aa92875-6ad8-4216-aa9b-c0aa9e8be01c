/* eslint-disable @typescript-eslint/no-explicit-any */
import React from "react";
// import { useAdminAuth } from "@/context/AdminAuthContext";

import DataTable from "./userList/dataTable";

export default function Users() {
  // const auth = useAdminAuth();
  // console.log(auth.token)

  return (
    <div className="mx-0 ml-0 md:ml-8 mt-10 mb-20">
      <div className="flex justify-between">
        <h1 className="font-medium tracking-tighter text-3xl mb-5 ">Users</h1>
      </div>

      <div className="border rounded-lg shadow-md h-full">
        <DataTable />
      </div>
    </div>
  );
}
