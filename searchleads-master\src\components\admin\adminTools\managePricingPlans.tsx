import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { toast } from "sonner";
import axios from "@/axois";
import { useAdminAuth } from "@/context/AdminAuthContext";
import { BiDollar } from "react-icons/bi";

interface PricingPlan {
  id: string;
  name: string;
  type: string;
  price_per_10k_credits: number;
  description: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export default function ManagePricingPlans() {
  const auth = useAdminAuth();
  const [plans, setPlans] = useState<PricingPlan[]>([]);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [editingPlan, setEditingPlan] = useState<PricingPlan | null>(null);
  
  const [formData, setFormData] = useState({
    name: "",
    type: "payasyougo",
    price_per_10k_credits: "",
    description: "",
    is_active: true,
  });

  useEffect(() => {
    fetchPricingPlans();
  }, []);

  const fetchPricingPlans = async () => {
    try {
      setIsLoading(true);
      const response = await axios.get("/api/admin/getPricingPlans", {
        headers: {
          Authorization: `Bearer ${auth.token}`,
        },
      });
      if (response.status === 200) {
        setPlans(response.data.plans || []);
      }
    } catch (error: any) {
      console.error(error);
      toast.error("Failed to fetch pricing plans");
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = async () => {
    try {
      setIsLoading(true);
      const endpoint = editingPlan 
        ? `/api/admin/updatePricingPlan/${editingPlan.id}`
        : "/api/admin/createPricingPlan";
      
      const method = editingPlan ? "put" : "post";
      
      const response = await axios[method](
        endpoint,
        {
          ...formData,
          price_per_10k_credits: parseFloat(formData.price_per_10k_credits),
        },
        {
          headers: {
            Authorization: `Bearer ${auth.token}`,
          },
        }
      );

      if (response.status === 200) {
        toast.success(editingPlan ? "Plan updated successfully" : "Plan created successfully");
        setIsDialogOpen(false);
        resetForm();
        fetchPricingPlans();
      }
    } catch (error: any) {
      console.error(error);
      toast.error(error.response?.data?.error || "Failed to save pricing plan");
    } finally {
      setIsLoading(false);
    }
  };

  const togglePlanStatus = async (planId: string, isActive: boolean) => {
    try {
      const response = await axios.put(
        `/api/admin/togglePricingPlan/${planId}`,
        { is_active: isActive },
        {
          headers: {
            Authorization: `Bearer ${auth.token}`,
          },
        }
      );

      if (response.status === 200) {
        toast.success("Plan status updated");
        fetchPricingPlans();
      }
    } catch (error: any) {
      console.error(error);
      toast.error("Failed to update plan status");
    }
  };

  const resetForm = () => {
    setFormData({
      name: "",
      type: "payasyougo",
      price_per_10k_credits: "",
      description: "",
      is_active: true,
    });
    setEditingPlan(null);
  };

  const openEditDialog = (plan: PricingPlan) => {
    setEditingPlan(plan);
    setFormData({
      name: plan.name,
      type: plan.type,
      price_per_10k_credits: plan.price_per_10k_credits.toString(),
      description: plan.description,
      is_active: plan.is_active,
    });
    setIsDialogOpen(true);
  };

  const openCreateDialog = () => {
    resetForm();
    setIsDialogOpen(true);
  };

  return (
    <div className="mb-2 flex flex-col justify-center items-center">
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogTrigger asChild>
          <Button variant="outline" className="w-fit h-fit p-6" onClick={openCreateDialog}>
            <div className="flex justify-center items-center">
              <BiDollar className="mr-4 w-14 h-14 md:w-20 md:h-20" />
              <div className="flex flex-col justify-center items-center">
                <p className="text-base md:text-xl font-medium">Pricing Plans</p>
                <p className="text-sm md:text-base text-gray-600">Manage subscription tiers</p>
              </div>
            </div>
          </Button>
        </DialogTrigger>
        <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {editingPlan ? "Edit Pricing Plan" : "Manage Pricing Plans"}
            </DialogTitle>
            <DialogDescription>
              {editingPlan ? "Update the pricing plan details" : "View and manage pricing plans"}
            </DialogDescription>
          </DialogHeader>
          
          {!editingPlan && (
            <div className="mb-4">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium">Current Plans</h3>
                <Button onClick={openCreateDialog} size="sm">
                  Add New Plan
                </Button>
              </div>
              
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Price</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {plans.map((plan) => (
                    <TableRow key={plan.id}>
                      <TableCell className="font-medium">{plan.name}</TableCell>
                      <TableCell>
                        <Badge variant={plan.type === "subscription" ? "default" : "secondary"}>
                          {plan.type}
                        </Badge>
                      </TableCell>
                      <TableCell>${plan.price_per_10k_credits}/10K</TableCell>
                      <TableCell>
                        <Switch
                          checked={plan.is_active}
                          onCheckedChange={(checked) => togglePlanStatus(plan.id, checked)}
                        />
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => openEditDialog(plan)}
                        >
                          Edit
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}

          {editingPlan && (
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <label className="text-right">Name</label>
                <Input
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <label className="text-right">Type</label>
                <select
                  value={formData.type}
                  onChange={(e) => setFormData({ ...formData, type: e.target.value })}
                  className="col-span-3 p-2 border rounded"
                >
                  <option value="payasyougo">Pay-as-you-go</option>
                  <option value="subscription">Subscription</option>
                </select>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <label className="text-right">Price per 10K</label>
                <Input
                  type="number"
                  step="0.01"
                  value={formData.price_per_10k_credits}
                  onChange={(e) => setFormData({ ...formData, price_per_10k_credits: e.target.value })}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <label className="text-right">Description</label>
                <Input
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <label className="text-right">Active</label>
                <Switch
                  checked={formData.is_active}
                  onCheckedChange={(checked) => setFormData({ ...formData, is_active: checked })}
                />
              </div>
            </div>
          )}

          {editingPlan && (
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleSubmit} disabled={isLoading}>
                {isLoading ? "Saving..." : "Save Changes"}
              </Button>
            </DialogFooter>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
