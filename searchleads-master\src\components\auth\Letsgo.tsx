import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { BrowserRouter, Routes, Route, useSearchParams } from 'react-router-dom';
import { Auth } from '@supabase/auth-ui-react'
import { ThemeSupa } from '@supabase/auth-ui-shared'
import { supabase } from '@/lib/supabase'
import companyLogo from '@/assets/logo.png'

function Letsgo() {
  const [isLogin, setIsLogin] = useState(true)
  const [loading, setLoading] = useState(false)
  const [isResetPassword, setIsResetPassword] = useState(false)
  const [email, setEmail] = useState('')
  const [error, setError] = useState<any>(null)
  const [data, setData] = useState<any>(null)

  const [searchParams] = useSearchParams();
  const state = searchParams.get('state');
  console.log('state', state)

  useEffect (() => {
    if (state == 'signup') {
      setIsLogin(false)
    } else {
      setIsLogin(true)
    }
  },[state])
  
  const slideVariants = {
    left: { x: '0%' },
    right: { x: '100%' },
  }

  const formVariants = {
    hidden: { opacity: 0, x: -20 },
    visible: { opacity: 1, x: 0 },
  }

  // Function to reset user password
  const resetPasswordForEmail = async (email: string) => {
    setLoading(true)
    // console.log(email)
    setData(null)
    setError(null)
    const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
    })

    if (error) {
      setError(error)
      console.log('Error: ', error)
      setLoading(false)
      return
    }
    setData('Password reset link sent to your email')
    setLoading(false)
    setEmail('')
  }

  // async function handleOAuthLoginGithub() {
  //   const { data, error } = await supabase.auth.signInWithOAuth({
  //     provider: 'google',
  //     options: {
  //       redirectTo: `${window.location.origin}`,
  //     }
  //   })
  //   console.log(data)
  //   if (error) console.error('Error: ', error.message)

  //   console.log(data)
  // }

  return (
    <>
      <div className='min-h-screen w-full bg-gradient-to-br from-blue-50 to-indigo-100 flex-col items-center'>
        <div className='flex items-center w-full space-x-1 py-4 px-16 absolute top-0'>
          <img
            draggable={false}
            src={companyLogo}
            alt='Company Logo'
            width={40}
            height={40}
          />
          <p className='text-2xl font-semibold text-black bg-clip-text'>
            Searchleads
          </p>
        </div>
        <div className='w-full h-full flex-1 flex justify-center items-center'>
          <div className='w-full max-w-[900px] h-[500px] bg-white rounded-2xl shadow-xl overflow-hidden relative hidden md:block'>
            <div className='grid grid-cols-2 h-full'>
              <motion.div
                className='absolute w-1/2 h-full bg-[#F28100] z-10'
                animate={isLogin ? 'left' : 'right'}
                variants={slideVariants}
                transition={{ type: 'spring', stiffness: 300, damping: 30 }}
              >
                <AnimatePresence mode='wait'>
                  <motion.div
                    key={isLogin ? 'login' : 'signup'}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.2 }}
                    className='p-12 text-white flex flex-col justify-center h-full'
                  >
                    {isLogin ? (
                      <>
                        <h2 className='text-4xl font-bold mb-4'>
                          Hello, Welcome!
                        </h2>
                        <p className='mb-8'>Don&apos;t have an account?</p>
                        <button
                          onClick={() => setIsLogin(false)}
                          className='px-8 py-2 border-2 border-white rounded-full text-white hover:bg-white hover:text-[#F28100] transition-colors w-fit'
                        >
                          Register
                        </button>
                      </>
                    ) : (
                      <>
                        <h2 className='text-4xl font-bold mb-4'>
                          Welcome Back!
                        </h2>
                        <p className='mb-8'>Already have an account?</p>
                        <button
                          onClick={() => setIsLogin(true)}
                          className='px-8 py-2 border-2 border-white rounded-full text-white hover:bg-white hover:text-[#F28100] transition-colors w-fit'
                        >
                          Login
                        </button>
                      </>
                    )}
                  </motion.div>
                </AnimatePresence>
              </motion.div>

              <div className='col-start-2'>
                <AnimatePresence mode='wait'>
                  <motion.div
                    key={isResetPassword ? 'reset-password-form' : 'login-form'}
                    variants={formVariants}
                    initial='hidden'
                    animate='visible'
                    exit='hidden'
                    className='p-12 flex flex-col justify-center h-full'
                  >
                    {isResetPassword ? (
                      <div>
                        <h2 className='text-3xl font-bold mb-8'>
                          Forgot Password
                        </h2>
                        <div className='space-y-4'>
                          <input
                            type='email'
                            placeholder='Email'
                            value={email}
                            onChange={e => setEmail(e.target.value)}
                            className='w-full px-4 py-3 rounded-lg bg-gray-100 focus:outline-none focus:ring-2 focus:ring-[#F28100]'
                          />
                          <button
                            onClick={() => resetPasswordForEmail(email)}
                            disabled={loading}
                            className='w-full py-3 bg-[#F28100] text-white rounded-lg hover:bg-[#F28100]/85 transition-colors'
                          >
                            {loading
                              ? 'Sending...'
                              : 'Send Password Reset Link'}
                          </button>
                          <button
                            onClick={() => setIsResetPassword(false)}
                            className='w-full py-3 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors mt-4'
                          >
                            Back to Login
                          </button>
                        </div>
                        {data && (
                          <div className='py-2 px-4 w-full flex justify-center bg-green-100 rounded-lg mt-4'>
                            <p className='text-green-500'>{data}</p>
                          </div>
                        )}
                        {error && (
                          <div className='py-2 px-4 w-full flex justify-center bg-red-100 rounded-lg mt-4'>
                            <p className='text-red-500'>{error.message}</p>
                          </div>
                        )}
                      </div>
                    ) : (
                      <div>
                        <h2 className='text-3xl font-bold mb-3'>Login</h2>
                        <Auth
                          view='sign_in'
                          showLinks={false}
                          supabaseClient={supabase}
                          redirectTo={`${window.location.origin}`}
                          providers={['google']}
                          appearance={{
                            style: {
                              label: { display: 'none' },
                            },
                            extend: false,
                            className: {
                              anchor: 'text-[#F28100]',
                              button:
                                'flex items-center justify-center gap-2 my-2 w-full py-3 bg-[#F28100] text-white rounded-lg hover:bg-[#F28100]/85 transition-colors',
                              input:
                                'w-full px-4 py-3 rounded-lg bg-gray-100 my-2 focus:outline-none focus:ring-2 focus:ring-[#F28100]',
                              divider:
                                'mt-4 mb-2 h-[0.1rem] w-full bg-[#eaeaea] grid',
                              message: 'w-full flex justify-center py-1',
                            },
                          }}
                          localization={{
                            variables: {
                              sign_in: {
                                email_input_placeholder: 'Email',
                                password_input_placeholder: 'Password',
                                button_label: 'Login',
                              },
                            },
                          }}
                        />
                        {/* <button
                          onClick={handleOAuthLoginGithub}
                          className='w-full py-3 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors mt-2'
                        >
                          Login with Google
                        </button> */}
                        <button
                          onClick={() => setIsResetPassword(true)}
                          className='w-full py-3 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors mt-2'
                        >
                          Forgot Password?
                        </button>
                      </div>
                    )}
                  </motion.div>
                </AnimatePresence>
              </div>

              <div className='col-start-1 row-start-1'>
                <AnimatePresence mode='wait'>
                  <motion.div
                    key='register-form'
                    variants={formVariants}
                    initial='hidden'
                    animate='visible'
                    exit='hidden'
                    className='p-12 flex flex-col justify-center h-full'
                  >
                    <h2 className='text-3xl font-bold mb-3 '>Registration</h2>
                    <Auth
                      view='sign_up'
                      showLinks={false}
                      supabaseClient={supabase}
                      providers={['google']}
                      redirectTo={`${window.location.origin}`}
                      appearance={{
                        style: {
                          label: { display: 'none' },
                        },
                        // to override original styles, set this to true
                        extend: false,
                        // custom classes
                        className: {
                          anchor: 'text-[#F28100]',
                          button:
                            'flex items-center justify-center gap-2 my-2 w-full py-3 bg-[#F28100] text-white rounded-lg hover:bg-[#F28100]/85 transition-colors',
                          input:
                            'w-full px-4 py-3 rounded-lg bg-gray-100 my-2 focus:outline-none focus:ring-2 focus:ring-[#F28100]',
                          divider:
                            'mt-4 mb-2 h-[0.1rem] w-full bg-[#eaeaea] grid',
                          message: 'w-full flex justify-center pt-3',
                        },
                      }}
                      localization={{
                        variables: {
                          sign_up: {
                            email_input_placeholder: 'Email',
                            password_input_placeholder: 'Password',
                            button_label: 'Register',
                          },
                        },
                      }}
                    />
                  </motion.div>
                </AnimatePresence>
              </div>
            </div>
          </div>

          <div className='p-10 max-w-lg bg-white rounded-2xl shadow-xl w-full overflow-auto md:hidden'>
            <Auth
              // view='sign_up'
              // showLinks={false}
              supabaseClient={supabase}
              providers={['google']}
              redirectTo={`${window.location.origin}`}
              appearance={{ theme: ThemeSupa }}
            />
          </div>
        </div>
      </div>
    </>
  )
}

export default Letsgo
