/* eslint-disable @typescript-eslint/no-explicit-any */
import { Auth } from "@supabase/auth-ui-react";
import { ThemeSupa } from "@supabase/auth-ui-shared";
import { Session } from "@supabase/supabase-js";
import axios from "../axois";
import {
  ReactNode,
  createContext,
  useContext,
  useEffect,
  useState,
} from "react";
import FirstLogin from "../components/auth/FirstLogin";
import PasswordRecoveryModal from "../components/auth/PasswordRecoveryModal"; // Assuming you have a modal component
import { toast } from "react-toastify";

import Letsgo from "@/components/auth/Letsgo";

// const supabase = createClient(import.meta.env.VITE_SUPABASE_URL as string, import.meta.env.VITE_SUPABASE_ANON_KEY as string)
// import { supabase } from '@/lib/supabase'
import { supabase } from "@/lib/supabase";

export type AuthContextType = {
  session: Session | null;
  supabase: typeof supabase;
  // user: User | null;
  user: any | null;
};

export const AuthContext = createContext<AuthContextType>({
  session: null,
  supabase: supabase,
  user: null,
});

// eslint-disable-next-line react-refresh/only-export-components
export const useAuth = () => {
  return useContext(AuthContext);
};

export const AuthProvier = (props: { children: ReactNode }) => {
  const [session, setSession] = useState<Session | null>(null);
  const [isFirstLogin, setIsFirstLogin] = useState<boolean>(false);
  const [user, setUser] = useState<unknown>(null);
  const [loading, setLoading] = useState(true);
  const [showPasswordRecovery, setShowPasswordRecovery] = useState(false);
  const [passwordRecoveryError, setPasswordRecoveryError] = useState<
    string | null
  >(null);

  async function checkFirstLogin() {
    try {
      if (!session?.access_token) return;
      const response = await axios.get("/api/user/getUser", {
        headers: { authorization: `Bearer ${session?.access_token}` },
      });
      if (response.status == 200) {
        setUser(response.data.user);
        // console.log("user",response.data);
      }
    } catch (e: any) {
      if (e.response.status == 404) {
        setIsFirstLogin(true);
      }
      console.log(e);
    }
  }

  useEffect(() => {
    checkFirstLogin();
  }, [session?.access_token]);

  useEffect(() => {
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      setLoading(false);
    });

    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((_event, session) => {
      setSession(session);
      setLoading(false);
    });

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  // Handle password recovery
  useEffect(() => {
    supabase.auth.onAuthStateChange(async (event) => {
      if (event === "PASSWORD_RECOVERY") {
        setShowPasswordRecovery(true);
      }
    });
  }, []);

  const handlePasswordRecovery = async (newPassword: string) => {
    const { data, error } = await supabase.auth.updateUser({
      password: newPassword,
    });

    if (data) {
      setShowPasswordRecovery(false);
      // alert("Password updated successfully!");
      toast.success("Password updated successfully!");
    } else if (error) {
      setPasswordRecoveryError("There was an error updating your password.");
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen grid place-items-center bg-black/10">
        Loading...
      </div>
    );
  }

  if (!session) {
    return (
      <>
        <div className="min-h-screen w-full flex justify-center items-center bg-transparent">
          <div className="bg-white w-full overflow-auto flex">
            <Letsgo />
          </div>
        </div>
        {/* <div className="min-h-screen grid place-items-center bg-white">
                 <div className='p-10 card max-w-lg bg-white w-full overflow-auto'>
                     <Auth supabaseClient={supabase}
                     providers={['google']}
                      appearance={{ theme: ThemeSupa }} />
                 </div>
             </div> */}
      </>
    );
  }

  if (isFirstLogin) {
    return (
      <AuthContext.Provider value={{ session, supabase, user }}>
        <FirstLogin
          isFirstLogin={isFirstLogin}
          setIsFirstLogin={setIsFirstLogin}
        />
      </AuthContext.Provider>
    );
  }

  return (
    <>
      {loading ? (
        <div className="min-h-screen grid place-items-center bg-black/10">
          Loading...
        </div>
      ) : !session ? (
        <div className="min-h-screen grid place-items-center bg-white">
          <div className="p-10 card max-w-lg bg-white w-full overflow-auto">
            <Auth
              supabaseClient={supabase}
              providers={["google"]}
              appearance={{ theme: ThemeSupa }}
            />
          </div>
        </div>
      ) : isFirstLogin ? (
        <AuthContext.Provider value={{ session, supabase, user }}>
          <FirstLogin
            isFirstLogin={isFirstLogin}
            setIsFirstLogin={setIsFirstLogin}
          />
        </AuthContext.Provider>
      ) : (
        <AuthContext.Provider value={{ session, supabase, user }}>
          {props.children}
        </AuthContext.Provider>
      )}
      {showPasswordRecovery && (
        <PasswordRecoveryModal
          onSubmit={handlePasswordRecovery}
          error={passwordRecoveryError}
          onClose={() => setShowPasswordRecovery(false)}
        />
      )}
    </>
  );
};

// Function to request password reset
export async function requestPasswordReset(email: string) {
  const { data, error } = await supabase.auth.resetPasswordForEmail(email);
  if (error) {
    console.error("Error resetting password:", error);
  } else {
    console.log("Password reset email sent:", data);
  }
}
