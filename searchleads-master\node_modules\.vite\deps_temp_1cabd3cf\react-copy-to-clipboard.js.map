{"version": 3, "sources": ["../../toggle-selection/index.js", "../../copy-to-clipboard/index.js", "../../react-copy-to-clipboard/lib/Component.js", "../../react-copy-to-clipboard/lib/index.js"], "sourcesContent": ["\nmodule.exports = function () {\n  var selection = document.getSelection();\n  if (!selection.rangeCount) {\n    return function () {};\n  }\n  var active = document.activeElement;\n\n  var ranges = [];\n  for (var i = 0; i < selection.rangeCount; i++) {\n    ranges.push(selection.getRangeAt(i));\n  }\n\n  switch (active.tagName.toUpperCase()) { // .toUpperCase handles XHTML\n    case 'INPUT':\n    case 'TEXTAREA':\n      active.blur();\n      break;\n\n    default:\n      active = null;\n      break;\n  }\n\n  selection.removeAllRanges();\n  return function () {\n    selection.type === 'Caret' &&\n    selection.removeAllRanges();\n\n    if (!selection.rangeCount) {\n      ranges.forEach(function(range) {\n        selection.addRange(range);\n      });\n    }\n\n    active &&\n    active.focus();\n  };\n};\n", "\"use strict\";\n\nvar deselectCurrent = require(\"toggle-selection\");\n\nvar clipboardToIE11Formatting = {\n  \"text/plain\": \"Text\",\n  \"text/html\": \"Url\",\n  \"default\": \"Text\"\n}\n\nvar defaultMessage = \"Copy to clipboard: #{key}, Enter\";\n\nfunction format(message) {\n  var copyKey = (/mac os x/i.test(navigator.userAgent) ? \"⌘\" : \"Ctrl\") + \"+C\";\n  return message.replace(/#{\\s*key\\s*}/g, copyKey);\n}\n\nfunction copy(text, options) {\n  var debug,\n    message,\n    reselectPrevious,\n    range,\n    selection,\n    mark,\n    success = false;\n  if (!options) {\n    options = {};\n  }\n  debug = options.debug || false;\n  try {\n    reselectPrevious = deselectCurrent();\n\n    range = document.createRange();\n    selection = document.getSelection();\n\n    mark = document.createElement(\"span\");\n    mark.textContent = text;\n    // avoid screen readers from reading out loud the text\n    mark.ariaHidden = \"true\"\n    // reset user styles for span element\n    mark.style.all = \"unset\";\n    // prevents scrolling to the end of the page\n    mark.style.position = \"fixed\";\n    mark.style.top = 0;\n    mark.style.clip = \"rect(0, 0, 0, 0)\";\n    // used to preserve spaces and line breaks\n    mark.style.whiteSpace = \"pre\";\n    // do not inherit user-select (it may be `none`)\n    mark.style.webkitUserSelect = \"text\";\n    mark.style.MozUserSelect = \"text\";\n    mark.style.msUserSelect = \"text\";\n    mark.style.userSelect = \"text\";\n    mark.addEventListener(\"copy\", function(e) {\n      e.stopPropagation();\n      if (options.format) {\n        e.preventDefault();\n        if (typeof e.clipboardData === \"undefined\") { // IE 11\n          debug && console.warn(\"unable to use e.clipboardData\");\n          debug && console.warn(\"trying IE specific stuff\");\n          window.clipboardData.clearData();\n          var format = clipboardToIE11Formatting[options.format] || clipboardToIE11Formatting[\"default\"]\n          window.clipboardData.setData(format, text);\n        } else { // all other browsers\n          e.clipboardData.clearData();\n          e.clipboardData.setData(options.format, text);\n        }\n      }\n      if (options.onCopy) {\n        e.preventDefault();\n        options.onCopy(e.clipboardData);\n      }\n    });\n\n    document.body.appendChild(mark);\n\n    range.selectNodeContents(mark);\n    selection.addRange(range);\n\n    var successful = document.execCommand(\"copy\");\n    if (!successful) {\n      throw new Error(\"copy command was unsuccessful\");\n    }\n    success = true;\n  } catch (err) {\n    debug && console.error(\"unable to copy using execCommand: \", err);\n    debug && console.warn(\"trying IE specific stuff\");\n    try {\n      window.clipboardData.setData(options.format || \"text\", text);\n      options.onCopy && options.onCopy(window.clipboardData);\n      success = true;\n    } catch (err) {\n      debug && console.error(\"unable to copy using clipboardData: \", err);\n      debug && console.error(\"falling back to prompt\");\n      message = format(\"message\" in options ? options.message : defaultMessage);\n      window.prompt(message, text);\n    }\n  } finally {\n    if (selection) {\n      if (typeof selection.removeRange == \"function\") {\n        selection.removeRange(range);\n      } else {\n        selection.removeAllRanges();\n      }\n    }\n\n    if (mark) {\n      document.body.removeChild(mark);\n    }\n    reselectPrevious();\n  }\n\n  return success;\n}\n\nmodule.exports = copy;\n", "\"use strict\";\n\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.CopyToClipboard = void 0;\n\nvar _react = _interopRequireDefault(require(\"react\"));\n\nvar _copyToClipboard = _interopRequireDefault(require(\"copy-to-clipboard\"));\n\nvar _excluded = [\"text\", \"onCopy\", \"options\", \"children\"];\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\n\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function\"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); Object.defineProperty(subClass, \"prototype\", { writable: false }); if (superClass) _setPrototypeOf(subClass, superClass); }\n\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\n\nfunction _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }\n\nfunction _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) { return call; } else if (call !== void 0) { throw new TypeError(\"Derived constructors may only return object or undefined\"); } return _assertThisInitialized(self); }\n\nfunction _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return self; }\n\nfunction _isNativeReflectConstruct() { if (typeof Reflect === \"undefined\" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === \"function\") return true; try { Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); return true; } catch (e) { return false; } }\n\nfunction _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nvar CopyToClipboard = /*#__PURE__*/function (_React$PureComponent) {\n  _inherits(CopyToClipboard, _React$PureComponent);\n\n  var _super = _createSuper(CopyToClipboard);\n\n  function CopyToClipboard() {\n    var _this;\n\n    _classCallCheck(this, CopyToClipboard);\n\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    _this = _super.call.apply(_super, [this].concat(args));\n\n    _defineProperty(_assertThisInitialized(_this), \"onClick\", function (event) {\n      var _this$props = _this.props,\n          text = _this$props.text,\n          onCopy = _this$props.onCopy,\n          children = _this$props.children,\n          options = _this$props.options;\n\n      var elem = _react[\"default\"].Children.only(children);\n\n      var result = (0, _copyToClipboard[\"default\"])(text, options);\n\n      if (onCopy) {\n        onCopy(text, result);\n      } // Bypass onClick if it was present\n\n\n      if (elem && elem.props && typeof elem.props.onClick === 'function') {\n        elem.props.onClick(event);\n      }\n    });\n\n    return _this;\n  }\n\n  _createClass(CopyToClipboard, [{\n    key: \"render\",\n    value: function render() {\n      var _this$props2 = this.props,\n          _text = _this$props2.text,\n          _onCopy = _this$props2.onCopy,\n          _options = _this$props2.options,\n          children = _this$props2.children,\n          props = _objectWithoutProperties(_this$props2, _excluded);\n\n      var elem = _react[\"default\"].Children.only(children);\n\n      return /*#__PURE__*/_react[\"default\"].cloneElement(elem, _objectSpread(_objectSpread({}, props), {}, {\n        onClick: this.onClick\n      }));\n    }\n  }]);\n\n  return CopyToClipboard;\n}(_react[\"default\"].PureComponent);\n\nexports.CopyToClipboard = CopyToClipboard;\n\n_defineProperty(CopyToClipboard, \"defaultProps\", {\n  onCopy: undefined,\n  options: undefined\n});", "\"use strict\";\n\nvar _require = require('./Component'),\n    CopyToClipboard = _require.CopyToClipboard;\n\nCopyToClipboard.CopyToClipboard = CopyToClipboard;\nmodule.exports = CopyToClipboard;"], "mappings": ";;;;;;;;AAAA;AAAA;AACA,WAAO,UAAU,WAAY;AAC3B,UAAI,YAAY,SAAS,aAAa;AACtC,UAAI,CAAC,UAAU,YAAY;AACzB,eAAO,WAAY;AAAA,QAAC;AAAA,MACtB;AACA,UAAI,SAAS,SAAS;AAEtB,UAAI,SAAS,CAAC;AACd,eAAS,IAAI,GAAG,IAAI,UAAU,YAAY,KAAK;AAC7C,eAAO,KAAK,UAAU,WAAW,CAAC,CAAC;AAAA,MACrC;AAEA,cAAQ,OAAO,QAAQ,YAAY,GAAG;AAAA,QACpC,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,KAAK;AACZ;AAAA,QAEF;AACE,mBAAS;AACT;AAAA,MACJ;AAEA,gBAAU,gBAAgB;AAC1B,aAAO,WAAY;AACjB,kBAAU,SAAS,WACnB,UAAU,gBAAgB;AAE1B,YAAI,CAAC,UAAU,YAAY;AACzB,iBAAO,QAAQ,SAAS,OAAO;AAC7B,sBAAU,SAAS,KAAK;AAAA,UAC1B,CAAC;AAAA,QACH;AAEA,kBACA,OAAO,MAAM;AAAA,MACf;AAAA,IACF;AAAA;AAAA;;;ACtCA;AAAA;AAAA;AAEA,QAAI,kBAAkB;AAEtB,QAAI,4BAA4B;AAAA,MAC9B,cAAc;AAAA,MACd,aAAa;AAAA,MACb,WAAW;AAAA,IACb;AAEA,QAAI,iBAAiB;AAErB,aAAS,OAAO,SAAS;AACvB,UAAI,WAAW,YAAY,KAAK,UAAU,SAAS,IAAI,MAAM,UAAU;AACvE,aAAO,QAAQ,QAAQ,iBAAiB,OAAO;AAAA,IACjD;AAEA,aAAS,KAAK,MAAM,SAAS;AAC3B,UAAI,OACF,SACA,kBACA,OACA,WACA,MACA,UAAU;AACZ,UAAI,CAAC,SAAS;AACZ,kBAAU,CAAC;AAAA,MACb;AACA,cAAQ,QAAQ,SAAS;AACzB,UAAI;AACF,2BAAmB,gBAAgB;AAEnC,gBAAQ,SAAS,YAAY;AAC7B,oBAAY,SAAS,aAAa;AAElC,eAAO,SAAS,cAAc,MAAM;AACpC,aAAK,cAAc;AAEnB,aAAK,aAAa;AAElB,aAAK,MAAM,MAAM;AAEjB,aAAK,MAAM,WAAW;AACtB,aAAK,MAAM,MAAM;AACjB,aAAK,MAAM,OAAO;AAElB,aAAK,MAAM,aAAa;AAExB,aAAK,MAAM,mBAAmB;AAC9B,aAAK,MAAM,gBAAgB;AAC3B,aAAK,MAAM,eAAe;AAC1B,aAAK,MAAM,aAAa;AACxB,aAAK,iBAAiB,QAAQ,SAAS,GAAG;AACxC,YAAE,gBAAgB;AAClB,cAAI,QAAQ,QAAQ;AAClB,cAAE,eAAe;AACjB,gBAAI,OAAO,EAAE,kBAAkB,aAAa;AAC1C,uBAAS,QAAQ,KAAK,+BAA+B;AACrD,uBAAS,QAAQ,KAAK,0BAA0B;AAChD,qBAAO,cAAc,UAAU;AAC/B,kBAAIA,UAAS,0BAA0B,QAAQ,MAAM,KAAK,0BAA0B,SAAS;AAC7F,qBAAO,cAAc,QAAQA,SAAQ,IAAI;AAAA,YAC3C,OAAO;AACL,gBAAE,cAAc,UAAU;AAC1B,gBAAE,cAAc,QAAQ,QAAQ,QAAQ,IAAI;AAAA,YAC9C;AAAA,UACF;AACA,cAAI,QAAQ,QAAQ;AAClB,cAAE,eAAe;AACjB,oBAAQ,OAAO,EAAE,aAAa;AAAA,UAChC;AAAA,QACF,CAAC;AAED,iBAAS,KAAK,YAAY,IAAI;AAE9B,cAAM,mBAAmB,IAAI;AAC7B,kBAAU,SAAS,KAAK;AAExB,YAAI,aAAa,SAAS,YAAY,MAAM;AAC5C,YAAI,CAAC,YAAY;AACf,gBAAM,IAAI,MAAM,+BAA+B;AAAA,QACjD;AACA,kBAAU;AAAA,MACZ,SAAS,KAAK;AACZ,iBAAS,QAAQ,MAAM,sCAAsC,GAAG;AAChE,iBAAS,QAAQ,KAAK,0BAA0B;AAChD,YAAI;AACF,iBAAO,cAAc,QAAQ,QAAQ,UAAU,QAAQ,IAAI;AAC3D,kBAAQ,UAAU,QAAQ,OAAO,OAAO,aAAa;AACrD,oBAAU;AAAA,QACZ,SAASC,MAAK;AACZ,mBAAS,QAAQ,MAAM,wCAAwCA,IAAG;AAClE,mBAAS,QAAQ,MAAM,wBAAwB;AAC/C,oBAAU,OAAO,aAAa,UAAU,QAAQ,UAAU,cAAc;AACxE,iBAAO,OAAO,SAAS,IAAI;AAAA,QAC7B;AAAA,MACF,UAAE;AACA,YAAI,WAAW;AACb,cAAI,OAAO,UAAU,eAAe,YAAY;AAC9C,sBAAU,YAAY,KAAK;AAAA,UAC7B,OAAO;AACL,sBAAU,gBAAgB;AAAA,UAC5B;AAAA,QACF;AAEA,YAAI,MAAM;AACR,mBAAS,KAAK,YAAY,IAAI;AAAA,QAChC;AACA,yBAAiB;AAAA,MACnB;AAEA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;AClHjB;AAAA;AAAA;AAEA,aAAS,QAAQ,KAAK;AAAE;AAA2B,aAAO,UAAU,cAAc,OAAO,UAAU,YAAY,OAAO,OAAO,WAAW,SAAUC,MAAK;AAAE,eAAO,OAAOA;AAAA,MAAK,IAAI,SAAUA,MAAK;AAAE,eAAOA,QAAO,cAAc,OAAO,UAAUA,KAAI,gBAAgB,UAAUA,SAAQ,OAAO,YAAY,WAAW,OAAOA;AAAA,MAAK,GAAG,QAAQ,GAAG;AAAA,IAAG;AAE/U,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,kBAAkB;AAE1B,QAAI,SAAS,uBAAuB,eAAgB;AAEpD,QAAI,mBAAmB,uBAAuB,2BAA4B;AAE1E,QAAI,YAAY,CAAC,QAAQ,UAAU,WAAW,UAAU;AAExD,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,WAAW,IAAI;AAAA,IAAG;AAEhG,aAAS,QAAQ,QAAQ,gBAAgB;AAAE,UAAI,OAAO,OAAO,KAAK,MAAM;AAAG,UAAI,OAAO,uBAAuB;AAAE,YAAI,UAAU,OAAO,sBAAsB,MAAM;AAAG,2BAAmB,UAAU,QAAQ,OAAO,SAAU,KAAK;AAAE,iBAAO,OAAO,yBAAyB,QAAQ,GAAG,EAAE;AAAA,QAAY,CAAC,IAAI,KAAK,KAAK,MAAM,MAAM,OAAO;AAAA,MAAG;AAAE,aAAO;AAAA,IAAM;AAEpV,aAAS,cAAc,QAAQ;AAAE,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAAE,YAAI,SAAS,QAAQ,UAAU,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC;AAAG,YAAI,IAAI,QAAQ,OAAO,MAAM,GAAG,IAAE,EAAE,QAAQ,SAAU,KAAK;AAAE,0BAAgB,QAAQ,KAAK,OAAO,GAAG,CAAC;AAAA,QAAG,CAAC,IAAI,OAAO,4BAA4B,OAAO,iBAAiB,QAAQ,OAAO,0BAA0B,MAAM,CAAC,IAAI,QAAQ,OAAO,MAAM,CAAC,EAAE,QAAQ,SAAU,KAAK;AAAE,iBAAO,eAAe,QAAQ,KAAK,OAAO,yBAAyB,QAAQ,GAAG,CAAC;AAAA,QAAG,CAAC;AAAA,MAAG;AAAE,aAAO;AAAA,IAAQ;AAEzf,aAAS,yBAAyB,QAAQ,UAAU;AAAE,UAAI,UAAU,KAAM,QAAO,CAAC;AAAG,UAAI,SAAS,8BAA8B,QAAQ,QAAQ;AAAG,UAAI,KAAK;AAAG,UAAI,OAAO,uBAAuB;AAAE,YAAI,mBAAmB,OAAO,sBAAsB,MAAM;AAAG,aAAK,IAAI,GAAG,IAAI,iBAAiB,QAAQ,KAAK;AAAE,gBAAM,iBAAiB,CAAC;AAAG,cAAI,SAAS,QAAQ,GAAG,KAAK,EAAG;AAAU,cAAI,CAAC,OAAO,UAAU,qBAAqB,KAAK,QAAQ,GAAG,EAAG;AAAU,iBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,QAAG;AAAA,MAAE;AAAE,aAAO;AAAA,IAAQ;AAE3e,aAAS,8BAA8B,QAAQ,UAAU;AAAE,UAAI,UAAU,KAAM,QAAO,CAAC;AAAG,UAAI,SAAS,CAAC;AAAG,UAAI,aAAa,OAAO,KAAK,MAAM;AAAG,UAAI,KAAK;AAAG,WAAK,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAAE,cAAM,WAAW,CAAC;AAAG,YAAI,SAAS,QAAQ,GAAG,KAAK,EAAG;AAAU,eAAO,GAAG,IAAI,OAAO,GAAG;AAAA,MAAG;AAAE,aAAO;AAAA,IAAQ;AAElT,aAAS,gBAAgB,UAAU,aAAa;AAAE,UAAI,EAAE,oBAAoB,cAAc;AAAE,cAAM,IAAI,UAAU,mCAAmC;AAAA,MAAG;AAAA,IAAE;AAExJ,aAAS,kBAAkB,QAAQ,OAAO;AAAE,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAAE,YAAI,aAAa,MAAM,CAAC;AAAG,mBAAW,aAAa,WAAW,cAAc;AAAO,mBAAW,eAAe;AAAM,YAAI,WAAW,WAAY,YAAW,WAAW;AAAM,eAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,MAAG;AAAA,IAAE;AAE5T,aAAS,aAAa,aAAa,YAAY,aAAa;AAAE,UAAI,WAAY,mBAAkB,YAAY,WAAW,UAAU;AAAG,UAAI,YAAa,mBAAkB,aAAa,WAAW;AAAG,aAAO,eAAe,aAAa,aAAa,EAAE,UAAU,MAAM,CAAC;AAAG,aAAO;AAAA,IAAa;AAE5R,aAAS,UAAU,UAAU,YAAY;AAAE,UAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAAE,cAAM,IAAI,UAAU,oDAAoD;AAAA,MAAG;AAAE,eAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW,EAAE,aAAa,EAAE,OAAO,UAAU,UAAU,MAAM,cAAc,KAAK,EAAE,CAAC;AAAG,aAAO,eAAe,UAAU,aAAa,EAAE,UAAU,MAAM,CAAC;AAAG,UAAI,WAAY,iBAAgB,UAAU,UAAU;AAAA,IAAG;AAEnc,aAAS,gBAAgB,GAAG,GAAG;AAAE,wBAAkB,OAAO,kBAAkB,SAASC,iBAAgBC,IAAGC,IAAG;AAAE,QAAAD,GAAE,YAAYC;AAAG,eAAOD;AAAA,MAAG;AAAG,aAAO,gBAAgB,GAAG,CAAC;AAAA,IAAG;AAEzK,aAAS,aAAa,SAAS;AAAE,UAAI,4BAA4B,0BAA0B;AAAG,aAAO,SAAS,uBAAuB;AAAE,YAAI,QAAQ,gBAAgB,OAAO,GAAG;AAAQ,YAAI,2BAA2B;AAAE,cAAI,YAAY,gBAAgB,IAAI,EAAE;AAAa,mBAAS,QAAQ,UAAU,OAAO,WAAW,SAAS;AAAA,QAAG,OAAO;AAAE,mBAAS,MAAM,MAAM,MAAM,SAAS;AAAA,QAAG;AAAE,eAAO,2BAA2B,MAAM,MAAM;AAAA,MAAG;AAAA,IAAG;AAExa,aAAS,2BAA2B,MAAM,MAAM;AAAE,UAAI,SAAS,QAAQ,IAAI,MAAM,YAAY,OAAO,SAAS,aAAa;AAAE,eAAO;AAAA,MAAM,WAAW,SAAS,QAAQ;AAAE,cAAM,IAAI,UAAU,0DAA0D;AAAA,MAAG;AAAE,aAAO,uBAAuB,IAAI;AAAA,IAAG;AAE/R,aAAS,uBAAuB,MAAM;AAAE,UAAI,SAAS,QAAQ;AAAE,cAAM,IAAI,eAAe,2DAA2D;AAAA,MAAG;AAAE,aAAO;AAAA,IAAM;AAErK,aAAS,4BAA4B;AAAE,UAAI,OAAO,YAAY,eAAe,CAAC,QAAQ,UAAW,QAAO;AAAO,UAAI,QAAQ,UAAU,KAAM,QAAO;AAAO,UAAI,OAAO,UAAU,WAAY,QAAO;AAAM,UAAI;AAAE,gBAAQ,UAAU,QAAQ,KAAK,QAAQ,UAAU,SAAS,CAAC,GAAG,WAAY;AAAA,QAAC,CAAC,CAAC;AAAG,eAAO;AAAA,MAAM,SAAS,GAAG;AAAE,eAAO;AAAA,MAAO;AAAA,IAAE;AAExU,aAAS,gBAAgB,GAAG;AAAE,wBAAkB,OAAO,iBAAiB,OAAO,iBAAiB,SAASE,iBAAgBF,IAAG;AAAE,eAAOA,GAAE,aAAa,OAAO,eAAeA,EAAC;AAAA,MAAG;AAAG,aAAO,gBAAgB,CAAC;AAAA,IAAG;AAE5M,aAAS,gBAAgB,KAAK,KAAK,OAAO;AAAE,UAAI,OAAO,KAAK;AAAE,eAAO,eAAe,KAAK,KAAK,EAAE,OAAc,YAAY,MAAM,cAAc,MAAM,UAAU,KAAK,CAAC;AAAA,MAAG,OAAO;AAAE,YAAI,GAAG,IAAI;AAAA,MAAO;AAAE,aAAO;AAAA,IAAK;AAEhN,QAAI,kBAA+B,SAAU,sBAAsB;AACjE,gBAAUG,kBAAiB,oBAAoB;AAE/C,UAAI,SAAS,aAAaA,gBAAe;AAEzC,eAASA,mBAAkB;AACzB,YAAI;AAEJ,wBAAgB,MAAMA,gBAAe;AAErC,iBAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACvF,eAAK,IAAI,IAAI,UAAU,IAAI;AAAA,QAC7B;AAEA,gBAAQ,OAAO,KAAK,MAAM,QAAQ,CAAC,IAAI,EAAE,OAAO,IAAI,CAAC;AAErD,wBAAgB,uBAAuB,KAAK,GAAG,WAAW,SAAU,OAAO;AACzE,cAAI,cAAc,MAAM,OACpB,OAAO,YAAY,MACnB,SAAS,YAAY,QACrB,WAAW,YAAY,UACvB,UAAU,YAAY;AAE1B,cAAI,OAAO,OAAO,SAAS,EAAE,SAAS,KAAK,QAAQ;AAEnD,cAAI,UAAU,GAAG,iBAAiB,SAAS,GAAG,MAAM,OAAO;AAE3D,cAAI,QAAQ;AACV,mBAAO,MAAM,MAAM;AAAA,UACrB;AAGA,cAAI,QAAQ,KAAK,SAAS,OAAO,KAAK,MAAM,YAAY,YAAY;AAClE,iBAAK,MAAM,QAAQ,KAAK;AAAA,UAC1B;AAAA,QACF,CAAC;AAED,eAAO;AAAA,MACT;AAEA,mBAAaA,kBAAiB,CAAC;AAAA,QAC7B,KAAK;AAAA,QACL,OAAO,SAAS,SAAS;AACvB,cAAI,eAAe,KAAK,OACpB,QAAQ,aAAa,MACrB,UAAU,aAAa,QACvB,WAAW,aAAa,SACxB,WAAW,aAAa,UACxB,QAAQ,yBAAyB,cAAc,SAAS;AAE5D,cAAI,OAAO,OAAO,SAAS,EAAE,SAAS,KAAK,QAAQ;AAEnD,iBAAoB,OAAO,SAAS,EAAE,aAAa,MAAM,cAAc,cAAc,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG;AAAA,YACnG,SAAS,KAAK;AAAA,UAChB,CAAC,CAAC;AAAA,QACJ;AAAA,MACF,CAAC,CAAC;AAEF,aAAOA;AAAA,IACT,EAAE,OAAO,SAAS,EAAE,aAAa;AAEjC,YAAQ,kBAAkB;AAE1B,oBAAgB,iBAAiB,gBAAgB;AAAA,MAC/C,QAAQ;AAAA,MACR,SAAS;AAAA,IACX,CAAC;AAAA;AAAA;;;ACjHD;AAAA;AAEA,QAAI,WAAW;AAAf,QACI,kBAAkB,SAAS;AAE/B,oBAAgB,kBAAkB;AAClC,WAAO,UAAU;AAAA;AAAA;", "names": ["format", "err", "obj", "_setPrototypeOf", "o", "p", "_getPrototypeOf", "CopyToClipboard"]}