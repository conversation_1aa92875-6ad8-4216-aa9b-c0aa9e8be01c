import React from "react";
import ReactDOM from "react-dom/client";
import App from "./App.tsx";
import Admin from "./AdminApp.tsx";
import "./index.css";
import { BrowserRouter, Route, Routes } from "react-router-dom";
import { ToastContainer } from "react-toastify";
import { AuthProvier } from "@/context/AuthContext.tsx";
import { AdminAuthProvier } from "@/context/AdminAuthContext.tsx";
import { ModalProvier } from "./context/ModalContext.tsx";
import { DataProvider } from "@/context/DataContext";
import "react-toastify/dist/ReactToastify.css";

ReactDOM.createRoot(document.getElementById("root")!).render(
  <React.StrictMode>
    <BrowserRouter>
      <DataProvider>
        <ModalProvier>
          <ToastContainer></ToastContainer>
          <Routes>
            <Route
              path="/*"
              element={
                <AuthProvier>
                  <App />
                </AuthProvier>
              }
            />
            <Route
              path="/admin/*"
              element={
                <AdminAuthProvier>
                  <Admin />
                </AdminAuthProvier>
              }
            />
          </Routes>
        </ModalProvier>
      </DataProvider>
    </BrowserRouter>
  </React.StrictMode>
);
