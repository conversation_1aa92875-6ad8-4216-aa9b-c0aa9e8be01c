/* eslint-disable @typescript-eslint/no-unused-vars */
"use client";
import { Link } from "react-router-dom";
import { useAuth } from "@/context/AuthContext";
import { useEffect, useState } from "react";
import { useDataContext } from "@/context/DataContext";
import axios from "@/axois";
import { supabase } from "@/lib/supabase";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
  AlertDialogDescription,
} from "@/components/ui/alert-dialog";
import Ellipse from "@/assets/icons/Ellipse.svg";
import Profile from "@/assets/icons/Profile.svg";
import CompanyLogo from "@/assets/logo.png";

export default function Navbar() {
  const auth = useAuth();
  const { globalCredits, setGlobalCredits } = useDataContext();
  const [isOpen, setIsOpen] = useState(false); // Add this line to manage dropdown open state
  const [loadingCredits, isLoadingCredits] = useState(true);

  useEffect(() => {
    const userId = auth.user?.UserID;

    const updateCredits = async () => {
      try {
        isLoadingCredits(true);
        const res = await axios.get("api/user/getCredits", {
          headers: { authorization: `Bearer ${auth.session?.access_token}` },
        });
        console.log("aay res", res.data.credits)
        setGlobalCredits(res.data.credits);
      } catch (error) {
        console.error(error);
      } finally {
        isLoadingCredits(false);
      }
    };

    updateCredits();
  }, [auth.session?.access_token, auth.user?.UserID, setGlobalCredits]);

  useEffect(() => {
    // Set up the real-time subscription
    if (!auth.user?.UserID) return;

    const channel = supabase
      .channel("user-db-changes")
      .on(
        "postgres_changes",
        {
          event: "*", // Listening to INSERT events
          schema: "public",
          table: "User",
          filter: `UserID=eq.${auth.user.UserID}`,
        },
        (payload) => {
          console.log('New insert received:', payload.new)
          // Update the user state with the new row data
          if (
            payload.new &&
            typeof payload.new === "object" &&
            "credits" in payload.new
          ) {
            if (payload.new?.credits !== undefined) {
              setGlobalCredits(payload.new.credits);
            }
          }
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [auth.user?.UserID]);

  return (
    <>
      <nav className="backdrop-blur-md">
        <div className="mx-auto lg:h-14 h-full">
          <div className=" flex justify-between w-full items-center gap-10 px-4 py-2">
            <Link to="/" className="flex items-center space-x-1">
              <img
                draggable={false}
                src={CompanyLogo}
                alt="Company Logo"
                width={40}
                height={40}
                className="md:w-10 md:h-10 h-8 w-8"
              />
              <span className="md:text-xl text-lg font-medium text-black bg-clip-text ">
                Searchleads
              </span>
            </Link>
            <div className="flex justify-between gap-2">
              {loadingCredits ? (
                <div className="bg-white rounded-xl px-3 py-2 flex lg:flex-row flex-col justify-center items-center lg:gap-1">
                  {/* <div className="flex gap-1"> */}
                  <img
                    draggable={false}
                    src={Ellipse}
                    alt="Ellipse"
                    width={20}
                    height={20}
                    className="mr-1 animate-spin"
                  />
                  <p className="text-base font-medium">Loading...</p>
                  {/* </div> */}
                </div>
              ) : (
                <div className="bg-white rounded-xl px-3 py-2 flex lg:flex-row flex-col justify-center items-center lg:gap-1">
                  <p className="text-base font-bold">{globalCredits}</p>
                  <div className="flex gap-1">
                    <p className="text-base font-medium">Credits</p>
                    <img
                      draggable={false}
                      src={Ellipse}
                      alt="Ellipse"
                      width={20}
                      height={20}
                      className="ml-1"
                    />
                  </div>
                </div>
              )}
              <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
                <DropdownMenuTrigger className="bg-transparent border-none focus:outline-none">
                  <div className="cursor-pointer bg-white flex items-center justify-center rounded-xl h-10 w-10 text-black ">
                    <img
                      draggable={false}
                      src={Profile}
                      alt="Ellipse"
                      width={15}
                      height={15}
                      className=""
                      onClick={() => setIsOpen(!isOpen)}
                    />
                  </div>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="">
                  <div className="max-w-xl mx-4 my-2 flex flex-col gap-2">
                    <p className="text-lg text-center">
                      Hi, {auth.user?.name}!
                    </p>
                    <p className="text-base text-gray-600 text-center">
                      {auth.user?.email}
                    </p>

                    <DropdownMenuSeparator />

                    <AlertDialog>
                      <AlertDialogTrigger
                        className="flex justify-center text-white py-[6px] px-2 cursor-pointer w-full rounded-md
                        hover:bg-[radial-gradient(#F28100_0%,#F59E0B_100%)] bg-[#F28100] text-base"
                      >
                        Sign Out
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>
                            Are you sure you want to logout?
                          </AlertDialogTitle>
                          <AlertDialogDescription></AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>No</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={async () => {
                              try {
                                await supabase.auth.signOut({
                                  scope: "local",
                                });
                              } catch (error) {
                                console.error("Sign-out error:", error);
                              } finally {
                                clearCookies();
                              }
                            }}
                          >
                            Yes
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </div>
      </nav>
    </>
  );
}

const clearCookies = () => {
  const cookies = document.cookie.split("; ");
  cookies.forEach((cookie) => {
    const [name] = cookie.split("=");
    document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
  });
};
