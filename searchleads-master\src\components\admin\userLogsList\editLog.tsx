/* eslint-disable @typescript-eslint/no-explicit-any */
import React from "react";
("use client");
import { useAdminAuth } from "@/context/AdminAuthContext";
import { toast } from "react-toastify";
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import axios from "@/axois";

export default function EditLogs({
  logID,
  onLogUpdate,
}: {
  logID: string;
  onLogUpdate: () => void;
}) {
  const auth = useAdminAuth();

  const [isCreditsDialogOpen, setIsCreditsDialogOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);


  const [creditsDeducted, setCreditsDeducted] = useState<number | null>(null);
  const [status, setStatus] = useState<string | null>(null);
  const [searchLink, setSearchLink] = useState<string | null>(null);
  const [resultLink, setResultLink] = useState<string | null>(null);

  // Helper function to check if all fields are filled
  const isFormValid = () => {
    return (
      searchLink &&
      status &&
      resultLink &&
      creditsDeducted !== null &&
      !isNaN(creditsDeducted)
    );
  };

  const handleLogChange = async () => {
    try {
      setIsLoading(true);
      const response = await axios.post(
        "/api/admin/editLogAdmin",
        {
          logID: logID,
          apollo_link: searchLink,
          creditsUsed: creditsDeducted,
          status: status,
          url: resultLink,
        },
        {
          headers: {
            Authorization: "Bearer " + auth.token,
          },
        }
      );
      console.log(response);
      if (response.status === 200) {
        toast.success("Log edited successfully");
        setIsCreditsDialogOpen(false); // Close the dialog on success
        onLogUpdate(); // Invoke the callback after successful update
        setIsLoading(false);

        setSearchLink(null);
        setStatus(null);
        setCreditsDeducted(null);
        setIsCreditsDialogOpen(false);
      }
    } catch (e: any) {
      console.log(e);
      toast.error(e.response.data.error);
      setIsLoading(false);
    }
  };

  return (
    <div className="">
      <Dialog open={isCreditsDialogOpen} onOpenChange={setIsCreditsDialogOpen}>
        <DialogTrigger asChild>
          <Button className="">Edit Log</Button>
        </DialogTrigger>
        <DialogContent className="sm:max-w-[425px]">
          <DialogDescription></DialogDescription>
          <DialogHeader>
            <DialogTitle> Edit Log</DialogTitle>
            <DialogDescription></DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Input
                value={searchLink === null ? "" : searchLink} // Handle null value appropriately
                onChange={(e) => setSearchLink(e.target.value)}
                type="text"
                placeholder="Search Link"
                className="col-span-4"
              />
              <Select
                value={status === null ? "" : status} // Handle null value appropriately
                onValueChange={(e) => setStatus(e)}
                // className="col-span-1"
              >
                <SelectTrigger className=" col-span-4 w-full">
                  <SelectValue placeholder="Select Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Completed">Completed</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="Failed">Failed</SelectItem>
                  <SelectItem value="Cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>
              <Input
                value={creditsDeducted === null ? "" : creditsDeducted} // Handle null value appropriately
                onChange={(e) => setCreditsDeducted(Number(e.target.value))}
                type="number"
                placeholder="Credits Deducted"
                className="col-span-4"
              />
              <Input
                value={resultLink === null ? "" : resultLink} // Handle null value appropriately
                onChange={(e) => setResultLink(e.target.value)}
                type="text"
                placeholder="Result Link"
                className="col-span-4"
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              type="submit"
              disabled={isLoading || !isFormValid()} // Disable button if form is not valid
              onClick={() => {
                handleLogChange();
              }}
              className="w-full"
            >
              {isLoading ? "Saving..." : "Save"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}