import React, { useEffect, useState } from "react";
import { Bar } from "react-chartjs-2";
import { useAdminAuth } from "@/context/AdminAuthContext";
import axios from "@/axois";
import {
  FaChevronLeft,
  FaChevronRight,
  FaCalendarDay,
  FaCalendarAlt,
} from "react-icons/fa";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";
import dayjs from "dayjs";
import isBetween from "dayjs/plugin/isBetween";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";

interface RowData {
  date: string;
  creditsUsed: number;
  leadsEnriched: number;
  userID: string; // New property for userID
}

dayjs.extend(isBetween);
import utc from "dayjs/plugin/utc";
dayjs.extend(utc);

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

export default function CreditsAnalytics() {
  const auth = useAdminAuth();
  const [rows, setRows] = useState<RowData[]>([]);
  const [loading, setLoading] = useState(false);
  const [viewType, setViewType] = useState<"daily" | "monthly">("daily");
  const [startDate, setStartDate] = useState(
    dayjs().subtract(6, "day").format("YYYY-MM-DD")
  );
  const [endDate, setEndDate] = useState(dayjs().format("YYYY-MM-DD"));

  const [startDatePicker, setStartDatePicker] = useState(new Date(startDate));
  const [endDatePicker, setEndDatePicker] = useState(new Date(endDate));

  const getAllCreditsData = async () => {
    try {
      setLoading(true);
      const response = await axios.get("/api/admin/getAllLogs", {
        headers: {
          Authorization: `Bearer ${auth.token}`,
        },
      });
      const data: RowData[] = Array.isArray(response.data.data)
        ? response.data.data
        : [];
      if (response.status === 200) {
        setRows(data);
      }
    } catch (e: any) {
      console.error(e);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    getAllCreditsData();
  }, []);

  const groupDataByDate = (rows: RowData[]) => {
    return rows.reduce(
      (
        acc: {
          credits: Record<string, number>;
          users: Record<string, Set<string>>;
        },
        row
      ) => {
        const date = dayjs(row.date).utc().format("YYYY-MM-DD");
        acc.credits[date] = (acc.credits[date] || 0) + row.creditsUsed;
        acc.users[date] = acc.users[date] || new Set();
        acc.users[date].add(row.userID);
        return acc;
      },
      { credits: {}, users: {} }
    );
  };

  const groupDataByMonth = (rows: RowData[]) => {
    return rows.reduce(
      (
        acc: {
          credits: Record<string, number>;
          users: Record<string, Set<string>>;
        },
        row
      ) => {
        const month = dayjs(row.date).format("YYYY-MM");
        acc.credits[month] = (acc.credits[month] || 0) + row.creditsUsed;
        acc.users[month] = acc.users[month] || new Set();
        acc.users[month].add(row.userID);
        return acc;
      },
      { credits: {}, users: {} }
    );
  };

  const logUniqueUsersCount = (groupedData: {
    users: Record<string, Set<string>>;
  }) => {
    // Object.keys(groupedData.users).forEach((key) => {
    //   console.log(
    //     `Date/Month: ${key}, Unique Users: ${groupedData.users[key].size}`
    //   );
    // });
  };

  const getChartData = (
    start: string,
    end: string,
    groupedData: {
      credits: Record<string, number>;
      users: Record<string, Set<string>>;
    }
  ) => {
    const range = [];
    let current =
      viewType === "daily" ? dayjs(start) : dayjs(start).startOf("month");

    while (
      current.isBefore(dayjs(end).add(1, "day")) // Ensures correct iteration range
    ) {
      const key =
        viewType === "daily"
          ? current.format("YYYY-MM-DD")
          : current.format("YYYY-MM");
      range.push({
        date: key,
        credits: groupedData.credits[key] || 0,
        users: groupedData.users[key]?.size || 0,
      });
      current = current.add(1, viewType === "daily" ? "day" : "month");
    }

    return range;
  };

  const groupedData =
    viewType === "daily" ? groupDataByDate(rows) : groupDataByMonth(rows);

  // Log unique users count
  logUniqueUsersCount(groupedData);

  const chartData = getChartData(startDate, endDate, groupedData);

  const labels = chartData.map((data) =>
    viewType === "daily"
      ? dayjs(data.date).format("MMM D")
      : dayjs(data.date).format("MMM YYYY")
  );

  const handlePrev = () => {
    const newStart =
      viewType === "daily"
        ? dayjs(startDate).subtract(7, "day")
        : dayjs(startDate).subtract(1, "month").startOf("month");
    const newEnd =
      viewType === "daily"
        ? dayjs(endDate).subtract(7, "day")
        : newStart.endOf("month");
    setStartDate(newStart.format("YYYY-MM-DD"));
    setEndDate(newEnd.format("YYYY-MM-DD"));
  };

  const handleNext = () => {
    const newStart =
      viewType === "daily"
        ? dayjs(startDate).add(7, "day")
        : dayjs(startDate).add(1, "month").startOf("month");
    const newEnd =
      viewType === "daily"
        ? dayjs(endDate).add(7, "day")
        : newStart.endOf("month");
    setStartDate(newStart.format("YYYY-MM-DD"));
    setEndDate(newEnd.format("YYYY-MM-DD"));
  };

  const handleViewTypeChange = (view: "daily" | "monthly") => {
    setViewType(view);
    if (view === "daily") {
      setStartDate(dayjs().subtract(6, "day").format("YYYY-MM-DD"));
      setEndDate(dayjs().format("YYYY-MM-DD"));
    } else {
      setStartDate(dayjs().startOf("month").format("YYYY-MM-DD"));
      setEndDate(dayjs().endOf("month").format("YYYY-MM-DD"));
    }
  };

  const handleDateChange = (date: Date, type: "start" | "end") => {
    const formattedDate = dayjs(date).format("YYYY-MM-DD");
    if (type === "start") {
      setStartDate(formattedDate);
      setStartDatePicker(date);
    } else {
      setEndDate(formattedDate);
      setEndDatePicker(date);
    }
  };

  return (
    <div className=" h-full p-2">
      {loading ? (
        <div className="flex justify-center items-center h-[80vh]">
          <p>Loading...</p>
        </div>
      ) : (
        <div className="border rounded-lg h-full p-4 bg-white">
        <div className="mb-4 flex flex-row justify-between">
          <h2 className="text-xl font-semibold text-gray-700 flex items-center gap-2">
              Overall Credits Used <br />  
              ({viewType === "daily" ? "Daily" : "Monthly"}
              )
            </h2>
            <div className="flex gap-4">

            {viewType === "daily" && (
              <div className="flex gap-4">
                <div className="flex flex-col">
                  <label className="font-medium text-sm">Start Date</label>
                  <DatePicker
                    selected={startDatePicker}
                    onChange={(date: Date) => handleDateChange(date, "start")}
                    dateFormat="yyyy-MM-dd"
                    maxDate={new Date()}
                    selectsStart
                    startDate={startDatePicker}
                    endDate={endDatePicker}
                    className="border rounded-md p-2 mt-1"
                  />
                </div>
                <div className="flex flex-col">
                  <label className="font-medium text-sm">End Date</label>
                  <DatePicker
                    selected={endDatePicker}
                    onChange={(date: Date) => handleDateChange(date, "end")}
                    dateFormat="yyyy-MM-dd"
                    minDate={startDatePicker}
                    maxDate={new Date()}
                    selectsEnd
                    startDate={startDatePicker}
                    endDate={endDatePicker}
                    className="border rounded-md p-2 mt-1"
                  />
                </div>
              </div>
            )}
          </div>
          </div>

          <div className="max-w-5xl">
            <Bar
              data={{
                labels,
                datasets: [
                  {
                    label: "Credits Used",
                    data: chartData.map((d) => d.credits),
                    backgroundColor: "rgba(75, 192, 192, 0.6)",
                    borderColor: "rgba(75, 192, 192, 1)",
                    borderWidth: 1,
                  },
                ],
              }}
              options={{
                responsive: true,
                plugins: {
                  legend: {
                    position: "top",
                  },
                //   title: {
                //     display: true,
                //     text: `Credits and Unique Users (${
                //       viewType === "daily" ? "Daily" : "Monthly"
                //     })`,
                //   },
                  tooltip: {
                    callbacks: {
                      label: function (context) {
                        const index = context.dataIndex;
                        // Credits dataset
                        return `Credits Used: ${context.raw}, Users: ${chartData[index].users}`;
                      },
                    },
                  },
                },
              }}
            />
          </div>

          <div className="flex justify-between mt-6">
            <div className="flex gap-4">
              <button
                className={`flex items-center gap-2 px-4 py-2 text-sm font-medium text-white bg-blue-500 hover:bg-blue-600 rounded-lg transition`}
                onClick={() => handleViewTypeChange("daily")}
              >
                <FaCalendarDay /> Daily View
              </button>
              <button
                className={`flex items-center gap-2 px-4 py-2 text-sm font-medium text-white bg-blue-500 hover:bg-blue-600 rounded-lg transition`}
                onClick={() => handleViewTypeChange("monthly")}
              >
                <FaCalendarAlt /> Monthly View
              </button>
            </div>

            <div className="flex gap-4">
              <button
                className="flex items-center gap-2 px-4 py-2 text-sm font-medium text-gray-600 bg-gray-200 hover:bg-gray-300 rounded-lg transition"
                onClick={handlePrev}
              >
                <FaChevronLeft /> Previous
              </button>
              <button
                className="flex items-center gap-2 px-4 py-2 text-sm font-medium text-gray-600 bg-gray-200 hover:bg-gray-300 rounded-lg transition"
                onClick={handleNext}
                disabled={dayjs(endDate).isAfter(dayjs())}
              >
                Next <FaChevronRight />
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
