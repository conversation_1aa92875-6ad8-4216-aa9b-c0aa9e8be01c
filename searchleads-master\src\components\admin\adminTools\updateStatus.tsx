/* eslint-disable @typescript-eslint/no-explicit-any */
import React from "react";
("use client");
import { useAdminAuth } from "@/context/AdminAuthContext";
import { toast } from "react-toastify";
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { FaLink } from "react-icons/fa6";
import axios from "@/axois";

export default function UpdateStatus() {
  const auth = useAdminAuth();

  const [statusLink, setStatusLink] = useState("");
  const [isStatusDialogOpen, setIsStatusDialogOpen] = useState(false);

  const handleStatusLinkChange = async () => {
    try {
      const response = await axios.post(
        "/api/admin/changeStatusLink",
        {
          statusLink,
        },
        {
          headers: {
            Authorization: "Bearer " + auth.token,
          },
        }
      );
      if (response.status === 200) {
        toast.success("Automation Link updated");
        setIsStatusDialogOpen(false); // Close the dialog on success
        setStatusLink("");
      }
    } catch (e: any) {
      console.log(e);
      toast.error(e.response.data.error);
    }
  };

  return (
    <div className="mb-2 flex justify-center items-center">
      <Dialog
        open={isStatusDialogOpen}
        onOpenChange={setIsStatusDialogOpen}
      >
        <DialogTrigger asChild>
          <Button variant="outline" className="w-fit h-fit p-6">
            <div className="flex justify-center items-center">
              <FaLink className="mr-4 w-14 h-14 md:w-20 md:h-20" />
              <div className="flex flex-col justify-center items-center ">
                <p className="text-base md:text-xl font-medium">
                  Change <br />
                  Status Link
                </p>
              </div>
            </div>
          </Button>
        </DialogTrigger>
        <DialogContent className="sm:max-w-[425px]">
          <DialogDescription></DialogDescription>
          <DialogHeader>
            <DialogTitle> Update Status Link</DialogTitle>
            <DialogDescription></DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Input
                value={statusLink}
                onChange={(e) => setStatusLink(e.target.value)}
                type="text"
                placeholder="Status Link"
                className="col-span-4"
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              type="submit"
              onClick={() => {
                handleStatusLinkChange();
              }}
              className="w-full"
            >
              Save
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
