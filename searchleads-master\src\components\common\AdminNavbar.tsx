import { Link } from "react-router-dom";
import { useAuth } from "@/context/AuthContext";
import { useAdminAuth } from "@/context/AdminAuthContext";
import { useModal } from "@/context/ModalContext";
import {
  Search,
  Bolt,
  LogOut,
  TableProperties,
  ChartLine,
  ReceiptText,
} from "lucide-react";

function Navbar() {
  const modal = useModal();
  const auth = useAuth();
  const adminAuth = useAdminAuth();

  const navLinks = [
    {
      name: "Home",
      path: "/admin",
      svg: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          strokeWidth={1.5}
          stroke="currentColor"
          className="w-6 h-6 shrink-0"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            d="m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"
          />
        </svg>
      ),
    },
    {
      name: "Users",
      path: "/admin/user",
      svg: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          strokeWidth={1.5}
          stroke="currentColor"
          className="w-6 h-6 shrink-0"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            d="M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z"
          />
        </svg>
      ),
    },
    {
      name: "User Logs",
      path: "/admin/user-logs",
      svg: <TableProperties className="size-6 shrink-0" />,
    },
    {
      name: "Analytics",
      path: "/admin/analytics",
      svg: <ChartLine className="size-6 shrink-0" />,
    },
    {
      name: "Invoices",
      path: "/admin/invoice-logs",
      svg: <ReceiptText className="size-6 shrink-0" />,
    },
  ];
  return (
    <>
      {/* MOBILE NAVBAR */}
      <div className="group fixed left-0 bottom-0 z-40 bg-white h-16 w-full md:hidden flex justify-between">
        {navLinks.map((item, index) => {
          return (
            <Link
              key={index}
              to={item.path}
              className="flex flex-col justify-center items-center w-full h-full"
            >
              {item.svg}
              <span className="text-xs">{item.name}</span>
            </Link>
          );
        })}
        <div
          onClick={() => {
            modal
              ?.CreateModal(
                "Logout",
                "Are you sure you want to logout?",
                "Yes",
                "No"
              )
              .then((res) => {
                if (res) {
                  auth.supabase.auth.signOut();
                  adminAuth.handleLogout();
                }
              });
          }}
          className="flex flex-col justify-center items-center w-full h-full cursor-pointer"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth={1.5}
            stroke="currentColor"
            className="size-6 shrink-0"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M15.75 9V5.25A2.25 2.25 0 0 0 13.5 3h-6a2.25 2.25 0 0 0-2.25 2.25v13.5A2.25 2.25 0 0 0 7.5 21h6a2.25 2.25 0 0 0 2.25-2.25V15M12 9l-3 3m0 0 3 3m-3-3h12.75"
            />
          </svg>
          <span className="text-xs">Logout</span>
        </div>
      </div>

      {/* DESKTOP NAVBAR */}
      <div className="group fixed left-0 top-0 z-40 bg-text bg-[#091116] text-white hover:bg-black/50 backdrop-blur-md text-background px-2 hover:px-4 py-4 w-16 hover:w-96 duration-300 h-screen hidden md:flex flex-col justify-between items-stretch">
        {/* <div className="flex justify-center items-center duration-300">
          <svg className="size-6 shrink-0" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" d="m3.75 13.5 10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75Z" />
                    </svg>
          <div className="grid grid-cols-[0fr] group-hover:ml-4 group-hover:grid-cols-[1fr] duration-300">
            <span className="overflow-hidden">LOGO</span>
          </div>
        </div> */}
        <div className="flex flex-col">
          {navLinks.map((item, index) => {
            return (
              <Link
                key={index}
                to={item.path}
                className="flex justify-center items-center duration-300 outline outline-transparent outline-1 hover:outline-slate-500/50 p-4 rounded-xl cursor-pointer hover:bg-white/5 active:scale-95"
              >
                {item.svg}
                <div className="grid grid-cols-[0fr] group-hover:ml-4 group-hover:grid-cols-[1fr] duration-300">
                  <span className="overflow-hidden">{item.name}</span>
                </div>
              </Link>
            );
          })}
        </div>
        <div
          onClick={() => {
            modal
              ?.CreateModal(
                "Logout",
                "Are you sure you want to logout?",
                "Yes",
                "No"
              )
              .then((res) => {
                if (res) {
                  auth.supabase.auth.signOut();
                  adminAuth.handleLogout();
                }
              });
          }}
          className="flex justify-center items-center duration-300 outline outline-1 outline-slate-500/50 p-4 rounded-xl cursor-pointer hover:bg-white/5 active:scale-95"
        >
          {/* <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth={1.5}
            stroke="currentColor"
            className="size-6 shrink-0"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M15.75 9V5.25A2.25 2.25 0 0 0 13.5 3h-6a2.25 2.25 0 0 0-2.25 2.25v13.5A2.25 2.25 0 0 0 7.5 21h6a2.25 2.25 0 0 0 2.25-2.25V15M12 9l-3 3m0 0 3 3m-3-3h12.75"
            />
          </svg> */}
          <LogOut className="size-6 shrink-0 rotate-180" />
          <div className="grid grid-cols-[0fr] group-hover:ml-4 group-hover:grid-cols-[1fr] duration-300">
            <span className="overflow-hidden">LOGOUT</span>
          </div>
        </div>
      </div>
    </>
  );
}

export default Navbar;
