/* eslint-disable @typescript-eslint/no-unused-vars */
import { useCallback, useState } from "react";
import { toast } from "react-toastify";
import API from "@/axois";
import axios from "axios";
import { useAuth } from "../../context/AuthContext";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "../ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import "react-phone-number-input/style.css";
import PhoneInput from "react-phone-number-input";
import { set } from "react-datepicker/dist/date_utils";
import companyLogo from "@/assets/logo.png";

export default function FirstLogin(props: {
  isFirstLogin: boolean;
  setIsFirstLogin: (value: boolean) => void;
}) {
  const auth = useAuth();
  const accountSid = import.meta.env.VITE_TWILIO_ACCOUNT_SID!;
  const authToken = import.meta.env.VITE_TWILIO_AUTH_TOKEN!;

  const [loading, setLoading] = useState(false);
  const [fullName, setName] = useState("");
  const [companyName, setCompanyName] = useState("");
  const [phoneNumber, setPhoneNumber] = useState("");
  const [location, setLocation] = useState("");
  const [heardFrom, setHeardFrom] = useState("");

  const [selectedOption, setSelectedOption] = useState("");
  const [otherText, setOtherText] = useState("");

  const handleOptionChange = (value) => {
    if (value === "Other") {
      setSelectedOption("Other");
    } else {
      setSelectedOption(value);
      setHeardFrom(value);
      setOtherText(""); // Clear "Other" text if a different option is selected
    }
  };

  const handleOtherTextChange = (e) => {
    const text = e.target.value;
    setOtherText(text);
    setHeardFrom("Other: " + text);
  };

  const VerifyPhoneNumber = useCallback(
    async (phoneNumber: string) => {
      try {
        const headers = new Headers({
          Authorization: "Basic " + btoa(`${accountSid}:${authToken}`),
        });

        const response = await fetch(
          `https://lookups.twilio.com/v2/PhoneNumbers/${phoneNumber}`,
          {
            method: "GET",
            headers: headers,
          }
        );

        const data = await response.json();
        return data.valid; // Return the valid value
      } catch (error) {
        console.error("Error verifying phone number:", error);
        toast.error("Error verifying phone number");
        return false; // Return false in case of error
      }
    },
    [accountSid, authToken]
  );

  const submit = useCallback(async () => {
    if (fullName == "" || companyName == "" || phoneNumber == "") {
      toast.error("Please fill all fields");
      return;
    }
    setLoading(true);

    // const isValidPhone = await VerifyPhoneNumber(phoneNumber);
    // if (!isValidPhone) {
    //   toast.error("Invalid phone number");
    //   return;
    // }

    try {
      const res = await API.post(
        "api/user/register",
        {
          fullName,
          companyName,
          phoneNumber,
          location,
          heardFrom,
        },
        {
          headers: { authorization: `Bearer ${auth.session?.access_token}` },
        }
      );
      if (res.status == 200) {
        props.setIsFirstLogin(false);
        toast.success("Details submitted successfully!");
        setLoading(false);
        window.location.reload();
      }
    } catch (e) {
      // toast.error("Error submitting details");
      setLoading(false);
      console.log(e);
    }
  }, [
    fullName,
    companyName,
    VerifyPhoneNumber,
    phoneNumber,
    location,
    heardFrom,
    auth.session?.access_token,
    props,
  ]);

  return (
    <>
      <div className="min-h-screen flex  items-center justify-center bg-[#F281000D] ">
        <Card className="w-full max-w-md shadow-xl">
          <CardHeader>
            <div className="flex items-center w-full space-x-2 mb-4">
              <img
                draggable={false}
                src={companyLogo}
                alt="Company Logo"
                width={50}
                height={50}
              />
              <p className="text-4xl font-bold text-black bg-clip-text">
                Searchleads
              </p>
            </div>
            <CardTitle className="text-2xl font-bold text-gray-800">
              Tell us more about you
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div className="space-y-2">
                <label
                  htmlFor="fullName"
                  className="block text-sm font-medium text-gray-700"
                >
                  Name
                </label>
                <Input
                  id="fullName"
                  type="text"
                  value={fullName}
                  onChange={(e) => setName(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-orange-400 focus:border-transparent"
                  placeholder="Your Name"
                />
              </div>

              <div className="space-y-2">
                <label
                  htmlFor="companyName"
                  className="block text-sm font-medium text-gray-700"
                >
                  Company Name
                </label>
                <Input
                  id="companyName"
                  type="text"
                  value={companyName}
                  onChange={(e) => setCompanyName(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-orange-400 focus:border-transparent"
                  placeholder="Company"
                />
              </div>

              <div className="space-y-2">
                <label
                  htmlFor="phoneNumber"
                  className="block text-sm font-medium text-gray-700"
                >
                  Phone Number
                </label>
                <PhoneInput
                  className="border rounded px-2 py-2 focus:outline-none focus:ring-2 focus:ring-red-500"
                  onFocus={(e) => {
                    e.target.style.border = "0px solid";
                    e.target.style.outline = "none";
                  }}
                  international
                  enableSearch={true}
                  value={phoneNumber}
                  onChange={(phone) => setPhoneNumber(phone || "")}
                />
              </div>

              <div className="space-y-2">
                <label
                  htmlFor="location"
                  className="block text-sm font-medium text-gray-700"
                >
                  Location
                </label>
                <Input
                  id="location"
                  type="text"
                  value={location}
                  onChange={(e) => setLocation(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-orange-400 focus:border-transparent"
                  placeholder="Location"
                />
              </div>

              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">
                  How did you hear about us?
                </label>
                <Select
                  value={selectedOption}
                  onValueChange={handleOptionChange}
                >
                  <SelectTrigger className="w-full px-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-orange-400 focus:border-transparent">
                    <SelectValue placeholder="Select an option" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Friend/Colleague">
                      Friend/Colleague
                    </SelectItem>
                    <SelectItem value="Online Search">Online Search</SelectItem>
                    <SelectItem value="Social Media">Social Media</SelectItem>
                    <SelectItem value="Email Campaign">
                      Email Campaign
                    </SelectItem>
                    <SelectItem value="Other">Other</SelectItem>
                  </SelectContent>
                </Select>

                {selectedOption === "Other" && (
                  <div className="mt-4">
                    <Input
                      placeholder="Please specify..."
                      value={otherText}
                      onChange={handleOtherTextChange}
                      className="w-full px-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-orange-400 focus:border-transparent"
                    />
                  </div>
                )}
              </div>

              <Button
                onClick={submit}
                disabled={loading}
                className="w-full bg-orange-400 hover:bg-orange-500 text-white py-2 rounded-lg transition-colors duration-200"
              >
                {loading ? "Submitting" : "Submit"}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </>
  );
}
