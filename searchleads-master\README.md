# Search Leads

## Overview

Search Leads provides verified lead lists, including personal phone numbers and emails, using AI enrichment. Simply provide an Apollo Search URL, and receive a comprehensive lead list within an hour. Enjoy flexible pricing with options for "never-expiring" credits or "pay-as-you-go" plans.


## Table of Contents

- [Installation](#installation)
- [Usage](#usage)


## Installation

To get started with the project, follow these steps:

1. Clone the repository:
    ```sh
    git clone https://github.com/Brainholics/searchleads
    cd search-leads
    ```

2. Install the dependencies:
    ```sh
    npm install
    ```

3. Set up environment variables:
    Create a `.env` file in the root directory and add the necessary environment variables. Refer to `.env.sample` for the required variables.

4. Start the development server:
    ```sh
    npm run dev
    ```

## Usage

### Application Sections

The application is divided into two main sections: **Users** and **Admins**.

### User Features

- **Lead Search**: Users can search for leads using Apollo links.
- **Data Management**: Users can view, filter, and manage their search results.
- **View Data**: Users can view their search results in a Google Sheet.

### Admin Features

- **Update Price**: <PERSON><PERSON> can update and manage pricing information.
- **Update Automation**: <PERSON><PERSON> can configure and manage automation settings.
- **Update Status Link**: Admins can update Status Link.
- **Update DNS**: Admins can change and manage DNS settings.
- **User Management**: Admins can view, manage, and update user data, including credits and API keys.


