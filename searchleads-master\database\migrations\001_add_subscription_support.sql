-- Migration: Add subscription support to the database
-- This migration adds support for subscription-based pricing alongside pay-as-you-go

-- Create pricing_plans table to store different pricing models
CREATE TABLE IF NOT EXISTS pricing_plans (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    type VARCHAR(50) NOT NULL CHECK (type IN ('payasyougo', 'subscription')),
    price_per_10k_credits DECIMAL(10,2) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default pricing plans
INSERT INTO pricing_plans (name, type, price_per_10k_credits, description) VALUES
('Pay-as-you-go', 'payasyougo', 30.00, 'Standard pay-as-you-go pricing'),
('Subscription', 'subscription', 20.00, 'Discounted subscription pricing');

-- Add pricing_model column to existing tables that track payments/invoices
-- Note: Adjust table names based on your actual schema

-- Add to User table if it exists (to track user's preferred pricing model)
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'User') THEN
        ALTER TABLE "User" ADD COLUMN IF NOT EXISTS preferred_pricing_model VARCHAR(50) DEFAULT 'payasyougo';
        ALTER TABLE "User" ADD COLUMN IF NOT EXISTS subscription_status VARCHAR(50) DEFAULT 'inactive';
        ALTER TABLE "User" ADD COLUMN IF NOT EXISTS subscription_start_date TIMESTAMP WITH TIME ZONE;
        ALTER TABLE "User" ADD COLUMN IF NOT EXISTS subscription_end_date TIMESTAMP WITH TIME ZONE;
    END IF;
END $$;

-- Add to payment/invoice related tables (adjust table names as needed)
DO $$ 
BEGIN
    -- Add to payment logs/transactions table
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'PaymentLogs') THEN
        ALTER TABLE "PaymentLogs" ADD COLUMN IF NOT EXISTS pricing_model VARCHAR(50) DEFAULT 'payasyougo';
        ALTER TABLE "PaymentLogs" ADD COLUMN IF NOT EXISTS plan_id UUID REFERENCES pricing_plans(id);
    END IF;
    
    -- Add to invoice/billing table
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'Invoices') THEN
        ALTER TABLE "Invoices" ADD COLUMN IF NOT EXISTS pricing_model VARCHAR(50) DEFAULT 'payasyougo';
        ALTER TABLE "Invoices" ADD COLUMN IF NOT EXISTS plan_id UUID REFERENCES pricing_plans(id);
    END IF;
    
    -- Add to any other relevant tables
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'EnrichmentLogs') THEN
        ALTER TABLE "EnrichmentLogs" ADD COLUMN IF NOT EXISTS pricing_model VARCHAR(50) DEFAULT 'payasyougo';
    END IF;
END $$;

-- Create subscription_history table to track subscription changes
CREATE TABLE IF NOT EXISTS subscription_history (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL,
    plan_id UUID NOT NULL REFERENCES pricing_plans(id),
    status VARCHAR(50) NOT NULL CHECK (status IN ('active', 'inactive', 'cancelled', 'expired')),
    start_date TIMESTAMP WITH TIME ZONE NOT NULL,
    end_date TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_pricing_plans_type ON pricing_plans(type);
CREATE INDEX IF NOT EXISTS idx_pricing_plans_active ON pricing_plans(is_active);
CREATE INDEX IF NOT EXISTS idx_subscription_history_user_id ON subscription_history(user_id);
CREATE INDEX IF NOT EXISTS idx_subscription_history_status ON subscription_history(status);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_pricing_plans_updated_at BEFORE UPDATE ON pricing_plans FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_subscription_history_updated_at BEFORE UPDATE ON subscription_history FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Add RLS (Row Level Security) policies if needed
-- Note: Adjust based on your security requirements

-- Enable RLS on new tables
ALTER TABLE pricing_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscription_history ENABLE ROW LEVEL SECURITY;

-- Create policies for pricing_plans (readable by all authenticated users)
CREATE POLICY "Pricing plans are viewable by authenticated users" ON pricing_plans
    FOR SELECT USING (auth.role() = 'authenticated');

-- Create policies for subscription_history (users can only see their own history)
CREATE POLICY "Users can view their own subscription history" ON subscription_history
    FOR SELECT USING (auth.uid()::text = user_id::text);

CREATE POLICY "Users can insert their own subscription history" ON subscription_history
    FOR INSERT WITH CHECK (auth.uid()::text = user_id::text);

-- Grant necessary permissions
GRANT SELECT ON pricing_plans TO authenticated;
GRANT ALL ON subscription_history TO authenticated;

-- Add comments for documentation
COMMENT ON TABLE pricing_plans IS 'Stores different pricing models and their rates';
COMMENT ON TABLE subscription_history IS 'Tracks user subscription status changes over time';
COMMENT ON COLUMN pricing_plans.type IS 'Type of pricing: payasyougo or subscription';
COMMENT ON COLUMN pricing_plans.price_per_10k_credits IS 'Price for 10,000 credits in USD';
