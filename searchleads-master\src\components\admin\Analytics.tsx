import React, { useEffect, useState } from "react";
import { useAdminAuth } from "@/context/AdminAuthContext";
import axios from "@/axois";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

import UserSignUp from "./analytics/userSignup";
import OverallCreditsUsed from "./analytics/overallCredits";
import UserLeaderboard from "./analytics/userLeaderboard";

export default function Analytics() {
  const auth = useAdminAuth();

  return (
    <div className="mx-0 ml-0 md:ml-8 mt-10 mb-20">
      <div className="flex justify-between">
        <h1 className="font-medium tracking-tighter text-3xl mb-5 ">
          Analytics
        </h1>
      </div>
      <div className="border rounded-lg shadow-md p-2">
        <Tabs defaultValue="user-signup" className="">
          <TabsList>
            <TabsTrigger value="user-signup">User Signup</TabsTrigger>
            <TabsTrigger value="user-leaderboard">User Leaderboard</TabsTrigger>
            <TabsTrigger value="overall-credits-usage">
              Overall Credits Usage
            </TabsTrigger>
          </TabsList>
          <TabsContent value="user-signup">
            <UserSignUp />
          </TabsContent>
          <TabsContent value="user-leaderboard">
            <UserLeaderboard />
          </TabsContent>
          <TabsContent value="overall-credits-usage">
            <OverallCreditsUsed />
          </TabsContent>
        </Tabs>
        {/* 
        
         */}
      </div>
    </div>
  );
}
