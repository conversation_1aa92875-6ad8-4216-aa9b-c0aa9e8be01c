/* eslint-disable react-refresh/only-export-components */
import { ReactNode, createContext, useContext, useEffect, useState } from 'react';
import Login from '@/components/admin/Login';
import axios from '../axois';
import { toast } from 'react-toastify';


export type AdminAuthContextType = {
    token: string | null;
    setToken: (token: string | null) => void;
    handleLogout: () => void;
}

export const AdminAuthContext = createContext<AdminAuthContextType>({
    token: null,
    setToken: () => {},
    handleLogout: () => {}
});

export const useAdminAuth = () => {
    return useContext(AdminAuthContext);
}

export const AdminAuthProvier = (props: { children: ReactNode }) => {
    const [token, setToken] = useState<string | null>(localStorage.getItem('adminToken'));

    useEffect(() => {
        if (token) {
            localStorage.setItem('adminToken', token);
        } else {
            localStorage.removeItem('adminToken');
        }
    }, [token]);

    useEffect(() => {
        if (token) {
            validateToken(token);
        }
    }, [token])

    
    if (!token) {
        return (
            <AdminAuthContext.Provider value={{ token, setToken, handleLogout }}>
                <Login />
            </AdminAuthContext.Provider>
        );
    }

    async function validateToken(token: string): Promise<boolean> {
        try {
            // console.log("token check")
            const response = await axios.post('/api/admin/verifyToken', { token })
            console.log("token check",response)

        } catch (error) {
            console.error('Token validation failed:', error);
            toast.error('Token Expired! Please login again');
            setToken(null);
            handleLogout();
        }
        return false;
    }


    function handleLogout() {
        setToken(null);
    }

    return (
        <AdminAuthContext.Provider value={{ token, setToken, handleLogout }}>
            {props.children}
        </AdminAuthContext.Provider>
    );
}