/* eslint-disable @typescript-eslint/no-explicit-any */
import * as React from "react";
import { useState, useEffect } from "react";
// import { DataGrid, GridColDef } from "@mui/x-data-grid";
import {
  DataGrid,
  GridColDef,
  Grid<PERSON><PERSON>bar<PERSON>ontainer,
  GridToolbarQuickFilter,
} from "@mui/x-data-grid";

import axios from "@/axois";
import { useAdminAuth } from "@/context/AdminAuthContext";
import { Button } from "@/components/ui/button";
import { toast } from "react-toastify";
import { CopyToClipboard } from "react-copy-to-clipboard";
import { Copy } from "lucide-react";

// import { Badge } from "@/components/ui/badge";

import moment from "moment-timezone";
const capitalizeFirstLetter = (string: string) => {
  return string.charAt(0).toUpperCase() + string.slice(1);
};

interface RowData {
  UserID: string;
  email: string;
  name: string;
  credits: number;
  TotalCreditsBought: number;
  TotalCreditsUsed: number;
}

export default function UserLeaderboard() {
  const auth = useAdminAuth();
  // console.log(auth.token);
  const [rows, setRows] = useState<RowData[]>([]);

  const columns: GridColDef[] = [
    {
      field: "ranking",
      headerName: "Rank",
      width: 60,
      align: "center",
      renderCell: (data) => <div>{data.value}</div>,
    },
    {
      field: "name",
      headerName: "Name",
      width: 160,
      renderCell: (data) => <div>{capitalizeFirstLetter(data.value)}</div>,
    },
    {
      field: "UserID",
      headerName: "User ID",
      width: 250,
      renderCell: (data) => {
        return <div>{data.value}</div>;
      },
    },
    {
      field: "email",
      headerName: "Email",
      width: 240,
      renderCell: (data) => <div>{data.value}</div>,
    },
    {
      field: "credits",
      headerName: "Available Credits",
      type: "number",
      align: "center",
      headerAlign: "left",
      width: 140,
      renderCell: (data) => <div>{data.value}</div>,
    },
    {
      field: "TotalCreditsBought",
      headerName: "Total Credits Bought",
      width: 160,
      align: "center",
      renderCell: (data) => <div>{data.value}</div>,
    },
    {
      field: "TotalCreditsUsed",
      headerName: "Total Credits Used",
      align: "center",
      width: 160,
      renderCell: (data) => <div>{data.value}</div>,
    },
  ];

  useEffect(() => {
    getAllUser();
  }, []);

  const getAllUser = async () => {
    try {
      const response = await axios.get("/api/admin/getUsageRanking", {
        headers: {
          Authorization: `Bearer ${auth.token}`,
        },
      });
      if (response.status === 200) {
        const usersWithRank = response.data.ranking.map(
          (user: RowData, index: number) => ({
            ...user,
            ranking: index + 1, // Add the rank (1-based index)
          })
        );
        setRows(usersWithRank); // Update state with the ranked data
        console.log(usersWithRank);
      }
    } catch (e: any) {
      console.log(e);
    }
  };

  return (
    <div className="border rounded-lg h-full p-4 bg-white">
      <h2 className="text-xl font-semibold text-gray-700 flex items-center gap-2 my-3 mb-6">
        Credits Usage Analytics <br /> (Leaderboard)
      </h2>
      <DataGrid
        checkboxSelection={false}
        rows={rows}
        columns={columns}
        getRowId={(row) => row.UserID} // Specify custom id for each row
        initialState={{
          pagination: {
            paginationModel: { page: 0, pageSize: 10 },
          },
        }}
        pageSizeOptions={[5, 10]}
        slots={{ toolbar: CustomToolbar }} // Use custom toolbar
        slotProps={{
          toolbar: {
            showQuickFilter: true,
            disableExport: true,
          },
        }}
        disableColumnFilter
        disableColumnSelector
        disableDensitySelector
      />
    </div>
  );
}

// Custom toolbar without export functionality
const CustomToolbar = () => (
  <GridToolbarContainer>
    <GridToolbarQuickFilter
      sx={{
        borderRadius: 2,
        padding: "5px",
        minWidth: "500px",
      }}
    />
  </GridToolbarContainer>
);
