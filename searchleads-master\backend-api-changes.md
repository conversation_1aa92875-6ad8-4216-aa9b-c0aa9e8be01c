# Backend API Changes for Subscription Support

This document outlines the required backend API changes to support the new subscription pricing model alongside the existing pay-as-you-go model.

## Overview
The frontend now sends a `pricingModel` field in payment requests. The backend needs to be updated to handle both pricing models:
- **Pay-as-you-go**: $30 per 10K credits (existing)
- **Subscription**: $20 per 10K credits (new)

## Required API Endpoint Updates

### 1. Payment Intent Creation
**Endpoint**: `POST /api/payments/createPaymentIntent`

**Updated Request Body**:
```json
{
  "amount": 2000,
  "currency": "usd",
  "costumerID": "cus_...",
  "description": "Payment for SearchLeads Credits (Subscription)",
  "automaticPayment": true,
  "referral": null,
  "credits": "10000",
  "userID": "user-uuid",
  "cientName": "Lakshay",
  "pricingModel": "subscription"  // NEW FIELD
}
```

**Backend Changes Needed**:
```javascript
// Example implementation (adjust based on your backend framework)
app.post('/api/payments/createPaymentIntent', async (req, res) => {
  const { pricingModel, credits, amount, ...otherFields } = req.body;
  
  // Validate pricing model
  if (!['payasyougo', 'subscription'].includes(pricingModel)) {
    return res.status(400).json({ error: 'Invalid pricing model' });
  }
  
  // Get pricing from database
  const pricingPlan = await getPricingPlan(pricingModel);
  
  // Validate amount matches expected price
  const expectedAmount = calculateExpectedAmount(credits, pricingPlan.price_per_10k_credits);
  if (Math.abs(amount - expectedAmount) > 1) { // Allow 1 cent tolerance
    return res.status(400).json({ error: 'Amount mismatch' });
  }
  
  // Create payment intent with Stripe
  const paymentIntent = await stripe.paymentIntents.create({
    amount: amount,
    currency: req.body.currency,
    customer: req.body.costumerID,
    description: req.body.description,
    metadata: {
      pricingModel: pricingModel,
      credits: credits,
      userID: req.body.userID,
      planId: pricingPlan.id
    }
  });
  
  // Store payment record with pricing model
  await storePaymentRecord({
    ...otherFields,
    pricingModel,
    planId: pricingPlan.id,
    paymentIntentId: paymentIntent.id
  });
  
  res.json({ paymentIntent });
});
```

### 2. Invoice Creation
**Endpoint**: `POST /api/billing/createInvoice`

**Updated Request Body**:
```json
{
  "quantity": "10000",
  "unitCost": 0.002,
  "currency": "usd",
  "amountPaid": "20.00",
  "from": "<EMAIL>",
  "creditsRequested": 10000,
  "pricingModel": "subscription"  // NEW FIELD
}
```

**Backend Changes Needed**:
```javascript
app.post('/api/billing/createInvoice', async (req, res) => {
  const { pricingModel, ...invoiceData } = req.body;
  
  // Get pricing plan details
  const pricingPlan = await getPricingPlan(pricingModel);
  
  // Create invoice with pricing model information
  const invoice = await createInvoice({
    ...invoiceData,
    pricingModel,
    planId: pricingPlan.id,
    planName: pricingPlan.name
  });
  
  res.json({ invoice });
});
```

### 3. Get Pricing Plans
**New Endpoint**: `GET /api/pricing/plans`

```javascript
app.get('/api/pricing/plans', async (req, res) => {
  try {
    const plans = await supabase
      .from('pricing_plans')
      .select('*')
      .eq('is_active', true)
      .order('price_per_10k_credits', { ascending: true });
    
    res.json({ plans: plans.data });
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch pricing plans' });
  }
});
```

### 4. Update User Pricing Preference
**New Endpoint**: `POST /api/user/updatePricingPreference`

```javascript
app.post('/api/user/updatePricingPreference', async (req, res) => {
  const { pricingModel } = req.body;
  const userId = req.user.id; // From auth middleware
  
  try {
    await supabase
      .from('User')
      .update({ preferred_pricing_model: pricingModel })
      .eq('UserID', userId);
    
    res.json({ success: true });
  } catch (error) {
    res.status(500).json({ error: 'Failed to update pricing preference' });
  }
});
```

### 5. Get User Pricing Preference
**New Endpoint**: `GET /api/user/pricingPreference`

```javascript
app.get('/api/user/pricingPreference', async (req, res) => {
  const userId = req.user.id; // From auth middleware
  
  try {
    const { data } = await supabase
      .from('User')
      .select('preferred_pricing_model, subscription_status')
      .eq('UserID', userId)
      .single();
    
    res.json({ 
      pricingModel: data.preferred_pricing_model || 'payasyougo',
      subscriptionStatus: data.subscription_status || 'inactive'
    });
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch pricing preference' });
  }
});
```

## Helper Functions

### Calculate Expected Amount
```javascript
function calculateExpectedAmount(credits, pricePerTenK) {
  return Math.round((parseInt(credits) / 10000) * pricePerTenK * 100); // Convert to cents
}
```

### Get Pricing Plan
```javascript
async function getPricingPlan(pricingModel) {
  const { data, error } = await supabase
    .from('pricing_plans')
    .select('*')
    .eq('type', pricingModel)
    .eq('is_active', true)
    .single();
  
  if (error || !data) {
    throw new Error('Pricing plan not found');
  }
  
  return data;
}
```

## Database Queries to Update

### Update existing payment/invoice queries to include pricing model:
```sql
-- When storing payment records
INSERT INTO payment_logs (user_id, amount, credits, pricing_model, plan_id, ...)
VALUES ($1, $2, $3, $4, $5, ...);

-- When querying payment history
SELECT *, pp.name as plan_name, pp.type as pricing_type
FROM payment_logs pl
LEFT JOIN pricing_plans pp ON pl.plan_id = pp.id
WHERE pl.user_id = $1;
```

## Validation Rules

1. **Pricing Model Validation**: Must be either 'payasyougo' or 'subscription'
2. **Amount Validation**: Verify the amount matches the expected price for the selected model
3. **Credit Calculation**: Ensure credit amounts are calculated correctly based on the pricing model
4. **Plan Existence**: Verify the pricing plan exists and is active

## Testing Considerations

1. Test both pricing models work correctly
2. Verify amount calculations are accurate
3. Test edge cases (invalid pricing models, amount mismatches)
4. Ensure backward compatibility with existing pay-as-you-go payments
5. Test currency conversions work with both models

## Migration Notes

- Existing payment records without `pricing_model` should default to 'payasyougo'
- Update any existing analytics/reporting queries to account for pricing models
- Consider adding pricing model filters to admin dashboards
