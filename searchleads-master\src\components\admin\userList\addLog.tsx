/* eslint-disable @typescript-eslint/no-explicit-any */
import React from "react";
("use client");
import { useAdminAuth } from "@/context/AdminAuthContext";
import { toast } from "react-toastify";
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import axios from "@/axois";

export default function AddLogs({
  userID,
  onLogUpdate,
}: {
  userID: string;
  onLogUpdate: () => void;
}) {
  const auth = useAdminAuth();

  const [isCreditsDialogOpen, setIsCreditsDialogOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const [fileName, setFileName] = useState<string | null>(null);
  const [searchLink, setSearchLink] = useState<string | null>(null);
  const [resultLink, setResultLink] = useState<string | null>(null);
  const [status, setStatus] = useState<string | null>(null);
  const [requestedLeads, setRequestedLeads] = useState<number | null>(null);
  const [leadsEnriched, setLeadsEnriched] = useState<number | null>(null);
  const [creditsDeducted, setCreditsDeducted] = useState<number | null>(null);

  // Helper function to check if all fields are filled
  const isFormValid = () => {
    return (
      fileName &&
      searchLink &&
      resultLink &&
      status &&
      requestedLeads !== null &&
      leadsEnriched !== null &&
      creditsDeducted !== null &&
      !isNaN(requestedLeads) &&
      !isNaN(leadsEnriched) &&
      !isNaN(creditsDeducted)
    );
  };

  const handleLogChange = async () => {
    try {
      setIsLoading(true);
      const response = await axios.post(
        "/api/admin/createUserLog",
        {
          userID: userID,
          leadsRequested: requestedLeads,
          leadsEnriched: leadsEnriched,
          apolloLink: searchLink,
          fileName: fileName,
          creditsUsed: creditsDeducted,
          url: resultLink,
          status: status,
        },
        {
          headers: {
            Authorization: "Bearer " + auth.token,
          },
        }
      );
      console.log(response);
      if (response.status === 200) {
        toast.success("Log added successfully");
        setIsCreditsDialogOpen(false); // Close the dialog on success
        onLogUpdate(); // Invoke the callback after successful update
        setIsLoading(false);

        setFileName(null);
        setSearchLink(null);
        setResultLink(null);
        setStatus(null);
        setRequestedLeads(null);
        setLeadsEnriched(null);
        setCreditsDeducted(null);
        setIsCreditsDialogOpen(false);
      }
    } catch (e: any) {
      console.log(e);
      toast.error(e.response.data.error);
      setIsLoading(false);
    }
  };

  return (
    <div className="">
      <Dialog open={isCreditsDialogOpen} onOpenChange={setIsCreditsDialogOpen}>
        <DialogTrigger asChild>
          <Button className="">Add Log</Button>
        </DialogTrigger>
        <DialogContent className="sm:max-w-[425px]">
          <DialogDescription></DialogDescription>
          <DialogHeader>
            <DialogTitle> Add Log</DialogTitle>
            <DialogDescription></DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Input
                value={fileName === null ? "" : fileName} // Handle null value appropriately
                onChange={(e) => setFileName(e.target.value)}
                type="text"
                placeholder="File Name"
                className="col-span-4"
              />
              <Input
                value={searchLink === null ? "" : searchLink} // Handle null value appropriately
                onChange={(e) => setSearchLink(e.target.value)}
                type="text"
                placeholder="Search Link"
                className="col-span-4"
              />
              <Input
                value={resultLink === null ? "" : resultLink} // Handle null value appropriately
                onChange={(e) => setResultLink(e.target.value)}
                type="text"
                placeholder="Result Link"
                className="col-span-4"
              />
              <Select
                value={status === null ? "" : status} // Handle null value appropriately
                onValueChange={(e) => setStatus(e)}
                // className="col-span-1"
              >
                <SelectTrigger className=" col-span-4 w-full">
                  <SelectValue placeholder="Select Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Completed">Completed</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="Failed">Failed</SelectItem>
                  <SelectItem value="Cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>

              <Input
                value={requestedLeads === null ? "" : requestedLeads} // Handle null value appropriately
                onChange={(e) => setRequestedLeads(Number(e.target.value))}
                type="number"
                placeholder="Requested Leads"
                className="col-span-4"
              />
              <Input
                value={leadsEnriched === null ? "" : leadsEnriched} // Handle null value appropriately
                onChange={(e) => setLeadsEnriched(Number(e.target.value))}
                type="number"
                placeholder="Leads Enriched"
                className="col-span-4"
              />
              <Input
                value={creditsDeducted === null ? "" : creditsDeducted} // Handle null value appropriately
                onChange={(e) => setCreditsDeducted(Number(e.target.value))}
                type="number"
                placeholder="Credits Deducted"
                className="col-span-4"
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              type="submit"
              disabled={isLoading || !isFormValid()} // Disable button if form is not valid
              onClick={() => {
                handleLogChange();
              }}
              className="w-full"
            >
              {isLoading ? "Saving..." : "Save"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
