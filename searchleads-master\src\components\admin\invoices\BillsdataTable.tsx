import React, { useEffect, useCallback, useState } from 'react'
import { DataGrid, GridColDef, GridToolbarContainer, GridToolbarQuickFilter } from '@mui/x-data-grid';
import axios from '@/axois'
import { useAdminAuth } from '@/context/AdminAuthContext'
// import { useDataContext } from "@/context/DataContext";
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { toast } from 'react-toastify'
import { supabase } from '@/lib/supabase'

import { ChevronDown, Download, Trash2 } from 'lucide-react'
import moment from 'moment-timezone'

const capitalizeFirstLetter = (string: string) => {
  return string.charAt(0).toUpperCase() + string.slice(1)
}

interface RowData {
  userID: number
  date: string
  BillingID: string
  Url: string
  ServiceName: string
  CreditsRequested: number
}

export default function DataTable() {
  const auth = useAdminAuth()
  const [rows, setRows] = useState<RowData[]>([])
  const [loading, setLoading] = useState(true)
  const getUserData = useCallback(
    async (userID: string) => {
      try {
        const res = await axios.post(
          'api/admin/getUser',
          {
            userID,
          },
          {
            headers: { authorization: `Bearer ${auth.token}` },
          },
        )
        console.log(res.data)
        return res.data.data
      } catch (error) {
        console.error('Error fetching user logs:', error)
      }
    },
    [auth.token],
  )

  const fetchData = useCallback(async () => {
    try {
      setLoading(true)
      const res = await axios.get('api/admin/getAllBills', {
        headers: { authorization: `Bearer ${auth.token}` },
      })
      // console.log(res.data.data[0])
      // const data: RowData[] = Array.isArray(res.data.logs[0]) ? res.data.logs[0] : [];
      // setRows(res.data.data)

      // Fetch additional user data for each row
      const updatedRows = await Promise.all(
        res.data.data.map(async (row: RowData) => {
          const userData = await getUserData(row.userID.toString())
          return {
            ...row,
            username: userData.name, // Add username
            useremails: userData.email, // Add useremails
          }
        }),
      )

      setRows(updatedRows)
      setLoading(false)
      // console.log(updatedRows)
    } catch (error) {
      console.error('Error fetching user logs:', error)
      setLoading(false)
    }
  }, [auth.token])

  useEffect(() => {
    fetchData()
  }, [fetchData])

  const columns: GridColDef[] = [
    {
      field: 'date',
      headerName: 'Date (dd-mm-yy)',
      width: 160,
      renderCell: data => {
        const utcTime = data.value // assuming this is in 'YYYY-MM-DD HH:mm:ss.SSS' format
        // Parse the UTC time directly
        const momentUTC = moment.utc(utcTime, 'YYYY-MM-DD HH:mm:ss.SSS')
        // Convert IST time to the user's local timezone
        const userLocalTime = momentUTC.tz(moment.tz.guess())
        // Format the time in a readable format (optional)
        const formattedTime = userLocalTime.format('DD-MM-YY HH:mm z')

        return formattedTime
      },
    },
    {
      field: 'userID',
      headerName: 'User ID',
      width: 180,
      renderCell: data => {
        return (
          <>
            <div className=''>{data.value}</div>
          </>
        )
      },
    },
    {
      field: 'username',
      headerName: 'User Name',
      width: 150,
      renderCell: data => {
        // const userData = getUserData(data.row.userID)
        return (
          <>
            <div className=''>
              {/* {userData.then(data => data.name)} */}
              {data.value}
            </div>
          </>
        )
      },
    },
    {
      field: 'useremails',
      headerName: 'User Email',
      width: 180,
      renderCell: data => {
        // const userData = getUserData(data.row.userID)
        return (
          <>
            <div className=''>
              {/* {userData.then(data => data.email)} */}
              {data.value}
            </div>
          </>
        )
      },
    },
    {
      field: 'CreditsRequested',
      headerName: 'Credits Bought',
      width: 130,
      renderCell: data => {
        return data.value
      },
    },
    // {
    //   field: 'ServiceName',
    //   headerName: 'Service Name',
    //   width: 180,
    //   headerAlign: 'left',
    //   renderCell: data => <div className='text-left w-full'>{data.value}</div>,
    // },
    {
      field: 'Url',
      headerName: 'Invoice',
      width: 150,
      renderCell: data => {
        return (
          <>
            <div className='h-full flex justify-center items-center'>
              {data?.value ? (
                <a href={data.value} target='_blank'>
                  <Button
                    variant='outline'
                    className='rounded-lg cursor-pointer hover:bg-[#5844B2] hover:text-white'
                  >
                    Preview <ChevronDown className='ml-2 w-3' />
                  </Button>
                </a>
              ) : (
                <Button
                  variant='outline'
                  disabled
                  className='rounded-lg cursor-pointer hover:bg-[#5844B2] hover:text-white'
                >
                  Preview <ChevronDown className='ml-2 w-3' />
                </Button>
              )}
            </div>
          </>
        )
      },
    },
  ]

  useEffect(() => {
    // Set up the real-time subscription
    const changes = supabase
      .channel('db-changes')
      .on(
        'postgres_changes',
        {
          event: '*', // Listening to INSERT events
          schema: 'public',
          table: 'EnrichmentLogs', // Replace with your table name
        },
        payload => {
          // Update the state with the new row data
          setRows(prevRows => {
            // Find the index of the row to update
            const index = prevRows.findIndex(
              row => row.userID === (payload.new as RowData).userID,
            )

            if (index !== -1) {
              // Create a new array with the updated row
              const updatedRows = [...prevRows]
              updatedRows[index] = { ...prevRows[index], ...payload.new }
              return updatedRows
            }

            // If the LogID is not found, return the previous state
            return prevRows
          })

          // Call fetchData to ensure UI is refreshed with the latest data
          fetchData()
        },
      )
      .subscribe()

    // Clean up subscription on component unmount
    return () => {
      supabase.removeChannel(changes)
    }
  }, [])

  return (
    <>
    {loading ? <div className="flex justify-center items-center h-full py-40">Loading...</div> : (
    <div>
      <DataGrid
        checkboxSelection={false}
        rows={rows}
        columns={columns}
        getRowId={row => row.BillingID} // Specify custom id for each row
        initialState={{
          pagination: {
            paginationModel: { page: 0, pageSize: 10 },
          },
        }}
        pageSizeOptions={[5, 10]}
        slots={{ toolbar: CustomToolbar }} // Use custom toolbar
        slotProps={{
          toolbar: {
            showQuickFilter: true,
            disableExport: true,
          },
        }}
        disableColumnFilter
        disableColumnSelector
        disableDensitySelector
      />
    </div>
    )}</>
  )
}

// Custom toolbar without export functionality
const CustomToolbar = () => (
  <GridToolbarContainer>
    <GridToolbarQuickFilter
      sx={{
        borderRadius: 2,
        padding: '5px',
        minWidth: '500px',
      }}
    />
  </GridToolbarContainer>
);
