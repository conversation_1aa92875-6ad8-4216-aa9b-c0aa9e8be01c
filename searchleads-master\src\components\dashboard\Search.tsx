import { useState } from "react";
import axios from "@/axois";
import SearchPng from "../../assets/search.png";
import { Asterisk } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/context/AuthContext";
import { useDataContext } from "@/context/DataContext"; // Import the context
import { Toaster, toast } from "sonner";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTrigger,
  DialogTitle,
} from "@/components/ui/dialog";
import { Link as RouterLink } from "react-router-dom";
import { Input } from "@/components/ui/input";

export default function Search() {
  const [apolloLink, setApolloLink] = useState("");
  const [apolloLinkError, setApolloLinkError] = useState(""); // Add state for Apollo Link error
  const [noOfLeads, setNumberOfLeads] = useState("");
  const [fileName, setFileName] = useState("");
  const [fileNameError, setFileNameError] = useState("");
  const [loading, setLoading] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const { globalCredits } = useDataContext();

  const auth = useAuth();
  // console.log(auth.session?.access_token);
  const { setRefreshData } = useDataContext(); // Use the context

  // const validateApolloLink = (link: string) => {
  //   const validPattern = /https:\/\/app\.apollo\.io\//;
  //   return validPattern.test(link);
  // };

  const validateApolloLink = (link: string): string | null => {
    // Regex to check base URL
    const validPattern = /^https:\/\/app\.apollo\.io\/#\/people/;
    const allowedPattern = /^https:\/\/app\.apollo\.io\/\?utm_campaign=Transactional%3A\+Account\+Activation&utm_medium=email&utm_source=cio#\/people/;
  
    // Allow URLs that match the specific allowed pattern
    if (allowedPattern.test(link) || link === "https://app.apollo.io/?utm_campaign=Transactional%3A+Account+Activation&utm_medium=email&utm_source=cio#/people") {
      return null;
    }
  
    // Error if the link doesn't start with the expected base URL
    if (!validPattern.test(link)) {
      return "Please make sure your URL is a 'people' URL, not 'company' or any other one.";
    }
  
    // Array of identifiers to check in the URL
    const forbiddenIdentifiers = [
      "contactLabelIds",
      "accountLabelIds",
      "notContactLabelIds",
      "andedContactLabelIds",
      "andedAccountLabelIds",
      "notAccountLabelIds",
    ];
  
    // Check if any forbidden identifier is present in the link
    for (const identifier of forbiddenIdentifiers) {
      if (link.includes(identifier)) {
        return "Make sure your search URL does not have any list filters, as we don't support them.";
      }
    }
  
    // If no issues, return null
    return null;
  };
  

  const handleApolloLinkChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setApolloLink(value);

    // Validate the Apollo link and set the appropriate error
    const errorMessage = validateApolloLink(value);
    if (errorMessage) {
      setApolloLinkError(errorMessage); // Set precise error in UI
    } else {
      setApolloLinkError(""); // Clear error if the link is valid
    }
  };

  const handleFileNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    if (value === "") {
      setFileName(value);
      setFileNameError("");
    } else {
      // const isValid = /^[a-zA-Z0-9]+$/.test(value);
      // if (isValid) {
      setFileName(value);
      setFileNameError("");
      // } else {
      //   setFileNameError(
      //     "File name must not contain spaces or special characters."
      //   );
      // }
    }
  };

  // const fetchPrice = async () => {
  //   try {
  //     const response = await axios.get("/api/user/getCost", {
  //       headers: {
  //         Authorization: `Bearer ${auth.session?.access_token}`,
  //       },
  //     });
  //     // console.log("price",response.data.costPerLead);
  //     if (response.status === 200) {
  //       return response.data.costPerLead; // Assuming the API returns the price directly
  //     }
  //     // eslint-disable-next-line @typescript-eslint/no-explicit-any
  //   } catch (error: any) {
  //     console.error(error);
  //     toast.error(error.message);
  //   }
  // };

  const handleSubmit = async (e: { preventDefault: () => void }) => {
    e.preventDefault();
    // const price = await fetchPrice();

    // Check if user has enough credits
    if (Number(noOfLeads) > globalCredits) {
      toast.error(" Insufficient credits! Please buy credits");
      // setIsDialogOpen(true);
      setApolloLink("");
      setNumberOfLeads("");
      setFileName("");
      return; // Exit if there are not enough credits
    }

    if (!apolloLink || !noOfLeads || !fileName) {
      toast(` All fields are required! `, {
        // description: "Please ensure it contains the correct parameters",
        action: {
          label: "Okay",
          onClick: () => console.log("Okay"),
        },
        duration: 5000,
      });
      // alert("All fields are required.");
      return;
    }

    if (validateApolloLink(apolloLink)) {
      toast(` Invalid Apollo Link! `, {
        description: "Please ensure it contains the correct parameters",
        action: {
          label: "Okay",
          onClick: () => console.log("Okay"),
        },
        duration: 5000,
      });
      return;
    }

    setApolloLink("");
    setNumberOfLeads("");
    setFileName("");
    setLoading(true); // Set loading to true when the form is submitted

    try {
      const response = await axios.post(
        "api/service/searchlead",
        {
          apolloLink,
          fileName,
          noOfLeads,
        },
        {
          headers: { authorization: `Bearer ${auth.session?.access_token}` },
        }
      );
      toast(` Form submitted successfully! `, {
        // description: `Time Taken: ${['total time']}`,
        action: {
          label: "Okay",
          onClick: () => console.log("Okay"),
        },
        duration: 5000,
      });
      // alert("Form submitted successfully!");
      // console.log("Response from API:", response.data);
      setRefreshData((prev) => !prev); // Trigger data refresh
    } catch (error) {
      console.error("Error submitting form:", error);
      if (error instanceof Error) {
        // Check if error is an instance of Error
        toast.error(` Error submitting form! `, {
          description: ` ${error.message}`, // Now safely access error message
          action: {
            label: "Okay",
            onClick: () => console.log("Okay"),
          },
          duration: 5000,
        });
      }
      // alert("Failed to submit form.");
    } finally {
      setLoading(false); // Set loading to false after the API call is complete
    }
  };

  const handleDialogClose = () => {
    setIsDialogOpen(false);
    setApolloLink("");
    setNumberOfLeads("");
    setFileName("");
  };

  return (
    <div>
      <form className="space-y-4" onSubmit={handleSubmit}>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Apollo Link*
          </label>
          <Input
            type="text"
            placeholder="https://app.apollo.io/#/people"
            value={apolloLink}
            onChange={handleApolloLinkChange}
            required
          />
          {apolloLinkError && (
            <p className="text-red-500 text-sm">{apolloLinkError}</p>
          )}{" "}
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Number of Leads*
          </label>
          <Select
            value={noOfLeads}
            onValueChange={(value) => setNumberOfLeads(value)}
            required
          >
            <SelectTrigger>
              <SelectValue placeholder="Select number of leads" />
            </SelectTrigger>
            <SelectContent className="h-60">
              {/* <SelectItem key="1" value="1">
                    1
                  </SelectItem> */}
              {Array.from({ length: 50 }, (_, i) => (i + 1) * 1000).map(
                (value) => (
                  <SelectItem key={value} value={value.toString()}>
                    {value}
                  </SelectItem>
                )
              )}
            </SelectContent>
          </Select>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            File Name*
          </label>
          <Input
            type="text"
            placeholder="Leads for John Doe"
            value={fileName}
            onChange={handleFileNameChange}
            required
          />
          {fileNameError && (
            <p className="text-red-500 text-sm">{fileNameError}</p>
          )}
        </div>
        <Button
          className="w-full bg-[#F28100] hover:bg-[#F59E0B]"
          disabled={loading}
        >
          {loading ? "Loading..." : "Submit"}
        </Button>
        {noOfLeads && (
          <p className="text-sm text-gray-500 text-center">-{noOfLeads} Credits</p>)}
      </form>
      <Toaster richColors />
    </div>
  );
}
