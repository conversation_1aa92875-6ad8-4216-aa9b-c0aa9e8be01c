# Database Migrations

This directory contains database migration scripts for adding subscription support to the SearchLeads application.

## Migration 001: Add Subscription Support

### Overview
This migration adds support for subscription-based pricing alongside the existing pay-as-you-go model.

### Changes Made
1. **New Tables:**
   - `pricing_plans`: Stores different pricing models (pay-as-you-go: $30/10K, subscription: $20/10K)
   - `subscription_history`: Tracks user subscription status changes over time

2. **Modified Tables:**
   - Adds `preferred_pricing_model`, `subscription_status`, `subscription_start_date`, `subscription_end_date` to User table
   - Adds `pricing_model` and `plan_id` columns to payment/invoice related tables

3. **Security:**
   - Row Level Security (RLS) policies for data access control
   - Proper permissions for authenticated users

### How to Apply Migration

#### Option 1: Supabase Dashboard (Recommended)
1. Go to your Supabase project dashboard
2. Navigate to the SQL Editor
3. Copy and paste the contents of `001_add_subscription_support.sql`
4. Execute the script

#### Option 2: Supabase CLI
```bash
# If you have Supabase CLI installed
supabase db reset
# Or apply specific migration
psql -h your-db-host -U postgres -d your-db-name -f migrations/001_add_subscription_support.sql
```

### Verification
After applying the migration, verify the changes:

```sql
-- Check if new tables exist
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('pricing_plans', 'subscription_history');

-- Check if pricing plans were inserted
SELECT * FROM pricing_plans;

-- Check if User table has new columns (adjust table name if different)
SELECT column_name FROM information_schema.columns 
WHERE table_name = 'User' 
AND column_name IN ('preferred_pricing_model', 'subscription_status');
```

### Rollback (if needed)
If you need to rollback this migration:

```sql
-- Drop new tables
DROP TABLE IF EXISTS subscription_history;
DROP TABLE IF EXISTS pricing_plans;

-- Remove added columns (adjust table names as needed)
ALTER TABLE "User" DROP COLUMN IF EXISTS preferred_pricing_model;
ALTER TABLE "User" DROP COLUMN IF EXISTS subscription_status;
ALTER TABLE "User" DROP COLUMN IF EXISTS subscription_start_date;
ALTER TABLE "User" DROP COLUMN IF EXISTS subscription_end_date;

-- Remove columns from other tables
ALTER TABLE "PaymentLogs" DROP COLUMN IF EXISTS pricing_model;
ALTER TABLE "PaymentLogs" DROP COLUMN IF EXISTS plan_id;
ALTER TABLE "Invoices" DROP COLUMN IF EXISTS pricing_model;
ALTER TABLE "Invoices" DROP COLUMN IF EXISTS plan_id;
ALTER TABLE "EnrichmentLogs" DROP COLUMN IF EXISTS pricing_model;

-- Drop function
DROP FUNCTION IF EXISTS update_updated_at_column();
```

### Notes
- The migration uses conditional logic (`DO $$ BEGIN ... END $$`) to safely add columns only if tables exist
- Adjust table names in the migration script based on your actual database schema
- The migration assumes your User table is named "User" - update if different
- Default pricing models are set to maintain backward compatibility
