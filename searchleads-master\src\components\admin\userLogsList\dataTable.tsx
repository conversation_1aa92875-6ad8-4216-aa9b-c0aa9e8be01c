/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useEffect, useState } from "react";
// import { DataGrid, GridColDef } from "@mui/x-data-grid";
import { DataGrid, GridColDef, Grid<PERSON>oolbar<PERSON>ontainer, GridToolbarQuickFilter } from '@mui/x-data-grid';
import { toast } from "react-toastify";
import axios from "@/axois";
import { useAdminAuth } from "@/context/AdminAuthContext";
import { useDataContext } from "@/context/DataContext"; // Import the context
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import moment from 'moment-timezone'
import EditLogs from "./editLog";

const capitalizeFirstLetter = (string: string) => {
  return string.charAt(0).toUpperCase() + string.slice(1);
};



interface RowData {
  Name: string;
  LogID: number;
  userID: string;
  date: string;
  fileName: string;
  apolloLink: string;
  status: string;
  leadsRequested: number;
  creditsUsed: number;
}

export default function DataTable() {
  const auth = useAdminAuth();
  // console.log(auth.token);

  const [rows, setRows] = useState<RowData[]>([]);
  const { refreshData } = useDataContext();
  const[ loading, setLoading] = useState(false);

  // const fetchDataOld = React.useCallback(async () => {
  //   try {
  //     const res = await axios.get("api/admin/getAllLogs", {
  //       headers: { authorization: `Bearer ${auth.token}` },
  //     });
  //     console.log("res.data", res.data.data);
  //     const data: RowData[] = Array.isArray(res.data.data) ? res.data.data : [];
  //     setRows(data);
  //   } catch (error) {
  //     console.error("Error fetching user logs:", error);
  //   }
  // }, [auth.token]);

  // const getUserData = React.useCallback(
  //   async (userID: string) => {
  //     try {
  //       const res = await axios.post(
  //         'api/admin/getUser',
  //         {
  //           userID,
  //         },
  //         {
  //           headers: { authorization: `Bearer ${auth.token}` },
  //         },
  //       )
  //       // console.log(res.data)
  //       return res.data.data
  //     } catch (error) {
  //       console.error('Error fetching user logs:', error)
  //     }
  //   },
  //   [auth.token],
  // )

  const fetchData = React.useCallback(async () => {
    try {
      setLoading(true);
      const res = await axios.get("api/admin/getAllLogs", {
        headers: { authorization: `Bearer ${auth.token}` },
      })
      // console.log("all logs",res.data)
      // const data: RowData[] = Array.isArray(res.data.logs[0]) ? res.data.logs[0] : [];
      const data: RowData[] = Array.isArray(res.data.data) ? res.data.data : [];

      setRows(data)
      // console.log(res.data)

      // Fetch additional user data for each row
      // const updatedRows = await Promise.all(
      //   res.data.data.map(async (row: RowData) => {
      //     const userData = await getUserData(row.userID)
      //     return {
      //       ...row,
      //       username: userData.name, // Add username
      //       useremails: userData.email, // Add useremails
      //     }
      //   }),
      // )

      // setRows(updatedRows)
      // console.log(updatedRows)
      setLoading(false);
    } catch (error) {
      console.error('Error fetching user logs:', error)
      setLoading(false);
    }
  }, [auth.token])

  const retryLog = async (logID: string) => {
      try {
        console.log(logID);
        const response = await axios.post(
          "/api/admin/retryLog",
          {
            logID: logID
          },
          {
            headers: {
              Authorization: "Bearer " + auth.token,
            },
          }
        );
        console.log(response);
        if (response.status === 200) {
          toast.success("Retried successfully");

          fetchData();
        }
      } catch (e: any) {
        console.log(e);
        toast.error(e.response.data.error);

      }
    };

  const columns: GridColDef[] = [
    // {
    //   field: "date",
    //   headerName: "Date",
    //   width: 130,
    //   renderCell: (data) => {
    //     const date = new Date(data.value);
    //     const formattedDate = `${date.getDate().toString().padStart(2, "0")}/${(
    //       date.getMonth() + 1
    //     )
    //       .toString()
    //       .padStart(2, "0")}/${date.getFullYear().toString().slice(-2)}`;
    //     return formattedDate;
    //   },
    // },
    {
      field: 'date',
      headerName: 'Date (dd-mm-yy)',
      width: 160,
      renderCell: data => {
        const utcTime = data.value // assuming this is in 'YYYY-MM-DD HH:mm:ss.SSS' format
        // Parse the UTC time directly
        const momentUTC = moment.utc(utcTime, 'YYYY-MM-DD HH:mm:ss.SSS')
        // Convert IST time to the user's local timezone
        const userLocalTime = momentUTC.tz(moment.tz.guess())
        // Format the time in a readable format (optional)
        const formattedTime = userLocalTime.format('DD-MM-YY HH:mm z')
  
        return formattedTime
      },
    },
    {
      field: "userID",
      headerName: "User ID",
      width: 250,
      renderCell: (data) => {
        return <div>{data.value}</div>;
      },
    },
    {
      field: 'name',
      headerName: 'User Name',
      width: 150,
      renderCell: data => {
        // const userData = getUserData(data.row.userID)
        return (
          <>
            <div className=''>
              {/* {userData.then(data => data.name)} */}
              {data.value}
            </div>
          </>
        )
      },
    },
    {
      field: 'email',
      headerName: 'User Email',
      width: 180,
      renderCell: data => {
        // const userData = getUserData(data.row.userID)
        return (
          <>
            <div className=''>
              {/* {userData.then(data => data.email)} */}
              {data.value}
            </div>
          </>
        )
      },
    },
    {
      field: "fileName",
      headerName: "File Name",
      width: 150,
      renderCell: (data) => {
        const capitalizedFileName = capitalizeFirstLetter(data.value);
        return capitalizedFileName;
      },
    },
    { field: "apolloLink", headerName: "Search Link", width: 270 },
    {
      field: "url",
      headerName: "Live Results File",
      width: 150,
      renderCell: (data) => (
        <Button
          className="bg-blue-500 hover:bg-blue-700 text-white font-bold rounded"
          disabled={data.value == "url"}
        >
          <a href={data.value} target="_blank" rel="noopener noreferrer">
            Results File
          </a>
        </Button>
      ),
    },
    {
      field: "status",
      headerName: "Status",
      width: 100,
      renderCell: (data) => (
        <div>
          {data.value === "Completed" ? (
            <Badge className="bg-green-500 hover:bg-green-700 rounded-md h-8">
              {data.value}
            </Badge>
          ) : data.value === "Failed" ? (
            <Badge className="bg-red-500 hover:bg-red-700 rounded-md h-8">
              {data.value}
            </Badge>
          ) : data.value === "Cancelled" || data.value === "Failed" ? (
            <Badge className="bg-red-500 hover:bg-red-700 rounded-md h-8">
              {data.value}
            </Badge>
          ) : (
            <Badge className="bg-yellow-500 hover:bg-yellow-700 rounded-md h-8">
              {data.value}
            </Badge>
          )}
        </div>
      ),
    },
    {
      field: "leadsRequested",
      headerName: "Requested Leads",
      type: "number",
      width: 130,
      headerAlign: "left",
      renderCell: (data) => <div className="text-left w-full">{data.value}</div>,
    },
    {
      field: "leadsEnriched",
      headerName: "Leads Enriched",
      type: "number",
      width: 130,
      headerAlign: "left",
      renderCell: (data) => <div className="text-left w-full">{data.value}</div>,
    },
    {
      field: "creditsUsed",
      headerName: "Credits Deducted",
      type: "number",
      width: 130,
      headerAlign: "left",
      renderCell: (data) => <div className="text-left w-full">{data.value}</div>,
    },
    {
      field: "editLog",
      headerName: "Edit Log",
      width: 120,
      renderCell: (data) => {
        return <div><EditLogs logID={data.row.LogID} onLogUpdate={fetchData} /></div>;
      },
    },
    {
      field: "retryLog",
      headerName: "Retry",
      width: 100,
      renderCell: (data) => {
        return <div>
          <Button onClick={() => retryLog(data.row.LogID)}>Retry</Button>
        </div>;
      },
    },
  ];


  useEffect(() => {
    fetchData();
  }, [fetchData, refreshData]); 





// useEffect(() => {
//   getUser();
// }, []);

// const user = "d2b7fd1e-adb1-412a-8d23-9d88d0960103";
//   const getUser = async () => {
//     try {
//       const response = await axios.get(`/api/admin/getUser`, {
//         data: { userID: user }, // Include userID in the body
//         headers: {
//           Authorization: `Bearer ${auth.token}`,
//         },
//       });
//       console.log(response.data);
//     } catch (e: any) {
//       console.log(e);
//     }
//   };


  return (
    <>
    {loading ? <div className="flex justify-center items-center h-full py-40">Loading...</div> : (
    <div>
      <DataGrid
        // loading={loading}
        checkboxSelection={false}
        rows={rows}
        columns={columns}
        getRowId={(row) => row.LogID} // Specify custom id for each row
        initialState={{
          pagination: {
            paginationModel: { page: 0, pageSize: 10 },
          },
        }}
        pageSizeOptions={[5, 10]}
        slots={{ toolbar: CustomToolbar }} // Use custom toolbar
        slotProps={{
          toolbar: {
            showQuickFilter: true,
            disableExport: true,
          },
        }}
        disableColumnFilter
        disableColumnSelector
        disableDensitySelector
      />
    </div>
    )}
    </>
  );
}



// Custom toolbar without export functionality
const CustomToolbar = () => (
  <GridToolbarContainer>
    <GridToolbarQuickFilter
      sx={{
        borderRadius: 2,
        padding: "5px",
        minWidth: "500px",
      }}
    />
  </GridToolbarContainer>
);
