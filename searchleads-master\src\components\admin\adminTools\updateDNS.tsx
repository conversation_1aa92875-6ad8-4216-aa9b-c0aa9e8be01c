/* eslint-disable @typescript-eslint/no-explicit-any */
import React from "react";
("use client");
import { useAdminAuth } from "@/context/AdminAuthContext";
import { toast } from "react-toastify";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { FaGlobe } from "react-icons/fa";
import axios from "@/axois";

export default function UpdateDNS() {
  const auth = useAdminAuth();

  const [dns, setDNS] = useState("");
  const [isDNSDialogOpen, setIsDNSDialogOpen] = useState(false);

  const handleDNSChange = async () => {
    try {
      const response = await axios.post(
        "/api/admin/changeDNS",
        {
          dns,
        },
        {
          headers: {
            Authorization: "Bearer " + auth.token,
          },
        }
      );
      if (response.status === 200) {
        toast.success("Automation Link updated");
        setIsDNSDialogOpen(false); // Close the dialog on success
        setDNS("");
      }
    } catch (e: any) {
      console.log(e);
      toast.error(e.response.data.error);
    }
  };

  return (
    <div className="mb-2 flex justify-center items-center">
      <Dialog
        open={isDNSDialogOpen}
        onOpenChange={setIsDNSDialogOpen}
      >
        <DialogTrigger asChild>
          <Button variant="outline" className="w-fit h-fit p-6">
            <div className="flex justify-center items-center">
              <FaGlobe className="mr-4 w-14 h-14 md:w-20 md:h-20" />
              <div className="flex flex-col justify-center items-center ">
                <p className="text-base md:text-xl font-medium">
                  Change <br />
                  DNS
                </p>
              </div>
            </div>
          </Button>
        </DialogTrigger>
        <DialogContent className="sm:max-w-[425px]">
          <DialogDescription></DialogDescription>
          <DialogHeader>
            <DialogTitle> Update DNS </DialogTitle>
            <DialogDescription></DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Input
                value={dns}
                onChange={(e) => setDNS(e.target.value)}
                type="text"
                placeholder="Add new DNS"
                className="col-span-4"
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              type="submit"
              onClick={() => {
                handleDNSChange();
              }}
              className="w-full"
            >
              Save
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
