"use client";

import {
  PaymentElement,
  useElements,
  useStripe,
} from "@stripe/react-stripe-js"; // Import necessary Stripe components
import React, { FC, useState } from "react"; // Import React and necessary hooks
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Toaster, toast } from "sonner";
import { useAuth } from "@/context/AuthContext";
interface PaymentFormProps {
  onPaymentSuccess: (isSuccessful: boolean) => void;
}

const PaymentForm: FC<PaymentFormProps> = ({ onPaymentSuccess }) => {
  const auth = useAuth();
  const stripe = useStripe(); // Initialize Stripe
  const elements = useElements(); // Initialize Stripe elements
  const [loading, setLoading] = useState(false); // State for loading status
  const [error, setError] = useState<string | null>(null); // State for error messages

  // State for custom address form
  const [address, setAddress] = useState({
    line1: "",
    line2: "",
    city: "",
    state: "",
    postalCode: "",
    country: "",
  });

  const handleAddressChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setAddress((prev) => ({ ...prev, [name]: value }));
  };

  const onSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault(); // Prevent default form submission
    setLoading(true); // Set loading state to true
    setError(null); // Reset error state

    try {
      if (!stripe || !elements) return;

      const { error, paymentIntent } = await stripe.confirmPayment({
        elements,
        confirmParams: {
          return_url: "https://app.searchleads.co/",
          payment_method_data: {
            billing_details: {
              name: auth.user.name,
              address: {
                line1: address.line1,
                line2: address.line2,
                city: address.city,
                state: address.state,
                postal_code: address.postalCode,
              },
            },
          },
        },
        redirect: "if_required",
      });

      if (error) {
        console.error("Stripe Error:", error);
        setError(error.message || "An unknown error occurred.");
      } else if (paymentIntent && paymentIntent.status === "succeeded") {
        toast("Payment successful!", { duration: 5000 });
        onPaymentSuccess(true);
      } else {
        console.warn("Payment incomplete:", paymentIntent);
        setError("Payment was not completed. Please try again.");
      }
    } catch (err) {
      console.error("Payment error:", err);
      setError(err.message || "An unknown error occurred.");
    } finally {
      setLoading(false);
    }
  };

  // Check if all required fields are filled
  const isFormComplete =
    address.line1 && address.city && address.state && address.postalCode;

  return (
    <>
      <form onSubmit={onSubmit}>
        <div className="max-h-96 overflow-y-auto">
          <PaymentElement />
          <div className="grid grid-cols-1 gap-2">
            <label className="text-base pt-4 w-full">Shipping Address</label>
            <div className="grid grid-cols-1 gap-4 mb-4">
              <input
                type="text"
                name="line1"
                placeholder="Address Line 1"
                value={address.line1}
                onChange={handleAddressChange}
                className="border rounded p-2 w-full"
                required
              />
              <input
                type="text"
                name="line2"
                placeholder="Address Line 2 (Optional)"
                value={address.line2}
                onChange={handleAddressChange}
                className="border rounded p-2 w-full"
              />
              <input
                type="text"
                name="city"
                placeholder="City"
                value={address.city}
                onChange={handleAddressChange}
                className="border rounded p-2 w-full"
                required
              />
              <div className="grid grid-cols-2 gap-4 mb-4">
                <input
                  type="text"
                  name="postalCode"
                  placeholder="Postal Code"
                  value={address.postalCode}
                  onChange={handleAddressChange}
                  className="border rounded p-2 w-full"
                  required
                />
                <input
                  type="text"
                  name="state"
                  placeholder="State"
                  value={address.state}
                  onChange={handleAddressChange}
                  className="border rounded p-2 w-full"
                  required
                />
              </div>
            </div>
          </div>
          {error && <div className="text-red-600 mt-2">{error}</div>}{" "}
        </div>
        <Button
          type="submit"
          className="mt-4"
          disabled={!stripe || loading || !isFormComplete}
        >
          {loading ? "Processing..." : "Pay"}{" "}
        </Button>
      </form>
      <Toaster richColors />
    </>
  );
};

export default PaymentForm;
