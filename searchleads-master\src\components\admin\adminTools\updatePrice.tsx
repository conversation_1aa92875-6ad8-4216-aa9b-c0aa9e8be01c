/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';
"use client";
import { useAdminAuth } from "@/context/AdminAuthContext";
import { toast } from "react-toastify";
import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

import axios from "@/axois";
import { BiMoneyWithdraw } from "react-icons/bi";

export default function UpdatePrice() {
    const auth = useAdminAuth();
  
    const [price, setPrice] = useState("0");
    const [priceInput, setPriceInput] = useState("");
    const [isPriceDialogOpen, setIsPriceDialogOpen] = useState(false);
  
    useEffect(() => {
      fetchPrice();
    }, []);
  
    const fetchPrice = async () => {
      try {
        const response = await axios.get("/api/admin/getPrice", {
          headers: {
            Authorization: `Bearer ${auth.token}`,
          },
        });
        if (response.status === 200) {
          setPrice(response.data.resp);
        }
      } catch (e: any) {
        console.log(e);
        toast.error(e.message);
      }
    };
  
    const handlePriceChange = async () => {
      try {
        const response = await axios.post(
          "/api/admin/changePrice",
          {
            newPrice: parseInt(priceInput, 10),
          },
          {
            headers: {
              Authorization: "Bearer " + auth.token,
            },
          }
        );
        if (response.status === 200) {
          toast.success("Price updated");
          setIsPriceDialogOpen(false); // Close the dialog on success
          setPriceInput("");
        }
        fetchPrice();
      } catch (e: any) {
        console.log(e);
        toast.error(e.response.data.error);
      }
    };

    
  return (
    <div className="mb-2 flex justify-center items-center">
    <Dialog open={isPriceDialogOpen} onOpenChange={setIsPriceDialogOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" className="w-fit h-fit p-6">
          <div className="flex justify-center items-center">
            <BiMoneyWithdraw className="mr-4 w-14 h-14 md:w-20 md:h-20" />
            <div className="flex flex-col justify-center items-center">
              <p className="text-base md:text-xl font-medium">Credit Price</p>
              <p className="text-xl md:text-4xl font-bold">${price}</p>
            </div>
          </div>
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]"
      onInteractOutside={e => {
        e.preventDefault()
      }}
      onEscapeKeyDown={e => {
        e.preventDefault()
      }}>
        <DialogHeader>
          <DialogTitle>Update Price</DialogTitle>
          <DialogDescription></DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Input
              value={priceInput}
              onChange={(e) => setPriceInput(e.target.value)}
              type="number"
              placeholder="Credit Price"
              className="col-span-4"
            />
          </div>
        </div>
        <DialogFooter>
          <Button
            type="submit"
            onClick={() => {
              handlePriceChange();
            }}
            className="w-full"
          >
            Save
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  </div>
  );
}
