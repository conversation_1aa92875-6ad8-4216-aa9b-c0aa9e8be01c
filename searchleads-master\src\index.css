@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Geist:wght@100..900&family=Righteous&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;


body {
  font-family: 'Geist', sans-serif;
  /* @apply bg-background text-text; */
}

@layer base {
  :root{
    --text: 9, 17, 21;
    --background: 0 0% 100%;
    --background2: 240, 242, 256;
    --primary: 0 0% 9%;
    --secondary: 0 0% 96.1%;
    --accent: 0 0% 96.1%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary-foreground: 0 0% 98%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
  .dark {
    --text: 234, 242, 246;
    --background: 0 0% 3.9%;
    --background2: 15, 17, 33;
    --primary: 0 0% 98%;
    --secondary: 0 0% 14.9%;
    --accent: 0 0% 14.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
  /* ::-webkit-scrollbar
  {
    @apply w-2;
  } */

  /* ::-webkit-scrollbar-thumb
  {
    @apply bg-primary rounded-full;
  } */
}

/* Enables the ability to override these classes, eg bg-accent or p-8 works now ! */

@layer components {
  .p-element {
    @apply px-4 py-3;
  }
  .text-element
  {
    @apply text-sm md:text-base;
  }
  .hover-element
  {
    @apply hover:-translate-y-0.5 transition-transform active:scale-95;
  }
  .round
  {
    @apply rounded-lg md:rounded-xl;
  }

  .rotate-animation {
    animation: reverse-spin 1s linear infinite;
  }
  
}


@keyframes reverse-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(-360deg);
  }
}


@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}