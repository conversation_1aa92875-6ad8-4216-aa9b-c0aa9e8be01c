"use client";
import { use<PERSON><PERSON>back, useEffect, useState } from "react";
import {
  DataGrid,
  GridColDef,
  GridToolbarContainer,
  GridToolbarQuickFilter,
  useGridApiContext,
  useGridSelector,
  gridPageSelector,
  gridPageCountSelector,
  gridPageSizeSelector,
} from "@mui/x-data-grid";
import { IconButton, Pagination, PaginationItem, Box } from "@mui/material";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
} from "@/components/ui/select";
import axios from "@/axois";
import { useAuth } from "@/context/AuthContext";
import { useDataContext } from "@/context/DataContext"; // Import the context
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ChevronDown, Search, ArrowUpRight } from "lucide-react";
import Ellipse from "@/assets/icons/Ellipse.svg";

const capitalizeFirstLetter = (string: string) => {
  return string.charAt(0).toUpperCase() + string.slice(1);
};

const columns: GridColDef[] = [
  {
    field: "date",
    headerName: "Date",
    width: 130,
    renderCell: (data) => {
      const date = new Date(data.value);
      const formattedDate = `${date.getDate().toString().padStart(2, "0")}/${(
        date.getMonth() + 1
      )
        .toString()
        .padStart(2, "0")}/${date.getFullYear().toString().slice(-2)}`;
      return formattedDate;
    },
  },
  {
    field: "CreditsRequested",
    headerName: "Credits Bought",
    width: 130,
    renderCell: (data) => {
      return data.value;
    },
  },
  // {
  //   field: 'ServiceName',
  //   headerName: 'Service Name',
  //   width: 180,
  //   headerAlign: 'left',
  //   renderCell: data => <div className='text-left w-full'>{data.value}</div>,
  // },
  {
    field: "Url",
    headerName: "Invoice",
    width: 150,
    align: "center", // Center the content horizontally in the cell
    renderCell: (data) => {
      return (
        <>
          <div className="h-full flex justify-center items-center">
            {data?.value ? (
              <>
                <a href={data.value} target="_blank">
                  {/* <Button
                  variant="outline"
                  className="rounded-lg cursor-pointer hover:bg-[#5844B2] hover:text-white"
                >
                  Preview <ChevronDown className="ml-2 w-3" />
                </Button> */}
                  <Badge className="font-bold rounded-lg px-2 h-8 text-[#006DF2] bg-[#006DF2]/15 hover:bg-[#006DF2]/30 cursor-pointer flex items-center justify-center">
                    <p className="text-sm">Preview</p>
                    <ChevronDown className="ml-2 w-3" />
                  </Badge>
                </a>
              </>
            ) : (
              <Button
                variant="outline"
                disabled
                className="rounded-lg cursor-pointer hover:bg-[#5844B2] hover:text-white"
              >
                Preview <ChevronDown className="ml-2 w-3" />
              </Button>
            )}
          </div>
        </>
      );
    },
  },
];

interface RowData {
  LogID: number;
  date: string;
  fileName: string;
  apolloLink: string;
  status: string;
  leadsRequested: number;
  creditsUsed: number;
}

export default function DataTable() {
  const auth = useAuth();
  const [rows, setRows] = useState<RowData[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const fetchData = useCallback(async () => {
    try {
      setIsLoading(true);
      const res = await axios.get("api/billing/getBillsByUser", {
        headers: { authorization: `Bearer ${auth.session?.access_token}` },
      });
      // console.log(res.data.bills)
      const data: RowData[] = Array.isArray(res.data.bills)
        ? res.data.bills
        : [];
      setRows(data);
      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
      console.error("Error fetching invoices", error);
    }
  }, [auth.session?.access_token]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return (
    <div className="lg:h-[calc(100vh-153.5px)] ">
      {isLoading ? (
        <div className="flex flex-col items-center justify-center h-96 space-y-4">
          {/* Spinning Loader */}
          <img
            draggable={false}
            src={Ellipse}
            alt="Ellipse"
            width={40}
            height={40}
            className="animate-spin"
          />
          {/* Text Descriptions */}
          <div className="text-center">
            <span className="text-gray-600 text-base block">
              Loading your Table...
            </span>
            <span className="text-gray-500 text-sm">
              Please wait while we load your data and prepare the table for you.
            </span>
          </div>
        </div>
      ) : (
        <DataGrid
          sx={{
            "&.MuiDataGrid-root": {
              border: "none !important",
            },
          }}
          checkboxSelection={false}
          rows={rows}
          rowHeight={47} // Set custom row height
          columns={columns}
          getRowId={(row) => row.BillingID} // Specify custom id for each row
          initialState={{
            sorting: {
              sortModel: [{ field: "date", sort: "desc" }],
            },
            // pagination: {
            //   paginationModel: { page: 0, pageSize: 10 },
            // },
          }}
          autoPageSize // Automatically adjusts page size to fit grid height
          // pageSizeOptions={[5, 10]}
          getRowClassName={(params) => `border-none bg-white`} // Updated class name
          getCellClassName={(params) => `border-none `} // Updated class name
          slots={{
            pagination: CustomPagination, // Use custom pagination
            toolbar: CustomToolbar, // Use custom toolbar
          }}
          slotProps={{
            toolbar: {
              showQuickFilter: true,
              disableExport: true,
            },
          }}
          disableColumnFilter
          disableColumnSelector
          disableDensitySelector
        />
      )}
    </div>
  );
}

const CustomToolbar = () => {
  const [showInput, setShowInput] = useState(false);

  const toggleInput = () => {
    setShowInput((prev) => !prev);
  };

  return (
    <GridToolbarContainer sx={{ display: "flex", justifyContent: "flex-end" }}>
      <div className="flex items-center justify-center">
        {/* <div style={{ display: "flex", alignItems: "center" }}> */}
        <IconButton
          onClick={toggleInput}
          sx={{
            borderRadius: "15px", // Rounded-lg equivalent
            border: "1px solid #ddd", // Light border around the button
            padding: 1, // Adjust padding for consistent roundness
            transition: "background-color 0.3s ease", // Smooth hover transition
          }}
        >
          <Search className="p-1" />
        </IconButton>
        <GridToolbarQuickFilter
          sx={{
            transition: "width 0.3s ease, opacity 0.3s ease",
            width: showInput ? "300px" : "0px",
            opacity: showInput ? 1 : 0,
            padding: showInput ? "5px" : "0px",
            borderRadius: 2,
            overflow: "hidden",
            marginLeft: showInput ? "0px" : "0px",
            "& .MuiSvgIcon-root": {
              display: "none", // Removes the default search icon at the start
            },
          }}
        />
      </div>
    </GridToolbarContainer>
  );
};

// Custom Pagination Component
const CustomPagination = () => {
  const apiRef = useGridApiContext();
  const page = useGridSelector(apiRef, gridPageSelector); // Current page
  const pageCount = useGridSelector(apiRef, gridPageCountSelector); // Total number of pages

  const handlePageChange = (event, value) => {
    if (value) {
      apiRef.current.setPage(value - 1); // Pagination uses 1-based indexing
    }
  };

  const handleSelectChange = (newValue) => {
    const newPage = parseInt(newValue, 10) - 1; // Convert value to 0-based index
    apiRef.current.setPage(newPage);
  };

  return (
    <Box
      sx={{
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        gap: 2,
        mt: 1,
        mr: 15,
      }}
    >
      {/* Previous Button */}
      <Pagination
        count={1} // Hack to only render "previous" and "next" buttons
        page={page + 1} // 1-based index for UI
        siblingCount={0}
        boundaryCount={0}
        onChange={handlePageChange}
        renderItem={(item) => {
          if (item.type === "previous") {
            return (
              <PaginationItem
                {...item}
                onClick={() => apiRef.current.setPage(page - 1)}
                disabled={page === 0}
                sx={{
                  borderRadius: "8px",
                  boxShadow: "0 1px 4px rgba(0, 0, 0, 0.2)",
                }}
              />
            );
          }

          if (item.type === "page") {
            return (
              <div className="mx-1">
                {/* Page Selector Dropdown */}
                <Select
                  value={(page + 1).toString()}
                  onValueChange={handleSelectChange} // Corrected event handler
                >
                  <SelectTrigger className="h-8 rounded-lg shadow-md px-2  py-0">
                    <p className="mx-2">{page + 1}</p>
                  </SelectTrigger>
                  <SelectContent>
                    {Array.from({ length: pageCount }, (_, i) => (
                      <SelectItem key={i} value={(i + 1).toString()}>
                        {i + 1}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            );
          }

          if (item.type === "next") {
            return (
              <>
                <PaginationItem
                  {...item}
                  onClick={() => apiRef.current.setPage(page + 1)}
                  disabled={page === pageCount - 1}
                  sx={{
                    borderRadius: "8px",
                    boxShadow: "0 1px 4px rgba(0, 0, 0, 0.2)",
                  }}
                />
              </>
            );
          }

          return null; // Skip other items
        }}
      />
    </Box>
  );
};
