{"name": "search-leads", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "host": "vite --host"}, "dependencies": {"@emotion/react": "^11.13.0", "@emotion/styled": "^11.13.0", "@mui/material": "^5.16.7", "@mui/x-data-grid": "^7.12.1", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.5", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.2", "@stripe/react-stripe-js": "^2.7.3", "@stripe/stripe-js": "^4.2.0", "@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/supabase-js": "^2.43.2", "@tanstack/react-table": "^8.20.1", "axios": "^1.7.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.13", "framer-motion": "^12.0.1", "lucide-react": "^0.427.0", "moment": "^2.30.1", "moment-timezone": "^0.5.46", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-copy-to-clipboard": "^5.1.0", "react-datepicker": "^7.5.0", "react-dom": "^18.2.0", "react-icons": "^5.3.0", "react-phone-number-input": "^3.4.8", "react-router-dom": "^6.23.1", "react-toastify": "^10.0.5", "recharts": "^2.12.7", "sonner": "^1.5.0", "stripe": "^16.7.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "twilio": "^5.3.2"}, "devDependencies": {"@types/node": "^22.2.0", "@types/react": "^18.2.66", "@types/react-copy-to-clipboard": "^5.0.7", "@types/react-dom": "^18.2.22", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "postcss": "^8.4.38", "tailwindcss": "^3.4.3", "typescript": "^5.2.2", "vite": "^5.2.0"}}