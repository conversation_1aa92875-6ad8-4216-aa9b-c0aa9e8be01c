"use client";
import { useEffect } from "react";
import Navbar from "./components/common/AppNavbar";
import Dashboard from "./components/dashboard/Dashboard";

export default function App() {
  // Tawk.to chat widget
  useEffect(() => {
    const script = document.createElement("script");
    script.async = true;
    script.src = "https://embed.tawk.to/6672a8cf981b6c56477ed3d0/1i0nu6qrr";
    script.charset = "UTF-8";
    script.setAttribute("crossorigin", "*");

    const firstScript = document.getElementsByTagName("script")[0];
    firstScript.parentNode?.insertBefore(script, firstScript);

    return () => {
      const existingScript = document.querySelector(
        "script[src='https://embed.tawk.to/6672a8cf981b6c56477ed3d0/1i0nu6qrr']"
      );
      if (existingScript) {
        existingScript.remove();
      }
    };
  }, []);

  return (
    <>
      <div className="bg-[#F281000D]">
        <Navbar />
        <Dashboard />
      </div>
    </>
  );
}
