import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from "path"

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  server: {
    hmr: {
      overlay: false, // Set overlay to false
    },
  },
  optimizeDeps: {
    exclude: ['chunk-3TOLAYXB.js'], // Exclude the problematic dependency
  },
})