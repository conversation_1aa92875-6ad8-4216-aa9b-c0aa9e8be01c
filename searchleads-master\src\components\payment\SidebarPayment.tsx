/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";
import { useAuth } from "@/context/AuthContext";
import { useDataContext } from "@/context/DataContext";
import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import axios from "@/axois";
import { Toaster, toast } from "sonner";
import PaymentComponent from "@/components/payment/Payment";
import {
  SelectTrigger as SelectTrigger_Modified,
  SelectItem as SelectItem_Modified,
} from "@/components/ui/select-modified";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import Ellipse from "@/assets/icons/Ellipse.svg";

import { supabase } from "@/lib/supabase";

import Stripe from "stripe";
import { Input } from "@/components/ui/input";
const stripe = new Stripe(import.meta.env.VITE_STRIPE_SECRET_KEY!, {
  typescript: true,
  apiVersion: "2024-06-20",
});

// const auth = useAuth();

export default function Settings() {
  // export default function Settings({ creditPrice }) {
  const auth = useAuth();
  const { globalCredits, setGlobalCredits } = useDataContext();

  const [formData, setFormData] = useState({
    inputValue: "",
    currency: "",
    couponCode: "",
  });
  const [amount, setAmount] = useState(""); // New state for amount
  const [currency, setCurrency] = useState("");
  const [creditPrice, setCreditPrice] = useState(0);
  const [rate, setRate] = useState(0);
  const [customerId, setCustomerId] = useState("");
  const [beforeDiscountedPrice, setBeforeDiscountedPrice] = useState("");
  const [showCreditsDiv, setShowCreditsDiv] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [clientSecret, setClientSecret] = useState<string | null>(null);
  const [loadingPayment, setLoadingPayment] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (auth.user?.credits !== undefined) {
      setGlobalCredits(auth.user.credits);
    }
  }, [auth.user?.credits]);

  const fetchPrice = async () => {
    try {
      setIsLoading(true);
      const response = await axios.get("/api/user/getCost", {
        headers: {
          Authorization: `Bearer ${auth.session?.access_token}`,
        },
      });
      // console.log("price",response.data);
      if (response.status === 200) {
        setCreditPrice(response.data.costPerLead); // Assuming the API returns the price directly
      }
      setIsLoading(false);
    } catch (error: any) {
      setIsLoading(false);
      console.error(error);
      // toast.error(error.message);
    }
  };

  useEffect(() => {
    fetchPrice();
  }, []);

  const calculateAmount = async (inputValue: string, currency: string) => {
    // await fetchPrice();

    const value = parseInt(inputValue);
    let rate = 0;

    switch (currency) {
      case "usd":
        rate = creditPrice * 1; // 3 dollars for every 1000 leads
        break;
      case "eur":
        rate = creditPrice * 0.9; // example rate for Euro
        break;
      case "inr":
        rate = creditPrice * 83.95; // example rate for Indian Rupee
        break;
      case "gbp":
        rate = creditPrice * 0.76; // example rate for British Pound
        break;
      default:
        rate = 0;
    }
    setRate(rate);
    const calculatedAmount = ((value / 1000) * rate).toFixed(2);
    // console.log("calculatedAmount", calculatedAmount);
    return calculatedAmount;
  };

  const handleButtonClick = async () => {
    try {
      let customerId = null;
      setLoadingPayment(true);

      // Step 1: Check if there's an existing customer with the given email
      try {
        customerId = await findCustomerByEmail(auth.user.email);
        console.log("Customer ID found:", customerId);
      } catch (error) {
        console.warn("Customer not found, creating a new customer...");
      }

      // Step 2: Fetch the coupon discount if a valid coupon code is provided
      let discountPercentage = 0;
      if (formData.couponCode && formData.couponCode.trim() !== "") {
        try {
          discountPercentage = await fetchCouponByCode(formData.couponCode);
          console.log("Discount Percentage:", discountPercentage);
        } catch (error) {
          toast.error("Invalid coupon code, proceeding without discount...");
          console.warn("Invalid coupon code, proceeding without discount...");
        }
      }

      // Step 3: Handle customer update or creation based on coupon status
      if (customerId) {
        if (discountPercentage > 0) {
          // Update the customer with the new coupon discount
          const updatedCustomer = await updateCustomer(
            customerId,
            formData.couponCode
          );
          console.log("Updated Customer:", updatedCustomer);
        } else {
          // Remove any existing discount if no valid coupon is provided
          try {
            await removeCustomerDiscount(customerId);
            console.log(`Discount removed for customer: ${customerId}`);
          } catch (error) {
            console.warn("No discount to remove or already removed.");
          }
        }
      } else {
        // Step 4: If no customer exists, create a new customer with the coupon code
        const newCustomer = await createCustomer(
          auth.user.name,
          auth.user.email,
          formData.couponCode
        );
        console.log("New Customer Created:", newCustomer);
        customerId = newCustomer.id;
      }

      // Step 4: Apply the discount percentage to the amount
      const amountBeforeDiscount = await calculateAmount(
        formData.inputValue,
        formData.currency
      );
      const discountedAmount =
        Number(amountBeforeDiscount) -
        Number(amountBeforeDiscount) * (discountPercentage / 100);

      // Step 5: Set the discounted amount
      setAmount(discountedAmount.toString());
      setBeforeDiscountedPrice(amountBeforeDiscount);

      setCurrency(formData.currency);

      // Step 6: Set the customerId after all operations are done
      setCustomerId(customerId);
      console.log("Customer ID set:", customerId);

      // Step 7: Call fetchClientSecret after state updates
      await fetchClientSecret(
        discountedAmount.toString(),
        customerId,
        formData.currency
      );
      setLoadingPayment(false);
    } catch (error) {
      console.error("Error handling button click:", error);
      setLoadingPayment(false);
    }
  };

  const handlePaymentSuccess = async () => {
    setFormData({
      inputValue: "",
      currency: "",
      couponCode: "",
    });
    setAmount("");
    try {
      // const res = await axios.post(
      //   "api/user/addCredits",
      //   {
      //     credits: formData.inputValue, // Ensure inputValue is an integer
      //   },
      //   {
      //     headers: { authorization: `Bearer ${auth.session?.access_token}` },
      //   }
      // );

      toast(` ${formData.inputValue} Credits Added`, {
        description: "Refresh Page or Update the Credits",
        action: {
          label: "Okay",
          onClick: () => console.log("Okay"),
        },
        duration: 5000,
      });

      // New API call to create an invoice
      const invoiceData = {
        quantity: formData.inputValue, // Assuming formData.inputValue represents the quantity
        unitCost: rate / 1000, // Assuming creditPrice is the cost per unit
        currency: currency, // Currency from state
        amountPaid: amount, // Amount calculated
        from: auth.user?.email || "", // Assuming you want to use the user's email as 'from'
        creditsRequested: Number(formData.inputValue), // Convert formData.inputValue to an integer
      };

      try {
        await axios.post("api/billing/createInvoice", invoiceData, {
          headers: { authorization: `Bearer ${auth.session?.access_token}` },
        });
        toast.success("Invoice created successfully");
      } catch (error) {
        console.error("Error creating invoice:", error);
        toast.error("Failed to create invoice");
      }
    } catch (error) {
      console.error(error);
    }
  };

  const userId = auth.user?.UserID;

  useEffect(() => {
    // Set up the real-time subscription
    const changes = supabase
      .channel("user-db-changes")
      .on(
        "postgres_changes",
        {
          event: "*", // Listening to INSERT events
          schema: "public",
          table: "User", // Replace with your table name
        },
        (payload) => {
          // console.log('New insert received:', payload.new)
          // Update the user state with the new row data
          if (
            payload.new &&
            typeof payload.new === "object" &&
            "credits" in payload.new
          ) {
            // console.log('New credits:', payload.new.credits)
            setGlobalCredits(payload.new.credits);
          }
        }
      )
      .subscribe();

    // Clean up subscription on component unmount
    return () => {
      supabase.removeChannel(changes);
    };
  }, [auth.session?.access_token, setGlobalCredits]);

  const fetchClientSecret = async (
    amount: string,
    customerId: string,
    currency: string
  ) => {
    console.log("credits asked for:", formData.inputValue);

    try {
      const response = await axios.post(
        "/api/payments/createPaymentIntent",
        {
          amount: Math.round(Number(amount) * 100), // Convert to smallest currency unit
          currency: currency,
          costumerID: customerId, // Ensure the key matches backend expectation
          description: "Payment for SearchLeads Credits",
          automaticPayment: true, // Matches backend expected key
          referral: window.Afficone?.referral ?? null,
          credits: formData.inputValue,
          userID: userId,
          cientName: "Lakshay", // Ensure this key matches backend expectation
        },
        {
          headers: {
            Authorization: `Bearer ${auth.session?.access_token}`,
          },
        }
      );

      const paymentIntent = response.data.paymentIntent;

      if (paymentIntent) {
        console.log("paymentIntent from api", paymentIntent);
        setClientSecret(paymentIntent.client_secret);
        setIsDialogOpen(true); // Open dialog when client secret is ready
      } else {
        console.error("No paymentIntent returned from backend");
      }
    } catch (error) {
      console.error("Error fetching client secret:", error);
    }
  };

  // Function to create a customer
  async function createCustomer(name: string, email: string, couponId: string) {
    try {
      const response = await axios.post(
        "/api/payments/createCustomer",
        {
          name: name,
          email: email,
          couponID: couponId, // Ensure it matches the backend key
        },
        {
          headers: {
            Authorization: `Bearer ${auth.session?.access_token}`,
          },
        }
      );

      return response.data.customer;
    } catch (error) {
      console.error("Error creating customer:", error);
      throw error;
    }
  }

  // Function to find a customer by email
  async function findCustomerByEmail(email: string) {
    try {
      const response = await axios.post(
        "/api/payments/findCustomerByEmail",
        { email },
        {
          headers: {
            Authorization: `Bearer ${auth.session?.access_token}`, // Assuming auth.session contains the token
          },
        }
      );

      console.log("Customer found:", response.data.customerId);
      return response.data.customerId;
    } catch (error: any) {
      console.error("Error finding customer:", error.message);
      throw new Error("Failed to find customer.");
    }
  }

  // Update customer coupon code
  async function updateCustomer(customerId: string, couponCode: string) {
    try {
      const response = await axios.post(
        "/api/payments/updateCouponCode",
        { customerId, couponCode },
        {
          headers: {
            Authorization: `Bearer ${auth.session?.access_token}`,
          },
        }
      );

      console.log(`Updated customer ${customerId} with coupon:`, response.data.updatedCustomer);
      return response.data.updatedCustomer;
    } catch (error: any) {
      console.error("Error updating customer with coupon:", error.message);
      throw new Error("Failed to update customer with coupon.");
    }
  }

  // Remove the coupon from the customer
  async function removeCustomerDiscount(customerId: string) {
    try {
      const response = await axios.post(
        "/api/payments/deleteCustomer",
        { customerId },
        {
          headers: {
            Authorization: `Bearer ${auth.session?.access_token}`,
          },
        }
      );

      console.log(`Discount removed from customer ${customerId}:`, response.data.deletedCustomer);
    } catch (error: any) {
      console.error("Error removing discount:", error.message);
    }
  }

  // Fetch coupon details by coupon code
  async function fetchCouponByCode(couponCode: string) {
    try {
      const response = await axios.post(
        "/api/payments/retrieveCoupon",
        { couponCode },
        {
          headers: {
            Authorization: `Bearer ${auth.session?.access_token}`,
          },
        }
      );
      console.log("coupon", response.data);
      const coupon = response.data.coupon;

      if (coupon && coupon.percent_off) {
        console.log("Coupon details:", coupon);
        return coupon.percent_off;
      } else {
        throw new Error("Coupon is not a percentage discount.");
      }
    } catch (error: any) {
      console.error("Error fetching coupon:", error.message);
      throw new Error("Coupon not found or invalid.");
    }
  }


  return (
    <div>
      <div className=" h-full rounded-lg">
        {isLoading ? (
          <div className="flex flex-col items-center justify-center h-32 space-y-4">
            {/* Spinning Loader */}
            <img
              draggable={false}
              src={Ellipse}
              alt="Ellipse"
              width={40}
              height={40}
              className="animate-spin"
            />
            {/* Text Descriptions */}
            <div className="text-center">
              <span className="text-gray-600 text-base block">
                Loading credit options...
              </span>
              <span className="text-gray-500 text-sm">
                Please wait while we prepare the options for you to choose.
              </span>
            </div>
          </div>
        ) : (
          <div>
            {creditPrice !== 0 && (
              <form
                className="h-full rounded-lg pt-2"
                onSubmit={async (e) => {
                  e.preventDefault();
                  await handleButtonClick();
                  // await fetchClientSecret();
                }}
              >
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Select the Num of Credits
                </label>
                <Select
                  value={formData.inputValue}
                  onValueChange={(value) =>
                    setFormData({ ...formData, inputValue: value })
                  }
                  required
                >
                  <SelectTrigger className="mt-2 mb-4">
                    <SelectValue placeholder="Enter Amount" />
                  </SelectTrigger>
                  <SelectContent className="h-60 w-full">
                    {Array.from({ length: 10 }, (_, i) => (i + 1) * 10000).map((value) => (
                      <SelectItem_Modified key={value} value={value.toString()} className="w-full">
                        <div className="flex items-center gap-5 w-full">
                          <span>{value}</span>
                          <span className="text-gray-500">(${((value / 1000) * creditPrice)})</span>
                        </div>

                      </SelectItem_Modified>
                    ))}
                  </SelectContent>
                </Select>

                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {" "}
                  Your Currency
                </label>
                <Select
                  value={formData.currency}
                  onValueChange={(value) =>
                    setFormData({ ...formData, currency: value })
                  }
                >
                  <SelectTrigger className=" mt-2 mb-4">
                    <SelectValue placeholder="Currency" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="eur">Euro (EUR)</SelectItem>
                    <SelectItem value="usd">US Dollar (USD)</SelectItem>
                    <SelectItem value="inr">Indian Rupee (INR)</SelectItem>
                    <SelectItem value="gbp">British Pound (GBP)</SelectItem>
                  </SelectContent>
                </Select>

                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Coupon Code
                </label>
                <Input
                  id="couponCode"
                  type="text"
                  placeholder="Enter Coupon Code"
                  value={formData.couponCode}
                  onChange={(e) =>
                    setFormData({ ...formData, couponCode: e.target.value })
                  }
                  className="mt-2 mb-4"
                />

                <Button
                  type="submit"
                  disabled={
                    !formData.inputValue.trim() ||
                    !formData.currency ||
                    loadingPayment
                  }
                  className="w-full bg-[#F28100] hover:bg-[#F59E0B]"
                >
                  {loadingPayment ? "Submitting" : "Submit"}
                </Button>
              </form>
            )}
            {amount && (
              <PaymentComponent
                credits={formData.inputValue}
                currency={currency}
                maxAmount={amount}
                beforeDiscount={beforeDiscountedPrice}
                customerId={customerId}
                onPaymentSuccess={handlePaymentSuccess}
                clientSecret={clientSecret}
                isDialogOpen={isDialogOpen}
                setIsDialogOpen={setIsDialogOpen} // Pass dialog control state
              />
            )}
          </div>
        )}
      </div>

      <Toaster richColors />
    </div>
  );
}
