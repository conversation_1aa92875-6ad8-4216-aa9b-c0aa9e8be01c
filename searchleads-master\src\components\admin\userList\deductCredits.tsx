/* eslint-disable @typescript-eslint/no-explicit-any */
import React from "react";
("use client");
import { useAdminAuth } from "@/context/AdminAuthContext";
import { toast } from "react-toastify";
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import axios from "@/axois";

export default function DeductCredits({ userID, onCreditsUpdate }: { userID: string; onCreditsUpdate: () => void }) {
  const auth = useAdminAuth();

  const [credits, setCredits] = useState<number | null>(null); // Initialize with null instead of undefined
  const [isCreditsDialogOpen, setIsCreditsDialogOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleCreditsChange = async () => {
    // console.log("userID", userID);
    try {
      setIsLoading(true);
      const response = await axios.post(
        "/api/admin/updateCredits",
        {
          userID,
          // credits,
          credits: credits ? -credits : null, // Send negative credits
        },
        {
          headers: {
            Authorization: "Bearer " + auth.token,
          },
        }
      );
      if (response.status === 200) {
        toast.success("Credits updated");
        setIsCreditsDialogOpen(false); // Close the dialog on success
        setCredits(null); // Reset credits to null after updating
        onCreditsUpdate(); // Invoke the callback after successful update
        setIsLoading(false);
      }
    } catch (e: any) {
      setIsLoading(false);
      console.log(e);
      toast.error(e.response.data.error);
    }
  };

  return (
    <div className="">
      <Dialog open={isCreditsDialogOpen} onOpenChange={setIsCreditsDialogOpen}>
        <DialogTrigger asChild>
          <Button className="">
           Deduct Credits
          </Button>
        </DialogTrigger>
        <DialogContent className="sm:max-w-[425px]">
          <DialogDescription></DialogDescription>
          <DialogHeader>
            <DialogTitle> Deduct Credits</DialogTitle>
            <DialogDescription></DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Input
                value={credits === null ? '' : credits} // Handle null value appropriately
                onChange={(e) => setCredits(Number(e.target.value))}
                type="number"
                placeholder="Deduct Credits"
                className="col-span-4"
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              type="submit"
              onClick={() => {
                handleCreditsChange();
              }}
              className="w-full"
            >
              {isLoading ? "Saving..." : "Save"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}