import { useState } from "react";
import { useDataContext } from "@/context/DataContext";
import { useAuth } from "@/context/AuthContext";
import { Card } from "@/components/ui/card";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Search, Clock, FileText } from "lucide-react";
import UploadSearchUrl from "./Search";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import Payment from "@/components/payment/SidebarPayment";
import DataTable from "./dataTable";
import InvoiceTable from "@/components/creditHistory/creditHistoryDataTable";

export default function Dashboard() {
  const { globalCredits, setGlobalCredits } = useDataContext();
  const auth = useAuth();

  return (
    <>
      <div className=" mx-auto px-4 py-2">
        <div className="flex flex-col lg:flex-row gap-3">
          <div className="lg:w-[26rem]  lg:h-[calc(100vh-72px)] overflow-y-scroll flex flex-col gap-4">
            <Card className="p-6 border-none rounded-xl shadow-none">
              <h2 className="text-lg font-semibold mb-4">Get more Leads</h2>
              <UploadSearchUrl />
            </Card>

            <Card className="px-6 pt-6 pb-2 border-none rounded-xl shadow-none">
              <Accordion
                type="single"
                collapsible
                className="w-full lg:max-w-72 border-none"
              >
                {/* Add Credits Accordion */}
                <AccordionItem value="add-credits border-none">
                  <AccordionTrigger className="text-lg font-semibold py-0 mb-2">
                    Add Credits
                  </AccordionTrigger>
                  <AccordionContent>
                    <Payment />
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            </Card>

            <Card className="p-6 border-none rounded-xl shadow-none lg:flex flex-col hidden">
              <h2 className="text-lg font-semibold mb-4">FAQ</h2>
              {/* <div className="space-y-4">
                <div className="p-4 bg-orange-50 rounded-lg">
                  <div className="flex items-center text-orange-500 mb-2">
                    <FileText className="h-4 w-4 mr-2" />
                    <span className="text-sm">Article • 3 Min read</span>
                  </div>
                  <h3 className="font-medium mb-2">
                    10 Tips to Optimise your Email Infrastructure
                  </h3>
                </div>
                <div className="p-4 bg-orange-50 rounded-lg">
                  <div className="flex items-center text-orange-500 mb-2">
                    <Clock className="h-4 w-4 mr-2" />
                    <span className="text-sm">Video • 3 Min read</span>
                  </div>
                  <h3 className="font-medium">
                    New Growth Hacks from Searchleads
                  </h3>
                </div>
              </div> */}
              <div className="">
                <div className="flex flex-col items-center"></div>

                <Accordion type="single" collapsible className="">
                  {accordionData.map((item) => (
                    <AccordionItem key={item.value} value={item.value}>
                      <AccordionTrigger className="py-2 flex items-center justify-between text-start">
                        {item.question}</AccordionTrigger>
                      <AccordionContent>{item.answer}</AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>
              </div>
            </Card>
          </div>

          <div className="bg-white rounded-xl pt-4 px-3 pb-0 w-full lg:overflow-hidden">
            <Tabs defaultValue="purchased">
              <TabsList className="rounded-lg bg-[#F5F5F5] py-0 px-1 h-9 text-[#A8A29E] space-x-2">
                <TabsTrigger
                  value="purchased"
                  className="data-[state=active]:bg-[#F28100] data-[state=active]:text-white
                 rounded-lg font-semibold "
                >
                  Purchased Leads
                  {/* <span className="ml-2 bg-gray-100 px-2 py-0.5 rounded-full text-sm">
                    123
                  </span> */}
                </TabsTrigger>
                <TabsTrigger
                  value="invoices"
                  className="data-[state=active]:bg-[#F28100] data-[state=active]:text-white
                 rounded-lg font-semibold "
                >
                  Invoices
                  {/* <span className="ml-2 bg-gray-100 px-2 py-0.5 rounded-full text-sm">
                    48
                  </span> */}
                </TabsTrigger>
              </TabsList>
              <TabsContent value="purchased" className="">
                <div className="">
                  <DataTable />
                </div>
              </TabsContent>
              <TabsContent value="invoices">
                <div className="">
                  <InvoiceTable />
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </>
  );
}

const accordionData = [
  {
    value: "item-1",
    question: "Do I need a paid Apollo account?",
    answer:
      "No, a paid Apollo account is not required. We recommend signing up for a free Apollo account so you can apply filters and copy the Apollo URL for our service.",
  },
  {
    value: "item-2",
    question: "How does it work?",
    answer:
      "Go to Apollo.io and apply the filters you need. Once you've done that, copy the URL from your browser's address bar and submit it through our form. After submission, wait for about 5 minutes. The leads will be delivered directly to a table within your dashboard, making it easy for you to view and manage them.",
  },
  {
    value: "item-3",
    question: "How long does it take to receive the file?",
    answer:
      "After making the payment and submitting the form, the data will typically be available in your dashboard within 15 minutes to an hour, depending on the number of leads you've requested.",
  },
  {
    value: "item-4",
    question: "Are you directly scraping from the Apollo database?",
    answer:
      "Yes, we directly scrape leads from the Apollo database for each request. After scraping, we verify the emails, enrich any invalid emails using our custom solution, and also provide personal mobile numbers and personal emails for your leads.",
  },
  {
    value: "item-5",
    question: "How do I contact support?",
    answer:
      "If you need any assistance, please email <NAME_EMAIL> or use the chat support option on our website.",
  },
  {
    value: "item-6",
    question: "Do you offer partnerships, and can you build custom solutions?",
    answer:
      "Yes, we offer partnerships and have experience building custom solutions for agencies, SaaS companies, and enterprise clients. For more details, please email <NAME_EMAIL>.",
  },
];
