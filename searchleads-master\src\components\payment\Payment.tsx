/* eslint-disable @typescript-eslint/no-unused-vars */
"use client";
import React, { useState, useEffect } from "react";
import { loadStripe } from "@stripe/stripe-js";
import { Elements } from "@stripe/react-stripe-js";
import axios from "@/axois";
import { useAuth } from "@/context/AuthContext";
import PaymentForm from "./StripePaymentForm"; // Import the PaymentForm component
import Stripe from "stripe";
import {
  Dialog,
  DialogTrigger,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Toaster, toast } from "sonner";

declare global {
  interface Window {
    Afficone?: {
      referral?: string;
    };
  }
}

const stripe = new Stripe(import.meta.env.VITE_STRIPE_SECRET_KEY!, {
  typescript: true,
  apiVersion: "2024-06-20",
});

const stripePromise = loadStripe(
  `${import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY! as string}`
);

interface PaymentComponentProps {
  credits: string;
  currency: string;
  maxAmount: string;
  customerId: string;
  beforeDiscount: string;
  onPaymentSuccess: () => void;
  clientSecret: string;
  isDialogOpen: boolean;
  setIsDialogOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

const PaymentComponent: React.FC<PaymentComponentProps> = ({
  credits,
  currency,
  maxAmount,
  customerId,
  beforeDiscount,
  onPaymentSuccess,
  clientSecret,
  isDialogOpen,
  setIsDialogOpen,
}) => {
  const auth = useAuth();
  const userId = auth.user?.UserID;

  const handleDialogClose = async (isPaymentSuccessful = false) => {
    if (!isPaymentSuccessful && clientSecret) {
      const paymentIntentId = clientSecret.split("_secret")[0];

      try {
        const response = await axios.post(
          "/api/payments/paymentCancelledIntent",
          { paymentIntentId, cancellationReason: "requested_by_customer" },
          {
            headers: {
              Authorization: `Bearer ${auth.session?.access_token}`,
            },
          }
        );

        const cancelledPaymentIntent = response.data.paymentIntent;

        if (cancelledPaymentIntent) {
          console.log(
            "Payment successfully cancelled:",
            cancelledPaymentIntent
          );
          toast(`Payment is cancelled!`, {
            action: {
              label: "Okay",
              onClick: () => console.log("Okay"),
            },
            duration: 5000,
          });
        } else {
          console.error("Error cancelling payment intent");
        }
      } catch (error: any) {
        console.error("Error cancelling payment intent:", error.message);
      }
    }
    setIsDialogOpen(false);
  };

  const handlePaymentSuccess = (isSuccessful: boolean) => {
    if (isSuccessful) {
      onPaymentSuccess();
      toast(` Payment is successfull! `, {
        action: {
          label: "Okay",
          onClick: () => console.log("Okay"),
        },
        duration: 5000,
      });
    }
    handleDialogClose(isSuccessful);
  };

  const getCurrencySymbol = (currency: string): string => {
    const currencySymbols: { [key: string]: string } = {
      eur: "€",
      usd: "$",
      inr: "₹",
      gbp: "£",
    };
    return currencySymbols[currency] || "";
  };

  const appearance = {
    theme: "flat",

    rules: {
      ".Input": {
        padding: "8px",
      },
      ".Label": {
        paddingTop: "8px",
        paddingBottom: "5px",
        fontSize: "16px",
        fontWeight: "600",
      },
    },
  };

  return (
    <>
      <div className="mb-6 mx-10">
        <Dialog open={isDialogOpen} onOpenChange={handleDialogClose}>
          {/* <DialogTrigger asChild>
            <Button
              onClick={async () => {
                // await fetchClientSecret();
                setIsDialogOpen(true);
              }}
            >
              Continue to pay {getCurrencySymbol(currency)} {maxAmount}
            </Button>
          </DialogTrigger> */}
          <DialogContent
            onInteractOutside={(e) => {
              e.preventDefault();
            }}
            //  className="fixed inset-0 bg-white rounded-lg shadow-lg max-w-lg mx-auto my-auto z-50 p-6"
          >
            <DialogHeader>
              <DialogTitle>Payment</DialogTitle>
              <DialogDescription>Complete your payment below</DialogDescription>
            </DialogHeader>
            {clientSecret && (
              <Elements
                stripe={stripePromise}
                options={{
                  clientSecret,
                  appearance: { ...appearance, theme: "stripe" as const },
                }}
              >
                {/* <p className="text-2xl">
                  {getCurrencySymbol(currency)}
                  {maxAmount}
                  {beforeDiscount}
                </p> */}
                <p className="text-2xl">
                  {parseFloat(maxAmount) === parseFloat(beforeDiscount) ? (
                    <>
                      {getCurrencySymbol(currency)}
                      {maxAmount}
                    </>
                  ) : (
                    <>
                      <span className="text-gray-500 line-through">
                        {getCurrencySymbol(currency)}
                        {beforeDiscount}
                      </span>{" "}
                      {getCurrencySymbol(currency)}
                      {maxAmount}
                    </>
                  )}
                </p>

                <PaymentForm onPaymentSuccess={handlePaymentSuccess} />
              </Elements>
            )}
          </DialogContent>
        </Dialog>
      </div>
      {/* <Toaster richColors /> */}
    </>
  );
};

export default PaymentComponent;
