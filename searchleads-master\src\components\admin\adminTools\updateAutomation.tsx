/* eslint-disable @typescript-eslint/no-explicit-any */
import React from "react";
("use client");
import { useAdminAuth } from "@/context/AdminAuthContext";
import { toast } from "react-toastify";
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Workflow } from "lucide-react";

import axios from "@/axois";

export default function UpdateAutomation() {
  const auth = useAdminAuth();

  const [automationLink, setAutomationLink] = useState("");
  const [isAutomationDialogOpen, setIsAutomationDialogOpen] = useState(false);

  const handleAutomationLinkChange = async () => {
    try {
      const response = await axios.post(
        "/api/admin/changeAutomationLink",
        {
          automationLink,
        },
        {
          headers: {
            Authorization: "Bearer " + auth.token,
          },
        }
      );
      if (response.status === 200) {
        toast.success("Automation Link updated");
        setIsAutomationDialogOpen(false); // Close the dialog on success
        setAutomationLink("");
      }
    } catch (e: any) {
      console.log(e);
      toast.error(e.response.data.error);
    }
  };

  return (
    <div className="mb-2 flex justify-center items-center">
      <Dialog
        open={isAutomationDialogOpen}
        onOpenChange={setIsAutomationDialogOpen}
      >
        <DialogTrigger asChild>
          <Button variant="outline" className="w-fit h-fit p-6">
            <div className="flex justify-center items-center">
              <Workflow className="mr-4 w-14 h-14 md:w-20 md:h-20" />
              <div className="flex flex-col justify-center items-center ">
                <p className="text-base md:text-xl font-medium">
                  Change <br />
                  Automation <br />
                  Link
                </p>
              </div>
            </div>
          </Button>
        </DialogTrigger>
        <DialogContent className="sm:max-w-[425px]">
          <DialogDescription></DialogDescription>
          <DialogHeader>
            <DialogTitle> Update Automation Link</DialogTitle>
            <DialogDescription></DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Input
                value={automationLink}
                onChange={(e) => setAutomationLink(e.target.value)}
                type="text"
                placeholder="Automation Link"
                className="col-span-4"
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              type="submit"
              onClick={() => {
                handleAutomationLinkChange();
              }}
              className="w-full"
            >
              Save
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
