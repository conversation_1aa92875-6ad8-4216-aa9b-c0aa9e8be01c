export const leftAnimation = {
    hidden: { opacity: 0, x: -100 },
    visible: { opacity: 1, x: 0,transition: {
        type: 'spring',
        stiffness: 300,
        damping: 15,
    } }
};

export const rightAnimation = {
    hidden: { opacity: 0, x: 100 },
    visible: { opacity: 1, x: 0 ,transition: {
        type: 'spring',
        stiffness: 300,
        damping: 15,
    }}
};

export const bottomAnimation = {
    hidden: { opacity: 0, y: 100 },
    visible: {
        opacity: 1, y: 0, transition: {
            type: 'spring',
            stiffness: 300,
            damping: 15,
        }
    }
};

export const topAnimation = {
    hidden: { opacity: 0, y: -100 },
    visible: { opacity: 1, y: 0, transition: {
        type: 'spring',
        stiffness: 300,
        damping: 15,
    } }
}


export const topLeftDiagonalAnimation = {
    hidden: { opacity: 0, x: -100, y: -100 },
    visible: {
        opacity: 1, x: 0, y: 0,
        transition: {
            type: 'spring',
            stiffness: 300,
            damping: 15,
        }
    }
};

export const topRightDiagonalAnimation = {
    hidden: { opacity: 0, x: 100, y: -100 },
    visible: {
        opacity: 1, x: 0, y: 0,
        transition: {
            type: 'spring',
            stiffness: 300,
            damping: 15,
        }
    }
};

export const bottomLeftDiagonalAnimation = {
    hidden: { opacity: 0, x: -100, y: 100 },
    visible: {
        opacity: 1, x: 0, y: 0,
        transition: {
            type: 'spring',
            stiffness: 300,
            damping: 15,
        }
    }
};

export const bottomRightDiagonalAnimation = {
    hidden: { opacity: 0, x: 100, y: 100 },
    visible: {
        opacity: 1, x: 0, y: 0,
        transition: {
            type: 'spring',
            stiffness: 300,
            damping: 15,
        }
    }
};



