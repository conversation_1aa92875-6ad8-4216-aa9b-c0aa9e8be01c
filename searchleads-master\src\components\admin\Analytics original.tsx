import DataTable from "./userLogsList/dataTable";
import React, { useEffect, useState } from "react";
import { Bar } from "react-chartjs-2";
import { useAdminAuth } from "@/context/AdminAuthContext";
import axios from "@/axois";

import {
  FaChevronLeft,
  FaChevronRight,
  FaCalendarDay,
  FaCalendarAlt,
} from "react-icons/fa";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";
import dayjs from "dayjs";
import isBetween from "dayjs/plugin/isBetween";

interface RowData {
  date: string;
}

dayjs.extend(isBetween);
// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

export default function Analytics() {
  const auth = useAdminAuth();
  const [rows, setRows] = useState<RowData[]>([]); // Replace with your actual data fetch
  const [loading, setLoading] = useState(false);
  const [viewType, setViewType] = useState<"daily" | "monthly">("daily");
  const [startDate, setStartDate] = useState(
    dayjs().subtract(6, "day").format("YYYY-MM-DD")
  );
  const [endDate, setEndDate] = useState(dayjs().format("YYYY-MM-DD"));

  const getAllUser = async () => {
    try {
      setLoading(true);
      const response = await axios.get("/api/admin/getAllUsers", {
        headers: {
          Authorization: `Bearer ${auth.token}`,
        },
      });
      if (response.status === 200) {
        setRows(response.data.resp);
        setLoading(false);
        console.log(response.data.resp);
      }
    } catch (e: any) {
      console.log(e);
      setLoading(false);
    }
  };

  useEffect(() => {
    getAllUser();
  }, []);

  // Group data by date
  const groupDataByDate = (rows: RowData[]) => {
    return rows.reduce((acc: Record<string, number>, row) => {
      const date = dayjs(row.date).format("YYYY-MM-DD");
      acc[date] = (acc[date] || 0) + 1;
      return acc;
    }, {});
  };

  // Group data by month
  const groupDataByMonth = (rows: RowData[]) => {
    return rows.reduce((acc: Record<string, number>, row) => {
      const month = dayjs(row.date).format("YYYY-MM");
      acc[month] = (acc[month] || 0) + 1;
      return acc;
    }, {});
  };

  // Handle daily data range
  const getDailyData = (
    start: string,
    end: string,
    groupedData: Record<string, number>
  ) => {
    const range = [];
    let current = dayjs(start);

    while (current.isBefore(end) || current.isSame(end)) {
      const date = current.format("YYYY-MM-DD");
      range.push({ date, count: groupedData[date] || 0 });
      current = current.add(1, "day");
    }

    return range;
  };

  // Handle monthly data
  const getMonthlyData = (
    start: string,
    end: string,
    groupedData: Record<string, number>
  ) => {
    const range = [];
    let current = dayjs(start).startOf("month");

    while (
      current.isBefore(dayjs(end).endOf("month")) ||
      current.isSame(dayjs(end).endOf("month"))
    ) {
      const month = current.format("YYYY-MM");
      range.push({ date: month, count: groupedData[month] || 0 });
      current = current.add(1, "month");
    }

    return range;
  };

  // Get chart data based on view type
  const groupedData =
    viewType === "daily" ? groupDataByDate(rows) : groupDataByMonth(rows);
  const chartData =
    viewType === "daily"
      ? getDailyData(startDate, endDate, groupedData)
      : getMonthlyData(startDate, endDate, groupedData);

  const labels = chartData.map((data) =>
    viewType === "daily"
      ? dayjs(data.date).format("MMM D")
      : dayjs(data.date).format("MMM YYYY")
  );

  const handlePrev = () => {
    if (viewType === "daily") {
      setStartDate(dayjs(startDate).subtract(7, "day").format("YYYY-MM-DD"));
      setEndDate(dayjs(endDate).subtract(7, "day").format("YYYY-MM-DD"));
    } else {
      setStartDate(
        dayjs(startDate)
          .subtract(1, "month")
          .startOf("month")
          .format("YYYY-MM-DD")
      );
      setEndDate(
        dayjs(startDate)
          .subtract(1, "month")
          .endOf("month")
          .format("YYYY-MM-DD")
      );
    }
  };

  const handleNext = () => {
    if (viewType === "daily") {
      setStartDate(dayjs(startDate).add(7, "day").format("YYYY-MM-DD"));
      setEndDate(dayjs(endDate).add(7, "day").format("YYYY-MM-DD"));
    } else {
      setStartDate(
        dayjs(startDate).add(1, "month").startOf("month").format("YYYY-MM-DD")
      );
      setEndDate(
        dayjs(startDate).add(1, "month").endOf("month").format("YYYY-MM-DD")
      );
    }
  };

  const handleViewTypeChange = (view: "daily" | "monthly") => {
    setViewType(view);

    if (view === "daily") {
      // When switching to daily, reset the range to the last 7 days
      setStartDate(dayjs().subtract(6, "day").format("YYYY-MM-DD"));
      setEndDate(dayjs().format("YYYY-MM-DD"));
    } else {
      // Keep the same range when switching to monthly
      // Optionally, you can set it to the start of the current month as a default
      setStartDate(dayjs().startOf("month").format("YYYY-MM-DD"));
      setEndDate(dayjs().endOf("month").format("YYYY-MM-DD"));
    }
  };

  return (
    <div className="mx-0 ml-0 md:ml-8 mt-10 mb-20">
      <div className="flex justify-between">
        <h1 className="font-medium tracking-tighter text-3xl mb-5 ">
          Analytics
        </h1>
        <p className="text-black">{loading}</p>
      </div>

      <div className="border rounded-lg shadow-md h-full">
        {loading ? (
          <>
            <p>loading</p>
          </>
        ) : (
          <div className="border rounded-lg shadow-lg h-full p-4 bg-white">
            <div className="mb-4">
              <h2 className="text-lg font-semibold text-gray-700 flex items-center gap-2">
                User Signups ({viewType === "daily" ? "Daily" : "Monthly"})
              </h2>
            </div>

            <div>
              <Bar
                data={{
                  labels,
                  datasets: [
                    {
                      label: "Signups",
                      data: chartData.map((d) => d.count),
                      backgroundColor: "rgba(75, 192, 192, 0.6)",
                      borderColor: "rgba(75, 192, 192, 1)",
                      borderWidth: 1,
                    },
                  ],
                }}
              />
            </div>

            <div className="flex flex-col sm:flex-row items-center justify-between mt-6 gap-4">
              <div className="flex gap-4">
                <button
                  className={`flex items-center gap-2 px-4 py-2 text-sm font-medium text-white bg-blue-500 hover:bg-blue-600 rounded-lg transition`}
                  onClick={() => handleViewTypeChange("daily")}
                >
                  <FaCalendarDay /> Daily View
                </button>
                <button
                  className={`flex items-center gap-2 px-4 py-2 text-sm font-medium text-white bg-blue-500 hover:bg-blue-600 rounded-lg transition`}
                  onClick={() => handleViewTypeChange("monthly")}
                >
                  <FaCalendarAlt /> Monthly View
                </button>
              </div>

              <div className="flex gap-4">
                <button
                  className="flex items-center gap-2 px-4 py-2 text-sm font-medium text-gray-600 bg-gray-200 hover:bg-gray-300 rounded-lg transition"
                  onClick={handlePrev}
                >
                  <FaChevronLeft /> Previous
                </button>
                <button
                  className="flex items-center gap-2 px-4 py-2 text-sm font-medium text-gray-600 bg-gray-200 hover:bg-gray-300 rounded-lg transition"
                  onClick={handleNext}
                  disabled={dayjs(
                    chartData[chartData.length - 1]?.date
                  ).isAfter(dayjs())}
                >
                  Next <FaChevronRight />
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
