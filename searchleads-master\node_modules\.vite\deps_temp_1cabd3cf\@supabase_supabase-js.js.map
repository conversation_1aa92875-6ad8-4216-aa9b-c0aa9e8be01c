{"version": 3, "sources": ["../../@supabase/postgrest-js/src/PostgrestError.ts", "../../@supabase/postgrest-js/src/PostgrestBuilder.ts", "../../@supabase/postgrest-js/src/PostgrestTransformBuilder.ts", "../../@supabase/postgrest-js/src/PostgrestFilterBuilder.ts", "../../@supabase/postgrest-js/src/PostgrestQueryBuilder.ts", "../../@supabase/postgrest-js/src/version.ts", "../../@supabase/postgrest-js/src/constants.ts", "../../@supabase/postgrest-js/src/PostgrestClient.ts", "../../@supabase/postgrest-js/src/index.ts", "../../@supabase/functions-js/src/helper.ts", "../../@supabase/functions-js/src/types.ts", "../../@supabase/functions-js/src/FunctionsClient.ts", "../../@supabase/postgrest-js/dist/esm/wrapper.mjs", "../../@supabase/realtime-js/src/lib/version.ts", "../../@supabase/realtime-js/src/lib/constants.ts", "../../@supabase/realtime-js/src/lib/serializer.ts", "../../@supabase/realtime-js/src/lib/timer.ts", "../../@supabase/realtime-js/src/lib/transformers.ts", "../../@supabase/realtime-js/src/lib/push.ts", "../../@supabase/realtime-js/src/RealtimePresence.ts", "../../@supabase/realtime-js/src/RealtimeChannel.ts", "../../@supabase/realtime-js/src/RealtimeClient.ts", "../../@supabase/storage-js/src/lib/errors.ts", "../../@supabase/storage-js/src/lib/helpers.ts", "../../@supabase/storage-js/src/lib/fetch.ts", "../../@supabase/storage-js/src/packages/StorageFileApi.ts", "../../@supabase/storage-js/src/lib/version.ts", "../../@supabase/storage-js/src/lib/constants.ts", "../../@supabase/storage-js/src/packages/StorageBucketApi.ts", "../../@supabase/storage-js/src/StorageClient.ts", "../../@supabase/supabase-js/src/lib/version.ts", "../../@supabase/supabase-js/src/lib/constants.ts", "../../@supabase/supabase-js/src/lib/fetch.ts", "../../@supabase/supabase-js/src/lib/helpers.ts", "../../@supabase/auth-js/src/lib/version.ts", "../../@supabase/auth-js/src/lib/constants.ts", "../../@supabase/auth-js/src/lib/helpers.ts", "../../@supabase/auth-js/src/lib/errors.ts", "../../@supabase/auth-js/src/lib/fetch.ts", "../../@supabase/auth-js/src/GoTrueAdminApi.ts", "../../@supabase/auth-js/src/lib/local-storage.ts", "../../@supabase/auth-js/src/lib/polyfills.ts", "../../@supabase/auth-js/src/lib/locks.ts", "../../@supabase/auth-js/src/GoTrueClient.ts", "../../@supabase/auth-js/src/AuthAdminApi.ts", "../../@supabase/auth-js/src/AuthClient.ts", "../../@supabase/supabase-js/src/lib/SupabaseAuthClient.ts", "../../@supabase/supabase-js/src/SupabaseClient.ts", "../../@supabase/supabase-js/src/index.ts"], "sourcesContent": ["import type { PostgrestError as IPostgrestError } from './types'\n\nexport default class PostgrestError extends Error implements IPostgrestError {\n  details: string\n  hint: string\n  code: string\n\n  constructor(context: IPostgrestError) {\n    super(context.message)\n    this.name = 'PostgrestError'\n    this.details = context.details\n    this.hint = context.hint\n    this.code = context.code\n  }\n}\n", "// @ts-ignore\nimport nodeFetch from '@supabase/node-fetch'\n\nimport type { Fetch, PostgrestSingleResponse } from './types'\nimport PostgrestError from './PostgrestError'\n\nexport default abstract class PostgrestBuilder<Result>\n  implements PromiseLike<PostgrestSingleResponse<Result>>\n{\n  protected method: 'GET' | 'HEAD' | 'POST' | 'PATCH' | 'DELETE'\n  protected url: URL\n  protected headers: Record<string, string>\n  protected schema?: string\n  protected body?: unknown\n  protected shouldThrowOnError = false\n  protected signal?: AbortSignal\n  protected fetch: Fetch\n  protected isMaybeSingle: boolean\n\n  constructor(builder: PostgrestBuilder<Result>) {\n    this.method = builder.method\n    this.url = builder.url\n    this.headers = builder.headers\n    this.schema = builder.schema\n    this.body = builder.body\n    this.shouldThrowOnError = builder.shouldThrowOnError\n    this.signal = builder.signal\n    this.isMaybeSingle = builder.isMaybeSingle\n\n    if (builder.fetch) {\n      this.fetch = builder.fetch\n    } else if (typeof fetch === 'undefined') {\n      this.fetch = nodeFetch\n    } else {\n      this.fetch = fetch\n    }\n  }\n\n  /**\n   * If there's an error with the query, throwOnError will reject the promise by\n   * throwing the error instead of returning it as part of a successful response.\n   *\n   * {@link https://github.com/supabase/supabase-js/issues/92}\n   */\n  throwOnError(): this {\n    this.shouldThrowOnError = true\n    return this\n  }\n\n  /**\n   * Set an HTTP header for the request.\n   */\n  setHeader(name: string, value: string): this {\n    this.headers = { ...this.headers }\n    this.headers[name] = value\n    return this\n  }\n\n  then<TResult1 = PostgrestSingleResponse<Result>, TResult2 = never>(\n    onfulfilled?:\n      | ((value: PostgrestSingleResponse<Result>) => TResult1 | PromiseLike<TResult1>)\n      | undefined\n      | null,\n    onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null\n  ): PromiseLike<TResult1 | TResult2> {\n    // https://postgrest.org/en/stable/api.html#switching-schemas\n    if (this.schema === undefined) {\n      // skip\n    } else if (['GET', 'HEAD'].includes(this.method)) {\n      this.headers['Accept-Profile'] = this.schema\n    } else {\n      this.headers['Content-Profile'] = this.schema\n    }\n    if (this.method !== 'GET' && this.method !== 'HEAD') {\n      this.headers['Content-Type'] = 'application/json'\n    }\n\n    // NOTE: Invoke w/o `this` to avoid illegal invocation error.\n    // https://github.com/supabase/postgrest-js/pull/247\n    const _fetch = this.fetch\n    let res = _fetch(this.url.toString(), {\n      method: this.method,\n      headers: this.headers,\n      body: JSON.stringify(this.body),\n      signal: this.signal,\n    }).then(async (res) => {\n      let error = null\n      let data = null\n      let count: number | null = null\n      let status = res.status\n      let statusText = res.statusText\n\n      if (res.ok) {\n        if (this.method !== 'HEAD') {\n          const body = await res.text()\n          if (body === '') {\n            // Prefer: return=minimal\n          } else if (this.headers['Accept'] === 'text/csv') {\n            data = body\n          } else if (\n            this.headers['Accept'] &&\n            this.headers['Accept'].includes('application/vnd.pgrst.plan+text')\n          ) {\n            data = body\n          } else {\n            data = JSON.parse(body)\n          }\n        }\n\n        const countHeader = this.headers['Prefer']?.match(/count=(exact|planned|estimated)/)\n        const contentRange = res.headers.get('content-range')?.split('/')\n        if (countHeader && contentRange && contentRange.length > 1) {\n          count = parseInt(contentRange[1])\n        }\n\n        // Temporary partial fix for https://github.com/supabase/postgrest-js/issues/361\n        // Issue persists e.g. for `.insert([...]).select().maybeSingle()`\n        if (this.isMaybeSingle && this.method === 'GET' && Array.isArray(data)) {\n          if (data.length > 1) {\n            error = {\n              // https://github.com/PostgREST/postgrest/blob/a867d79c42419af16c18c3fb019eba8df992626f/src/PostgREST/Error.hs#L553\n              code: 'PGRST116',\n              details: `Results contain ${data.length} rows, application/vnd.pgrst.object+json requires 1 row`,\n              hint: null,\n              message: 'JSON object requested, multiple (or no) rows returned',\n            }\n            data = null\n            count = null\n            status = 406\n            statusText = 'Not Acceptable'\n          } else if (data.length === 1) {\n            data = data[0]\n          } else {\n            data = null\n          }\n        }\n      } else {\n        const body = await res.text()\n\n        try {\n          error = JSON.parse(body)\n\n          // Workaround for https://github.com/supabase/postgrest-js/issues/295\n          if (Array.isArray(error) && res.status === 404) {\n            data = []\n            error = null\n            status = 200\n            statusText = 'OK'\n          }\n        } catch {\n          // Workaround for https://github.com/supabase/postgrest-js/issues/295\n          if (res.status === 404 && body === '') {\n            status = 204\n            statusText = 'No Content'\n          } else {\n            error = {\n              message: body,\n            }\n          }\n        }\n\n        if (error && this.isMaybeSingle && error?.details?.includes('0 rows')) {\n          error = null\n          status = 200\n          statusText = 'OK'\n        }\n\n        if (error && this.shouldThrowOnError) {\n          throw new PostgrestError(error)\n        }\n      }\n\n      const postgrestResponse = {\n        error,\n        data,\n        count,\n        status,\n        statusText,\n      }\n\n      return postgrestResponse\n    })\n    if (!this.shouldThrowOnError) {\n      res = res.catch((fetchError) => ({\n        error: {\n          message: `${fetchError?.name ?? 'FetchError'}: ${fetchError?.message}`,\n          details: `${fetchError?.stack ?? ''}`,\n          hint: '',\n          code: `${fetchError?.code ?? ''}`,\n        },\n        data: null,\n        count: null,\n        status: 0,\n        statusText: '',\n      }))\n    }\n\n    return res.then(onfulfilled, onrejected)\n  }\n}\n", "import PostgrestBuilder from './PostgrestBuilder'\nimport { GetResult } from './select-query-parser'\nimport { GenericSchema } from './types'\n\nexport default class PostgrestTransformBuilder<\n  Schema extends GenericSchema,\n  Row extends Record<string, unknown>,\n  Result,\n  RelationName = unknown,\n  Relationships = unknown\n> extends PostgrestBuilder<Result> {\n  /**\n   * Perform a SELECT on the query result.\n   *\n   * By default, `.insert()`, `.update()`, `.upsert()`, and `.delete()` do not\n   * return modified rows. By calling this method, modified rows are returned in\n   * `data`.\n   *\n   * @param columns - The columns to retrieve, separated by commas\n   */\n  select<\n    Query extends string = '*',\n    NewResultOne = GetResult<Schema, Row, RelationName, Relationships, Query>\n  >(\n    columns?: Query\n  ): PostgrestTransformBuilder<Schema, Row, NewResultOne[], RelationName, Relationships> {\n    // Remove whitespaces except when quoted\n    let quoted = false\n    const cleanedColumns = (columns ?? '*')\n      .split('')\n      .map((c) => {\n        if (/\\s/.test(c) && !quoted) {\n          return ''\n        }\n        if (c === '\"') {\n          quoted = !quoted\n        }\n        return c\n      })\n      .join('')\n    this.url.searchParams.set('select', cleanedColumns)\n    if (this.headers['Prefer']) {\n      this.headers['Prefer'] += ','\n    }\n    this.headers['Prefer'] += 'return=representation'\n    return this as unknown as PostgrestTransformBuilder<\n      Schema,\n      Row,\n      NewResultOne[],\n      RelationName,\n      Relationships\n    >\n  }\n\n  order<ColumnName extends string & keyof Row>(\n    column: ColumnName,\n    options?: { ascending?: boolean; nullsFirst?: boolean; referencedTable?: undefined }\n  ): this\n  order(\n    column: string,\n    options?: { ascending?: boolean; nullsFirst?: boolean; referencedTable?: string }\n  ): this\n  /**\n   * @deprecated Use `options.referencedTable` instead of `options.foreignTable`\n   */\n  order<ColumnName extends string & keyof Row>(\n    column: ColumnName,\n    options?: { ascending?: boolean; nullsFirst?: boolean; foreignTable?: undefined }\n  ): this\n  /**\n   * @deprecated Use `options.referencedTable` instead of `options.foreignTable`\n   */\n  order(\n    column: string,\n    options?: { ascending?: boolean; nullsFirst?: boolean; foreignTable?: string }\n  ): this\n  /**\n   * Order the query result by `column`.\n   *\n   * You can call this method multiple times to order by multiple columns.\n   *\n   * You can order referenced tables, but it only affects the ordering of the\n   * parent table if you use `!inner` in the query.\n   *\n   * @param column - The column to order by\n   * @param options - Named parameters\n   * @param options.ascending - If `true`, the result will be in ascending order\n   * @param options.nullsFirst - If `true`, `null`s appear first. If `false`,\n   * `null`s appear last.\n   * @param options.referencedTable - Set this to order a referenced table by\n   * its columns\n   * @param options.foreignTable - Deprecated, use `options.referencedTable`\n   * instead\n   */\n  order(\n    column: string,\n    {\n      ascending = true,\n      nullsFirst,\n      foreignTable,\n      referencedTable = foreignTable,\n    }: {\n      ascending?: boolean\n      nullsFirst?: boolean\n      foreignTable?: string\n      referencedTable?: string\n    } = {}\n  ): this {\n    const key = referencedTable ? `${referencedTable}.order` : 'order'\n    const existingOrder = this.url.searchParams.get(key)\n\n    this.url.searchParams.set(\n      key,\n      `${existingOrder ? `${existingOrder},` : ''}${column}.${ascending ? 'asc' : 'desc'}${\n        nullsFirst === undefined ? '' : nullsFirst ? '.nullsfirst' : '.nullslast'\n      }`\n    )\n    return this\n  }\n\n  /**\n   * Limit the query result by `count`.\n   *\n   * @param count - The maximum number of rows to return\n   * @param options - Named parameters\n   * @param options.referencedTable - Set this to limit rows of referenced\n   * tables instead of the parent table\n   * @param options.foreignTable - Deprecated, use `options.referencedTable`\n   * instead\n   */\n  limit(\n    count: number,\n    {\n      foreignTable,\n      referencedTable = foreignTable,\n    }: { foreignTable?: string; referencedTable?: string } = {}\n  ): this {\n    const key = typeof referencedTable === 'undefined' ? 'limit' : `${referencedTable}.limit`\n    this.url.searchParams.set(key, `${count}`)\n    return this\n  }\n\n  /**\n   * Limit the query result by starting at an offset `from` and ending at the offset `to`.\n   * Only records within this range are returned.\n   * This respects the query order and if there is no order clause the range could behave unexpectedly.\n   * The `from` and `to` values are 0-based and inclusive: `range(1, 3)` will include the second, third\n   * and fourth rows of the query.\n   *\n   * @param from - The starting index from which to limit the result\n   * @param to - The last index to which to limit the result\n   * @param options - Named parameters\n   * @param options.referencedTable - Set this to limit rows of referenced\n   * tables instead of the parent table\n   * @param options.foreignTable - Deprecated, use `options.referencedTable`\n   * instead\n   */\n  range(\n    from: number,\n    to: number,\n    {\n      foreignTable,\n      referencedTable = foreignTable,\n    }: { foreignTable?: string; referencedTable?: string } = {}\n  ): this {\n    const keyOffset =\n      typeof referencedTable === 'undefined' ? 'offset' : `${referencedTable}.offset`\n    const keyLimit = typeof referencedTable === 'undefined' ? 'limit' : `${referencedTable}.limit`\n    this.url.searchParams.set(keyOffset, `${from}`)\n    // Range is inclusive, so add 1\n    this.url.searchParams.set(keyLimit, `${to - from + 1}`)\n    return this\n  }\n\n  /**\n   * Set the AbortSignal for the fetch request.\n   *\n   * @param signal - The AbortSignal to use for the fetch request\n   */\n  abortSignal(signal: AbortSignal): this {\n    this.signal = signal\n    return this\n  }\n\n  /**\n   * Return `data` as a single object instead of an array of objects.\n   *\n   * Query result must be one row (e.g. using `.limit(1)`), otherwise this\n   * returns an error.\n   */\n  single<\n    ResultOne = Result extends (infer ResultOne)[] ? ResultOne : never\n  >(): PostgrestBuilder<ResultOne> {\n    this.headers['Accept'] = 'application/vnd.pgrst.object+json'\n    return this as PostgrestBuilder<ResultOne>\n  }\n\n  /**\n   * Return `data` as a single object instead of an array of objects.\n   *\n   * Query result must be zero or one row (e.g. using `.limit(1)`), otherwise\n   * this returns an error.\n   */\n  maybeSingle<\n    ResultOne = Result extends (infer ResultOne)[] ? ResultOne : never\n  >(): PostgrestBuilder<ResultOne | null> {\n    // Temporary partial fix for https://github.com/supabase/postgrest-js/issues/361\n    // Issue persists e.g. for `.insert([...]).select().maybeSingle()`\n    if (this.method === 'GET') {\n      this.headers['Accept'] = 'application/json'\n    } else {\n      this.headers['Accept'] = 'application/vnd.pgrst.object+json'\n    }\n    this.isMaybeSingle = true\n    return this as PostgrestBuilder<ResultOne | null>\n  }\n\n  /**\n   * Return `data` as a string in CSV format.\n   */\n  csv(): PostgrestBuilder<string> {\n    this.headers['Accept'] = 'text/csv'\n    return this as PostgrestBuilder<string>\n  }\n\n  /**\n   * Return `data` as an object in [GeoJSON](https://geojson.org) format.\n   */\n  geojson(): PostgrestBuilder<Record<string, unknown>> {\n    this.headers['Accept'] = 'application/geo+json'\n    return this as PostgrestBuilder<Record<string, unknown>>\n  }\n\n  /**\n   * Return `data` as the EXPLAIN plan for the query.\n   *\n   * You need to enable the\n   * [db_plan_enabled](https://supabase.com/docs/guides/database/debugging-performance#enabling-explain)\n   * setting before using this method.\n   *\n   * @param options - Named parameters\n   *\n   * @param options.analyze - If `true`, the query will be executed and the\n   * actual run time will be returned\n   *\n   * @param options.verbose - If `true`, the query identifier will be returned\n   * and `data` will include the output columns of the query\n   *\n   * @param options.settings - If `true`, include information on configuration\n   * parameters that affect query planning\n   *\n   * @param options.buffers - If `true`, include information on buffer usage\n   *\n   * @param options.wal - If `true`, include information on WAL record generation\n   *\n   * @param options.format - The format of the output, can be `\"text\"` (default)\n   * or `\"json\"`\n   */\n  explain({\n    analyze = false,\n    verbose = false,\n    settings = false,\n    buffers = false,\n    wal = false,\n    format = 'text',\n  }: {\n    analyze?: boolean\n    verbose?: boolean\n    settings?: boolean\n    buffers?: boolean\n    wal?: boolean\n    format?: 'json' | 'text'\n  } = {}): PostgrestBuilder<Record<string, unknown>[]> | PostgrestBuilder<string> {\n    const options = [\n      analyze ? 'analyze' : null,\n      verbose ? 'verbose' : null,\n      settings ? 'settings' : null,\n      buffers ? 'buffers' : null,\n      wal ? 'wal' : null,\n    ]\n      .filter(Boolean)\n      .join('|')\n    // An Accept header can carry multiple media types but postgrest-js always sends one\n    const forMediatype = this.headers['Accept'] ?? 'application/json'\n    this.headers[\n      'Accept'\n    ] = `application/vnd.pgrst.plan+${format}; for=\"${forMediatype}\"; options=${options};`\n    if (format === 'json') return this as PostgrestBuilder<Record<string, unknown>[]>\n    else return this as PostgrestBuilder<string>\n  }\n\n  /**\n   * Rollback the query.\n   *\n   * `data` will still be returned, but the query is not committed.\n   */\n  rollback(): this {\n    if ((this.headers['Prefer'] ?? '').trim().length > 0) {\n      this.headers['Prefer'] += ',tx=rollback'\n    } else {\n      this.headers['Prefer'] = 'tx=rollback'\n    }\n    return this\n  }\n\n  /**\n   * Override the type of the returned `data`.\n   *\n   * @typeParam NewResult - The new result type to override with\n   */\n  returns<NewResult>(): PostgrestTransformBuilder<\n    Schema,\n    Row,\n    NewResult,\n    RelationName,\n    Relationships\n  > {\n    return this as unknown as PostgrestTransformBuilder<\n      Schema,\n      Row,\n      NewResult,\n      RelationName,\n      Relationships\n    >\n  }\n}\n", "import PostgrestTransformBuilder from './PostgrestTransformBuilder'\nimport { GenericSchema } from './types'\n\ntype FilterOperator =\n  | 'eq'\n  | 'neq'\n  | 'gt'\n  | 'gte'\n  | 'lt'\n  | 'lte'\n  | 'like'\n  | 'ilike'\n  | 'is'\n  | 'in'\n  | 'cs'\n  | 'cd'\n  | 'sl'\n  | 'sr'\n  | 'nxl'\n  | 'nxr'\n  | 'adj'\n  | 'ov'\n  | 'fts'\n  | 'plfts'\n  | 'phfts'\n  | 'wfts'\n\nexport default class PostgrestFilterBuilder<\n  Schema extends GenericSchema,\n  Row extends Record<string, unknown>,\n  Result,\n  RelationName = unknown,\n  Relationships = unknown\n> extends PostgrestTransformBuilder<Schema, Row, Result, RelationName, Relationships> {\n  eq<ColumnName extends string & keyof Row>(\n    column: ColumnName,\n    value: NonNullable<Row[ColumnName]>\n  ): this\n  eq<Value extends unknown>(column: string, value: NonNullable<Value>): this\n  /**\n   * Match only rows where `column` is equal to `value`.\n   *\n   * To check if the value of `column` is NULL, you should use `.is()` instead.\n   *\n   * @param column - The column to filter on\n   * @param value - The value to filter with\n   */\n  eq(column: string, value: unknown): this {\n    this.url.searchParams.append(column, `eq.${value}`)\n    return this\n  }\n\n  neq<ColumnName extends string & keyof Row>(column: ColumnName, value: Row[ColumnName]): this\n  neq(column: string, value: unknown): this\n  /**\n   * Match only rows where `column` is not equal to `value`.\n   *\n   * @param column - The column to filter on\n   * @param value - The value to filter with\n   */\n  neq(column: string, value: unknown): this {\n    this.url.searchParams.append(column, `neq.${value}`)\n    return this\n  }\n\n  gt<ColumnName extends string & keyof Row>(column: ColumnName, value: Row[ColumnName]): this\n  gt(column: string, value: unknown): this\n  /**\n   * Match only rows where `column` is greater than `value`.\n   *\n   * @param column - The column to filter on\n   * @param value - The value to filter with\n   */\n  gt(column: string, value: unknown): this {\n    this.url.searchParams.append(column, `gt.${value}`)\n    return this\n  }\n\n  gte<ColumnName extends string & keyof Row>(column: ColumnName, value: Row[ColumnName]): this\n  gte(column: string, value: unknown): this\n  /**\n   * Match only rows where `column` is greater than or equal to `value`.\n   *\n   * @param column - The column to filter on\n   * @param value - The value to filter with\n   */\n  gte(column: string, value: unknown): this {\n    this.url.searchParams.append(column, `gte.${value}`)\n    return this\n  }\n\n  lt<ColumnName extends string & keyof Row>(column: ColumnName, value: Row[ColumnName]): this\n  lt(column: string, value: unknown): this\n  /**\n   * Match only rows where `column` is less than `value`.\n   *\n   * @param column - The column to filter on\n   * @param value - The value to filter with\n   */\n  lt(column: string, value: unknown): this {\n    this.url.searchParams.append(column, `lt.${value}`)\n    return this\n  }\n\n  lte<ColumnName extends string & keyof Row>(column: ColumnName, value: Row[ColumnName]): this\n  lte(column: string, value: unknown): this\n  /**\n   * Match only rows where `column` is less than or equal to `value`.\n   *\n   * @param column - The column to filter on\n   * @param value - The value to filter with\n   */\n  lte(column: string, value: unknown): this {\n    this.url.searchParams.append(column, `lte.${value}`)\n    return this\n  }\n\n  like<ColumnName extends string & keyof Row>(column: ColumnName, pattern: string): this\n  like(column: string, pattern: string): this\n  /**\n   * Match only rows where `column` matches `pattern` case-sensitively.\n   *\n   * @param column - The column to filter on\n   * @param pattern - The pattern to match with\n   */\n  like(column: string, pattern: string): this {\n    this.url.searchParams.append(column, `like.${pattern}`)\n    return this\n  }\n\n  likeAllOf<ColumnName extends string & keyof Row>(\n    column: ColumnName,\n    patterns: readonly string[]\n  ): this\n  likeAllOf(column: string, patterns: readonly string[]): this\n  /**\n   * Match only rows where `column` matches all of `patterns` case-sensitively.\n   *\n   * @param column - The column to filter on\n   * @param patterns - The patterns to match with\n   */\n  likeAllOf(column: string, patterns: readonly string[]): this {\n    this.url.searchParams.append(column, `like(all).{${patterns.join(',')}}`)\n    return this\n  }\n\n  likeAnyOf<ColumnName extends string & keyof Row>(\n    column: ColumnName,\n    patterns: readonly string[]\n  ): this\n  likeAnyOf(column: string, patterns: readonly string[]): this\n  /**\n   * Match only rows where `column` matches any of `patterns` case-sensitively.\n   *\n   * @param column - The column to filter on\n   * @param patterns - The patterns to match with\n   */\n  likeAnyOf(column: string, patterns: readonly string[]): this {\n    this.url.searchParams.append(column, `like(any).{${patterns.join(',')}}`)\n    return this\n  }\n\n  ilike<ColumnName extends string & keyof Row>(column: ColumnName, pattern: string): this\n  ilike(column: string, pattern: string): this\n  /**\n   * Match only rows where `column` matches `pattern` case-insensitively.\n   *\n   * @param column - The column to filter on\n   * @param pattern - The pattern to match with\n   */\n  ilike(column: string, pattern: string): this {\n    this.url.searchParams.append(column, `ilike.${pattern}`)\n    return this\n  }\n\n  ilikeAllOf<ColumnName extends string & keyof Row>(\n    column: ColumnName,\n    patterns: readonly string[]\n  ): this\n  ilikeAllOf(column: string, patterns: readonly string[]): this\n  /**\n   * Match only rows where `column` matches all of `patterns` case-insensitively.\n   *\n   * @param column - The column to filter on\n   * @param patterns - The patterns to match with\n   */\n  ilikeAllOf(column: string, patterns: readonly string[]): this {\n    this.url.searchParams.append(column, `ilike(all).{${patterns.join(',')}}`)\n    return this\n  }\n\n  ilikeAnyOf<ColumnName extends string & keyof Row>(\n    column: ColumnName,\n    patterns: readonly string[]\n  ): this\n  ilikeAnyOf(column: string, patterns: readonly string[]): this\n  /**\n   * Match only rows where `column` matches any of `patterns` case-insensitively.\n   *\n   * @param column - The column to filter on\n   * @param patterns - The patterns to match with\n   */\n  ilikeAnyOf(column: string, patterns: readonly string[]): this {\n    this.url.searchParams.append(column, `ilike(any).{${patterns.join(',')}}`)\n    return this\n  }\n\n  is<ColumnName extends string & keyof Row>(\n    column: ColumnName,\n    value: Row[ColumnName] & (boolean | null)\n  ): this\n  is(column: string, value: boolean | null): this\n  /**\n   * Match only rows where `column` IS `value`.\n   *\n   * For non-boolean columns, this is only relevant for checking if the value of\n   * `column` is NULL by setting `value` to `null`.\n   *\n   * For boolean columns, you can also set `value` to `true` or `false` and it\n   * will behave the same way as `.eq()`.\n   *\n   * @param column - The column to filter on\n   * @param value - The value to filter with\n   */\n  is(column: string, value: boolean | null): this {\n    this.url.searchParams.append(column, `is.${value}`)\n    return this\n  }\n\n  in<ColumnName extends string & keyof Row>(\n    column: ColumnName,\n    values: ReadonlyArray<Row[ColumnName]>\n  ): this\n  in(column: string, values: readonly unknown[]): this\n  /**\n   * Match only rows where `column` is included in the `values` array.\n   *\n   * @param column - The column to filter on\n   * @param values - The values array to filter with\n   */\n  in(column: string, values: readonly unknown[]): this {\n    const cleanedValues = Array.from(new Set(values))\n      .map((s) => {\n        // handle postgrest reserved characters\n        // https://postgrest.org/en/v7.0.0/api.html#reserved-characters\n        if (typeof s === 'string' && new RegExp('[,()]').test(s)) return `\"${s}\"`\n        else return `${s}`\n      })\n      .join(',')\n    this.url.searchParams.append(column, `in.(${cleanedValues})`)\n    return this\n  }\n\n  contains<ColumnName extends string & keyof Row>(\n    column: ColumnName,\n    value: string | ReadonlyArray<Row[ColumnName]> | Record<string, unknown>\n  ): this\n  contains(column: string, value: string | readonly unknown[] | Record<string, unknown>): this\n  /**\n   * Only relevant for jsonb, array, and range columns. Match only rows where\n   * `column` contains every element appearing in `value`.\n   *\n   * @param column - The jsonb, array, or range column to filter on\n   * @param value - The jsonb, array, or range value to filter with\n   */\n  contains(column: string, value: string | readonly unknown[] | Record<string, unknown>): this {\n    if (typeof value === 'string') {\n      // range types can be inclusive '[', ']' or exclusive '(', ')' so just\n      // keep it simple and accept a string\n      this.url.searchParams.append(column, `cs.${value}`)\n    } else if (Array.isArray(value)) {\n      // array\n      this.url.searchParams.append(column, `cs.{${value.join(',')}}`)\n    } else {\n      // json\n      this.url.searchParams.append(column, `cs.${JSON.stringify(value)}`)\n    }\n    return this\n  }\n\n  containedBy<ColumnName extends string & keyof Row>(\n    column: ColumnName,\n    value: string | ReadonlyArray<Row[ColumnName]> | Record<string, unknown>\n  ): this\n  containedBy(column: string, value: string | readonly unknown[] | Record<string, unknown>): this\n  /**\n   * Only relevant for jsonb, array, and range columns. Match only rows where\n   * every element appearing in `column` is contained by `value`.\n   *\n   * @param column - The jsonb, array, or range column to filter on\n   * @param value - The jsonb, array, or range value to filter with\n   */\n  containedBy(column: string, value: string | readonly unknown[] | Record<string, unknown>): this {\n    if (typeof value === 'string') {\n      // range\n      this.url.searchParams.append(column, `cd.${value}`)\n    } else if (Array.isArray(value)) {\n      // array\n      this.url.searchParams.append(column, `cd.{${value.join(',')}}`)\n    } else {\n      // json\n      this.url.searchParams.append(column, `cd.${JSON.stringify(value)}`)\n    }\n    return this\n  }\n\n  rangeGt<ColumnName extends string & keyof Row>(column: ColumnName, range: string): this\n  rangeGt(column: string, range: string): this\n  /**\n   * Only relevant for range columns. Match only rows where every element in\n   * `column` is greater than any element in `range`.\n   *\n   * @param column - The range column to filter on\n   * @param range - The range to filter with\n   */\n  rangeGt(column: string, range: string): this {\n    this.url.searchParams.append(column, `sr.${range}`)\n    return this\n  }\n\n  rangeGte<ColumnName extends string & keyof Row>(column: ColumnName, range: string): this\n  rangeGte(column: string, range: string): this\n  /**\n   * Only relevant for range columns. Match only rows where every element in\n   * `column` is either contained in `range` or greater than any element in\n   * `range`.\n   *\n   * @param column - The range column to filter on\n   * @param range - The range to filter with\n   */\n  rangeGte(column: string, range: string): this {\n    this.url.searchParams.append(column, `nxl.${range}`)\n    return this\n  }\n\n  rangeLt<ColumnName extends string & keyof Row>(column: ColumnName, range: string): this\n  rangeLt(column: string, range: string): this\n  /**\n   * Only relevant for range columns. Match only rows where every element in\n   * `column` is less than any element in `range`.\n   *\n   * @param column - The range column to filter on\n   * @param range - The range to filter with\n   */\n  rangeLt(column: string, range: string): this {\n    this.url.searchParams.append(column, `sl.${range}`)\n    return this\n  }\n\n  rangeLte<ColumnName extends string & keyof Row>(column: ColumnName, range: string): this\n  rangeLte(column: string, range: string): this\n  /**\n   * Only relevant for range columns. Match only rows where every element in\n   * `column` is either contained in `range` or less than any element in\n   * `range`.\n   *\n   * @param column - The range column to filter on\n   * @param range - The range to filter with\n   */\n  rangeLte(column: string, range: string): this {\n    this.url.searchParams.append(column, `nxr.${range}`)\n    return this\n  }\n\n  rangeAdjacent<ColumnName extends string & keyof Row>(column: ColumnName, range: string): this\n  rangeAdjacent(column: string, range: string): this\n  /**\n   * Only relevant for range columns. Match only rows where `column` is\n   * mutually exclusive to `range` and there can be no element between the two\n   * ranges.\n   *\n   * @param column - The range column to filter on\n   * @param range - The range to filter with\n   */\n  rangeAdjacent(column: string, range: string): this {\n    this.url.searchParams.append(column, `adj.${range}`)\n    return this\n  }\n\n  overlaps<ColumnName extends string & keyof Row>(\n    column: ColumnName,\n    value: string | ReadonlyArray<Row[ColumnName]>\n  ): this\n  overlaps(column: string, value: string | readonly unknown[]): this\n  /**\n   * Only relevant for array and range columns. Match only rows where\n   * `column` and `value` have an element in common.\n   *\n   * @param column - The array or range column to filter on\n   * @param value - The array or range value to filter with\n   */\n  overlaps(column: string, value: string | readonly unknown[]): this {\n    if (typeof value === 'string') {\n      // range\n      this.url.searchParams.append(column, `ov.${value}`)\n    } else {\n      // array\n      this.url.searchParams.append(column, `ov.{${value.join(',')}}`)\n    }\n    return this\n  }\n\n  textSearch<ColumnName extends string & keyof Row>(\n    column: ColumnName,\n    query: string,\n    options?: { config?: string; type?: 'plain' | 'phrase' | 'websearch' }\n  ): this\n  textSearch(\n    column: string,\n    query: string,\n    options?: { config?: string; type?: 'plain' | 'phrase' | 'websearch' }\n  ): this\n  /**\n   * Only relevant for text and tsvector columns. Match only rows where\n   * `column` matches the query string in `query`.\n   *\n   * @param column - The text or tsvector column to filter on\n   * @param query - The query text to match with\n   * @param options - Named parameters\n   * @param options.config - The text search configuration to use\n   * @param options.type - Change how the `query` text is interpreted\n   */\n  textSearch(\n    column: string,\n    query: string,\n    { config, type }: { config?: string; type?: 'plain' | 'phrase' | 'websearch' } = {}\n  ): this {\n    let typePart = ''\n    if (type === 'plain') {\n      typePart = 'pl'\n    } else if (type === 'phrase') {\n      typePart = 'ph'\n    } else if (type === 'websearch') {\n      typePart = 'w'\n    }\n    const configPart = config === undefined ? '' : `(${config})`\n    this.url.searchParams.append(column, `${typePart}fts${configPart}.${query}`)\n    return this\n  }\n\n  match<ColumnName extends string & keyof Row>(query: Record<ColumnName, Row[ColumnName]>): this\n  match(query: Record<string, unknown>): this\n  /**\n   * Match only rows where each column in `query` keys is equal to its\n   * associated value. Shorthand for multiple `.eq()`s.\n   *\n   * @param query - The object to filter with, with column names as keys mapped\n   * to their filter values\n   */\n  match(query: Record<string, unknown>): this {\n    Object.entries(query).forEach(([column, value]) => {\n      this.url.searchParams.append(column, `eq.${value}`)\n    })\n    return this\n  }\n\n  not<ColumnName extends string & keyof Row>(\n    column: ColumnName,\n    operator: FilterOperator,\n    value: Row[ColumnName]\n  ): this\n  not(column: string, operator: string, value: unknown): this\n  /**\n   * Match only rows which doesn't satisfy the filter.\n   *\n   * Unlike most filters, `opearator` and `value` are used as-is and need to\n   * follow [PostgREST\n   * syntax](https://postgrest.org/en/stable/api.html#operators). You also need\n   * to make sure they are properly sanitized.\n   *\n   * @param column - The column to filter on\n   * @param operator - The operator to be negated to filter with, following\n   * PostgREST syntax\n   * @param value - The value to filter with, following PostgREST syntax\n   */\n  not(column: string, operator: string, value: unknown): this {\n    this.url.searchParams.append(column, `not.${operator}.${value}`)\n    return this\n  }\n\n  /**\n   * Match only rows which satisfy at least one of the filters.\n   *\n   * Unlike most filters, `filters` is used as-is and needs to follow [PostgREST\n   * syntax](https://postgrest.org/en/stable/api.html#operators). You also need\n   * to make sure it's properly sanitized.\n   *\n   * It's currently not possible to do an `.or()` filter across multiple tables.\n   *\n   * @param filters - The filters to use, following PostgREST syntax\n   * @param options - Named parameters\n   * @param options.referencedTable - Set this to filter on referenced tables\n   * instead of the parent table\n   * @param options.foreignTable - Deprecated, use `referencedTable` instead\n   */\n  or(\n    filters: string,\n    {\n      foreignTable,\n      referencedTable = foreignTable,\n    }: { foreignTable?: string; referencedTable?: string } = {}\n  ): this {\n    const key = referencedTable ? `${referencedTable}.or` : 'or'\n    this.url.searchParams.append(key, `(${filters})`)\n    return this\n  }\n\n  filter<ColumnName extends string & keyof Row>(\n    column: ColumnName,\n    operator: `${'' | 'not.'}${FilterOperator}`,\n    value: unknown\n  ): this\n  filter(column: string, operator: string, value: unknown): this\n  /**\n   * Match only rows which satisfy the filter. This is an escape hatch - you\n   * should use the specific filter methods wherever possible.\n   *\n   * Unlike most filters, `opearator` and `value` are used as-is and need to\n   * follow [PostgREST\n   * syntax](https://postgrest.org/en/stable/api.html#operators). You also need\n   * to make sure they are properly sanitized.\n   *\n   * @param column - The column to filter on\n   * @param operator - The operator to filter with, following PostgREST syntax\n   * @param value - The value to filter with, following PostgREST syntax\n   */\n  filter(column: string, operator: string, value: unknown): this {\n    this.url.searchParams.append(column, `${operator}.${value}`)\n    return this\n  }\n}\n", "import PostgrestBuilder from './PostgrestBuilder'\nimport PostgrestFilterBuilder from './PostgrestFilterBuilder'\nimport { GetResult } from './select-query-parser'\nimport { Fetch, GenericSchema, GenericTable, GenericView } from './types'\n\nexport default class PostgrestQueryBuilder<\n  Schema extends GenericSchema,\n  Relation extends GenericTable | GenericView,\n  RelationName = unknown,\n  Relationships = Relation extends { Relationships: infer R } ? R : unknown\n> {\n  url: URL\n  headers: Record<string, string>\n  schema?: string\n  signal?: AbortSignal\n  fetch?: Fetch\n\n  constructor(\n    url: URL,\n    {\n      headers = {},\n      schema,\n      fetch,\n    }: {\n      headers?: Record<string, string>\n      schema?: string\n      fetch?: Fetch\n    }\n  ) {\n    this.url = url\n    this.headers = headers\n    this.schema = schema\n    this.fetch = fetch\n  }\n\n  /**\n   * Perform a SELECT query on the table or view.\n   *\n   * @param columns - The columns to retrieve, separated by commas. Columns can be renamed when returned with `customName:columnName`\n   *\n   * @param options - Named parameters\n   *\n   * @param options.head - When set to `true`, `data` will not be returned.\n   * Useful if you only need the count.\n   *\n   * @param options.count - Count algorithm to use to count rows in the table or view.\n   *\n   * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n   * hood.\n   *\n   * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n   * statistics under the hood.\n   *\n   * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n   * numbers.\n   */\n  select<\n    Query extends string = '*',\n    ResultOne = GetResult<Schema, Relation['Row'], RelationName, Relationships, Query>\n  >(\n    columns?: Query,\n    {\n      head = false,\n      count,\n    }: {\n      head?: boolean\n      count?: 'exact' | 'planned' | 'estimated'\n    } = {}\n  ): PostgrestFilterBuilder<Schema, Relation['Row'], ResultOne[], RelationName, Relationships> {\n    const method = head ? 'HEAD' : 'GET'\n    // Remove whitespaces except when quoted\n    let quoted = false\n    const cleanedColumns = (columns ?? '*')\n      .split('')\n      .map((c) => {\n        if (/\\s/.test(c) && !quoted) {\n          return ''\n        }\n        if (c === '\"') {\n          quoted = !quoted\n        }\n        return c\n      })\n      .join('')\n    this.url.searchParams.set('select', cleanedColumns)\n    if (count) {\n      this.headers['Prefer'] = `count=${count}`\n    }\n\n    return new PostgrestFilterBuilder({\n      method,\n      url: this.url,\n      headers: this.headers,\n      schema: this.schema,\n      fetch: this.fetch,\n      allowEmpty: false,\n    } as unknown as PostgrestBuilder<ResultOne[]>)\n  }\n\n  // TODO(v3): Make `defaultToNull` consistent for both single & bulk inserts.\n  insert<Row extends Relation extends { Insert: unknown } ? Relation['Insert'] : never>(\n    values: Row,\n    options?: {\n      count?: 'exact' | 'planned' | 'estimated'\n    }\n  ): PostgrestFilterBuilder<Schema, Relation['Row'], null, RelationName, Relationships>\n  insert<Row extends Relation extends { Insert: unknown } ? Relation['Insert'] : never>(\n    values: Row[],\n    options?: {\n      count?: 'exact' | 'planned' | 'estimated'\n      defaultToNull?: boolean\n    }\n  ): PostgrestFilterBuilder<Schema, Relation['Row'], null, RelationName, Relationships>\n  /**\n   * Perform an INSERT into the table or view.\n   *\n   * By default, inserted rows are not returned. To return it, chain the call\n   * with `.select()`.\n   *\n   * @param values - The values to insert. Pass an object to insert a single row\n   * or an array to insert multiple rows.\n   *\n   * @param options - Named parameters\n   *\n   * @param options.count - Count algorithm to use to count inserted rows.\n   *\n   * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n   * hood.\n   *\n   * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n   * statistics under the hood.\n   *\n   * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n   * numbers.\n   *\n   * @param options.defaultToNull - Make missing fields default to `null`.\n   * Otherwise, use the default value for the column. Only applies for bulk\n   * inserts.\n   */\n  insert<Row extends Relation extends { Insert: unknown } ? Relation['Insert'] : never>(\n    values: Row | Row[],\n    {\n      count,\n      defaultToNull = true,\n    }: {\n      count?: 'exact' | 'planned' | 'estimated'\n      defaultToNull?: boolean\n    } = {}\n  ): PostgrestFilterBuilder<Schema, Relation['Row'], null, RelationName, Relationships> {\n    const method = 'POST'\n\n    const prefersHeaders = []\n    if (this.headers['Prefer']) {\n      prefersHeaders.push(this.headers['Prefer'])\n    }\n    if (count) {\n      prefersHeaders.push(`count=${count}`)\n    }\n    if (!defaultToNull) {\n      prefersHeaders.push('missing=default')\n    }\n    this.headers['Prefer'] = prefersHeaders.join(',')\n\n    if (Array.isArray(values)) {\n      const columns = values.reduce((acc, x) => acc.concat(Object.keys(x)), [] as string[])\n      if (columns.length > 0) {\n        const uniqueColumns = [...new Set(columns)].map((column) => `\"${column}\"`)\n        this.url.searchParams.set('columns', uniqueColumns.join(','))\n      }\n    }\n\n    return new PostgrestFilterBuilder({\n      method,\n      url: this.url,\n      headers: this.headers,\n      schema: this.schema,\n      body: values,\n      fetch: this.fetch,\n      allowEmpty: false,\n    } as unknown as PostgrestBuilder<null>)\n  }\n\n  // TODO(v3): Make `defaultToNull` consistent for both single & bulk upserts.\n  upsert<Row extends Relation extends { Insert: unknown } ? Relation['Insert'] : never>(\n    values: Row,\n    options?: {\n      onConflict?: string\n      ignoreDuplicates?: boolean\n      count?: 'exact' | 'planned' | 'estimated'\n    }\n  ): PostgrestFilterBuilder<Schema, Relation['Row'], null, RelationName, Relationships>\n  upsert<Row extends Relation extends { Insert: unknown } ? Relation['Insert'] : never>(\n    values: Row[],\n    options?: {\n      onConflict?: string\n      ignoreDuplicates?: boolean\n      count?: 'exact' | 'planned' | 'estimated'\n      defaultToNull?: boolean\n    }\n  ): PostgrestFilterBuilder<Schema, Relation['Row'], null, RelationName, Relationships>\n  /**\n   * Perform an UPSERT on the table or view. Depending on the column(s) passed\n   * to `onConflict`, `.upsert()` allows you to perform the equivalent of\n   * `.insert()` if a row with the corresponding `onConflict` columns doesn't\n   * exist, or if it does exist, perform an alternative action depending on\n   * `ignoreDuplicates`.\n   *\n   * By default, upserted rows are not returned. To return it, chain the call\n   * with `.select()`.\n   *\n   * @param values - The values to upsert with. Pass an object to upsert a\n   * single row or an array to upsert multiple rows.\n   *\n   * @param options - Named parameters\n   *\n   * @param options.onConflict - Comma-separated UNIQUE column(s) to specify how\n   * duplicate rows are determined. Two rows are duplicates if all the\n   * `onConflict` columns are equal.\n   *\n   * @param options.ignoreDuplicates - If `true`, duplicate rows are ignored. If\n   * `false`, duplicate rows are merged with existing rows.\n   *\n   * @param options.count - Count algorithm to use to count upserted rows.\n   *\n   * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n   * hood.\n   *\n   * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n   * statistics under the hood.\n   *\n   * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n   * numbers.\n   *\n   * @param options.defaultToNull - Make missing fields default to `null`.\n   * Otherwise, use the default value for the column. This only applies when\n   * inserting new rows, not when merging with existing rows under\n   * `ignoreDuplicates: false`. This also only applies when doing bulk upserts.\n   */\n  upsert<Row extends Relation extends { Insert: unknown } ? Relation['Insert'] : never>(\n    values: Row | Row[],\n    {\n      onConflict,\n      ignoreDuplicates = false,\n      count,\n      defaultToNull = true,\n    }: {\n      onConflict?: string\n      ignoreDuplicates?: boolean\n      count?: 'exact' | 'planned' | 'estimated'\n      defaultToNull?: boolean\n    } = {}\n  ): PostgrestFilterBuilder<Schema, Relation['Row'], null, RelationName, Relationships> {\n    const method = 'POST'\n\n    const prefersHeaders = [`resolution=${ignoreDuplicates ? 'ignore' : 'merge'}-duplicates`]\n\n    if (onConflict !== undefined) this.url.searchParams.set('on_conflict', onConflict)\n    if (this.headers['Prefer']) {\n      prefersHeaders.push(this.headers['Prefer'])\n    }\n    if (count) {\n      prefersHeaders.push(`count=${count}`)\n    }\n    if (!defaultToNull) {\n      prefersHeaders.push('missing=default')\n    }\n    this.headers['Prefer'] = prefersHeaders.join(',')\n\n    if (Array.isArray(values)) {\n      const columns = values.reduce((acc, x) => acc.concat(Object.keys(x)), [] as string[])\n      if (columns.length > 0) {\n        const uniqueColumns = [...new Set(columns)].map((column) => `\"${column}\"`)\n        this.url.searchParams.set('columns', uniqueColumns.join(','))\n      }\n    }\n\n    return new PostgrestFilterBuilder({\n      method,\n      url: this.url,\n      headers: this.headers,\n      schema: this.schema,\n      body: values,\n      fetch: this.fetch,\n      allowEmpty: false,\n    } as unknown as PostgrestBuilder<null>)\n  }\n\n  /**\n   * Perform an UPDATE on the table or view.\n   *\n   * By default, updated rows are not returned. To return it, chain the call\n   * with `.select()` after filters.\n   *\n   * @param values - The values to update with\n   *\n   * @param options - Named parameters\n   *\n   * @param options.count - Count algorithm to use to count updated rows.\n   *\n   * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n   * hood.\n   *\n   * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n   * statistics under the hood.\n   *\n   * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n   * numbers.\n   */\n  update<Row extends Relation extends { Update: unknown } ? Relation['Update'] : never>(\n    values: Row,\n    {\n      count,\n    }: {\n      count?: 'exact' | 'planned' | 'estimated'\n    } = {}\n  ): PostgrestFilterBuilder<Schema, Relation['Row'], null, RelationName, Relationships> {\n    const method = 'PATCH'\n    const prefersHeaders = []\n    if (this.headers['Prefer']) {\n      prefersHeaders.push(this.headers['Prefer'])\n    }\n    if (count) {\n      prefersHeaders.push(`count=${count}`)\n    }\n    this.headers['Prefer'] = prefersHeaders.join(',')\n\n    return new PostgrestFilterBuilder({\n      method,\n      url: this.url,\n      headers: this.headers,\n      schema: this.schema,\n      body: values,\n      fetch: this.fetch,\n      allowEmpty: false,\n    } as unknown as PostgrestBuilder<null>)\n  }\n\n  /**\n   * Perform a DELETE on the table or view.\n   *\n   * By default, deleted rows are not returned. To return it, chain the call\n   * with `.select()` after filters.\n   *\n   * @param options - Named parameters\n   *\n   * @param options.count - Count algorithm to use to count deleted rows.\n   *\n   * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n   * hood.\n   *\n   * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n   * statistics under the hood.\n   *\n   * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n   * numbers.\n   */\n  delete({\n    count,\n  }: {\n    count?: 'exact' | 'planned' | 'estimated'\n  } = {}): PostgrestFilterBuilder<Schema, Relation['Row'], null, RelationName, Relationships> {\n    const method = 'DELETE'\n    const prefersHeaders = []\n    if (count) {\n      prefersHeaders.push(`count=${count}`)\n    }\n    if (this.headers['Prefer']) {\n      prefersHeaders.unshift(this.headers['Prefer'])\n    }\n    this.headers['Prefer'] = prefersHeaders.join(',')\n\n    return new PostgrestFilterBuilder({\n      method,\n      url: this.url,\n      headers: this.headers,\n      schema: this.schema,\n      fetch: this.fetch,\n      allowEmpty: false,\n    } as unknown as PostgrestBuilder<null>)\n  }\n}\n", "export const version = '1.16.1'\n", "import { version } from './version'\nexport const DEFAULT_HEADERS = { 'X-Client-Info': `postgrest-js/${version}` }\n", "import PostgrestQueryBuilder from './PostgrestQueryBuilder'\nimport PostgrestFilterBuilder from './PostgrestFilterBuilder'\nimport PostgrestBuilder from './PostgrestBuilder'\nimport { DEFAULT_HEADERS } from './constants'\nimport { Fetch, GenericSchema } from './types'\n\n/**\n * PostgREST client.\n *\n * @typeParam Database - Types for the schema from the [type\n * generator](https://supabase.com/docs/reference/javascript/next/typescript-support)\n *\n * @typeParam SchemaName - Postgres schema to switch to. Must be a string\n * literal, the same one passed to the constructor. If the schema is not\n * `\"public\"`, this must be supplied manually.\n */\nexport default class PostgrestClient<\n  Database = any,\n  SchemaName extends string & keyof Database = 'public' extends keyof Database\n    ? 'public'\n    : string & keyof Database,\n  Schema extends GenericSchema = Database[SchemaName] extends GenericSchema\n    ? Database[SchemaName]\n    : any\n> {\n  url: string\n  headers: Record<string, string>\n  schemaName?: SchemaName\n  fetch?: Fetch\n\n  // TODO: Add back shouldThrowOnError once we figure out the typings\n  /**\n   * Creates a PostgREST client.\n   *\n   * @param url - URL of the PostgREST endpoint\n   * @param options - Named parameters\n   * @param options.headers - Custom headers\n   * @param options.schema - Postgres schema to switch to\n   * @param options.fetch - Custom fetch\n   */\n  constructor(\n    url: string,\n    {\n      headers = {},\n      schema,\n      fetch,\n    }: {\n      headers?: Record<string, string>\n      schema?: SchemaName\n      fetch?: Fetch\n    } = {}\n  ) {\n    this.url = url\n    this.headers = { ...DEFAULT_HEADERS, ...headers }\n    this.schemaName = schema\n    this.fetch = fetch\n  }\n\n  from<\n    TableName extends string & keyof Schema['Tables'],\n    Table extends Schema['Tables'][TableName]\n  >(relation: TableName): PostgrestQueryBuilder<Schema, Table, TableName>\n  from<ViewName extends string & keyof Schema['Views'], View extends Schema['Views'][ViewName]>(\n    relation: ViewName\n  ): PostgrestQueryBuilder<Schema, View, ViewName>\n  /**\n   * Perform a query on a table or a view.\n   *\n   * @param relation - The table or view name to query\n   */\n  from(relation: string): PostgrestQueryBuilder<Schema, any, any> {\n    const url = new URL(`${this.url}/${relation}`)\n    return new PostgrestQueryBuilder(url, {\n      headers: { ...this.headers },\n      schema: this.schemaName,\n      fetch: this.fetch,\n    })\n  }\n\n  /**\n   * Select a schema to query or perform an function (rpc) call.\n   *\n   * The schema needs to be on the list of exposed schemas inside Supabase.\n   *\n   * @param schema - The schema to query\n   */\n  schema<DynamicSchema extends string & keyof Database>(\n    schema: DynamicSchema\n  ): PostgrestClient<\n    Database,\n    DynamicSchema,\n    Database[DynamicSchema] extends GenericSchema ? Database[DynamicSchema] : any\n  > {\n    return new PostgrestClient(this.url, {\n      headers: this.headers,\n      schema,\n      fetch: this.fetch,\n    })\n  }\n\n  /**\n   * Perform a function call.\n   *\n   * @param fn - The function name to call\n   * @param args - The arguments to pass to the function call\n   * @param options - Named parameters\n   * @param options.head - When set to `true`, `data` will not be returned.\n   * Useful if you only need the count.\n   * @param options.get - When set to `true`, the function will be called with\n   * read-only access mode.\n   * @param options.count - Count algorithm to use to count rows returned by the\n   * function. Only applicable for [set-returning\n   * functions](https://www.postgresql.org/docs/current/functions-srf.html).\n   *\n   * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n   * hood.\n   *\n   * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n   * statistics under the hood.\n   *\n   * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n   * numbers.\n   */\n  rpc<FnName extends string & keyof Schema['Functions'], Fn extends Schema['Functions'][FnName]>(\n    fn: FnName,\n    args: Fn['Args'] = {},\n    {\n      head = false,\n      get = false,\n      count,\n    }: {\n      head?: boolean\n      get?: boolean\n      count?: 'exact' | 'planned' | 'estimated'\n    } = {}\n  ): PostgrestFilterBuilder<\n    Schema,\n    Fn['Returns'] extends any[]\n      ? Fn['Returns'][number] extends Record<string, unknown>\n        ? Fn['Returns'][number]\n        : never\n      : never,\n    Fn['Returns']\n  > {\n    let method: 'HEAD' | 'GET' | 'POST'\n    const url = new URL(`${this.url}/rpc/${fn}`)\n    let body: unknown | undefined\n    if (head || get) {\n      method = head ? 'HEAD' : 'GET'\n      Object.entries(args)\n        // params with undefined value needs to be filtered out, otherwise it'll\n        // show up as `?param=undefined`\n        .filter(([_, value]) => value !== undefined)\n        // array values need special syntax\n        .map(([name, value]) => [name, Array.isArray(value) ? `{${value.join(',')}}` : `${value}`])\n        .forEach(([name, value]) => {\n          url.searchParams.append(name, value)\n        })\n    } else {\n      method = 'POST'\n      body = args\n    }\n\n    const headers = { ...this.headers }\n    if (count) {\n      headers['Prefer'] = `count=${count}`\n    }\n\n    return new PostgrestFilterBuilder({\n      method,\n      url,\n      headers,\n      schema: this.schemaName,\n      body,\n      fetch: this.fetch,\n      allowEmpty: false,\n    } as unknown as PostgrestBuilder<Fn['Returns']>)\n  }\n}\n", "// Always update wrapper.mjs when updating this file.\nimport PostgrestClient from './PostgrestClient'\nimport PostgrestQueryBuilder from './PostgrestQueryBuilder'\nimport PostgrestFilterBuilder from './PostgrestFilterBuilder'\nimport PostgrestTransformBuilder from './PostgrestTransformBuilder'\nimport PostgrestBuilder from './PostgrestBuilder'\n\nexport {\n  PostgrestClient,\n  PostgrestQueryBuilder,\n  PostgrestFilterBuilder,\n  PostgrestTransformBuilder,\n  PostgrestBuilder,\n}\nexport default {\n  PostgrestClient,\n  PostgrestQueryBuilder,\n  PostgrestFilterBuilder,\n  PostgrestTransformBuilder,\n  PostgrestBuilder,\n}\nexport type {\n  PostgrestResponse,\n  PostgrestResponseFailure,\n  PostgrestResponseSuccess,\n  PostgrestSingleResponse,\n  PostgrestMaybeSingleResponse,\n  PostgrestError,\n} from './types'\n", "import { Fetch } from './types'\n\nexport const resolveFetch = (customFetch?: Fetch): Fetch => {\n  let _fetch: Fetch\n  if (customFetch) {\n    _fetch = customFetch\n  } else if (typeof fetch === 'undefined') {\n    _fetch = (...args) =>\n      import('@supabase/node-fetch' as any).then(({ default: fetch }) => fetch(...args))\n  } else {\n    _fetch = fetch\n  }\n  return (...args) => _fetch(...args)\n}\n", "export type Fetch = typeof fetch\n\n/**\n * Response format\n *\n */\nexport interface FunctionsResponseSuccess<T> {\n  data: T\n  error: null\n}\nexport interface FunctionsResponseFailure {\n  data: null\n  error: any\n}\nexport type FunctionsResponse<T> = FunctionsResponseSuccess<T> | FunctionsResponseFailure\n\nexport class FunctionsError extends Error {\n  context: any\n  constructor(message: string, name = 'FunctionsError', context?: any) {\n    super(message)\n    this.name = name\n    this.context = context\n  }\n}\n\nexport class FunctionsFetchError extends FunctionsError {\n  constructor(context: any) {\n    super('Failed to send a request to the Edge Function', 'FunctionsFetchError', context)\n  }\n}\n\nexport class FunctionsRelayError extends FunctionsError {\n  constructor(context: any) {\n    super('Relay Error invoking the Edge Function', 'FunctionsRelayError', context)\n  }\n}\n\nexport class FunctionsHttpError extends FunctionsError {\n  constructor(context: any) {\n    super('Edge Function returned a non-2xx status code', 'FunctionsHttpError', context)\n  }\n}\n// Define the enum for the 'region' property\nexport enum FunctionRegion {\n  Any = 'any',\n  ApNortheast1 = 'ap-northeast-1',\n  ApNortheast2 = 'ap-northeast-2',\n  ApSouth1 = 'ap-south-1',\n  ApSoutheast1 = 'ap-southeast-1',\n  ApSoutheast2 = 'ap-southeast-2',\n  CaCentral1 = 'ca-central-1',\n  EuCentral1 = 'eu-central-1',\n  EuWest1 = 'eu-west-1',\n  EuWest2 = 'eu-west-2',\n  EuWest3 = 'eu-west-3',\n  SaEast1 = 'sa-east-1',\n  UsEast1 = 'us-east-1',\n  UsWest1 = 'us-west-1',\n  UsWest2 = 'us-west-2',\n}\n\nexport type FunctionInvokeOptions = {\n  /**\n   * Object representing the headers to send with the request.\n   * */\n  headers?: { [key: string]: string }\n  /**\n   * The HTTP verb of the request\n   */\n  method?: 'POST' | 'GET' | 'PUT' | 'PATCH' | 'DELETE'\n  /**\n   * The Region to invoke the function in.\n   */\n  region?: FunctionRegion\n  /**\n   * The body of the request.\n   */\n  body?:\n    | File\n    | Blob\n    | ArrayBuffer\n    | FormData\n    | ReadableStream<Uint8Array>\n    | Record<string, any>\n    | string\n}\n", "import { resolveFetch } from './helper'\nimport {\n  Fetch,\n  FunctionsFetchError,\n  FunctionsHttpError,\n  FunctionsRelayError,\n  FunctionsResponse,\n  FunctionInvokeOptions,\n  FunctionRegion,\n} from './types'\n\nexport class FunctionsClient {\n  protected url: string\n  protected headers: Record<string, string>\n  protected region: FunctionRegion\n  protected fetch: Fetch\n\n  constructor(\n    url: string,\n    {\n      headers = {},\n      customFetch,\n      region = FunctionRegion.Any,\n    }: {\n      headers?: Record<string, string>\n      customFetch?: Fetch\n      region?: FunctionRegion\n    } = {}\n  ) {\n    this.url = url\n    this.headers = headers\n    this.region = region\n    this.fetch = resolveFetch(customFetch)\n  }\n\n  /**\n   * Updates the authorization header\n   * @param token - the new jwt token sent in the authorisation header\n   */\n  setAuth(token: string) {\n    this.headers.Authorization = `Bearer ${token}`\n  }\n\n  /**\n   * Invokes a function\n   * @param functionName - The name of the Function to invoke.\n   * @param options - Options for invoking the Function.\n   */\n  async invoke<T = any>(\n    functionName: string,\n    options: FunctionInvokeOptions = {}\n  ): Promise<FunctionsResponse<T>> {\n    try {\n      const { headers, method, body: functionArgs } = options\n      let _headers: Record<string, string> = {}\n      let { region } = options\n      if (!region) {\n        region = this.region\n      }\n      if (region && region !== 'any') {\n        _headers['x-region'] = region\n      }\n      let body: any\n      if (\n        functionArgs &&\n        ((headers && !Object.prototype.hasOwnProperty.call(headers, 'Content-Type')) || !headers)\n      ) {\n        if (\n          (typeof Blob !== 'undefined' && functionArgs instanceof Blob) ||\n          functionArgs instanceof ArrayBuffer\n        ) {\n          // will work for File as File inherits Blob\n          // also works for ArrayBuffer as it is the same underlying structure as a Blob\n          _headers['Content-Type'] = 'application/octet-stream'\n          body = functionArgs\n        } else if (typeof functionArgs === 'string') {\n          // plain string\n          _headers['Content-Type'] = 'text/plain'\n          body = functionArgs\n        } else if (typeof FormData !== 'undefined' && functionArgs instanceof FormData) {\n          // don't set content-type headers\n          // Request will automatically add the right boundary value\n          body = functionArgs\n        } else {\n          // default, assume this is JSON\n          _headers['Content-Type'] = 'application/json'\n          body = JSON.stringify(functionArgs)\n        }\n      }\n\n      const response = await this.fetch(`${this.url}/${functionName}`, {\n        method: method || 'POST',\n        // headers priority is (high to low):\n        // 1. invoke-level headers\n        // 2. client-level headers\n        // 3. default Content-Type header\n        headers: { ..._headers, ...this.headers, ...headers },\n        body,\n      }).catch((fetchError) => {\n        throw new FunctionsFetchError(fetchError)\n      })\n\n      const isRelayError = response.headers.get('x-relay-error')\n      if (isRelayError && isRelayError === 'true') {\n        throw new FunctionsRelayError(response)\n      }\n\n      if (!response.ok) {\n        throw new FunctionsHttpError(response)\n      }\n\n      let responseType = (response.headers.get('Content-Type') ?? 'text/plain').split(';')[0].trim()\n      let data: any\n      if (responseType === 'application/json') {\n        data = await response.json()\n      } else if (responseType === 'application/octet-stream') {\n        data = await response.blob()\n      } else if (responseType === 'text/event-stream') {\n        data = response\n      } else if (responseType === 'multipart/form-data') {\n        data = await response.formData()\n      } else {\n        // default to text\n        data = await response.text()\n      }\n\n      return { data, error: null }\n    } catch (error) {\n      return { data: null, error }\n    }\n  }\n}\n", "import index from '../cjs/index.js'\nconst {\n  PostgrestClient,\n  PostgrestQueryBuilder,\n  PostgrestFilterBuilder,\n  PostgrestTransformBuilder,\n  PostgrestBuilder,\n} = index\n\nexport {\n  PostgrestBuilder,\n  PostgrestClient,\n  PostgrestFilterBuilder,\n  PostgrestQueryBuilder,\n  PostgrestTransformBuilder,\n}\n\n// compatibility with CJS output\nexport default {\n  PostgrestClient,\n  PostgrestQueryBuilder,\n  PostgrestFilterBuilder,\n  PostgrestTransformBuilder,\n  PostgrestBuilder,\n}\n", "export const version = '2.10.2'\n", "import { version } from './version'\n\nexport const DEFAULT_HEADERS = { 'X-Client-Info': `realtime-js/${version}` }\n\nexport const VSN: string = '1.0.0'\n\nexport const DEFAULT_TIMEOUT = 10000\n\nexport const WS_CLOSE_NORMAL = 1000\n\nexport enum SOCKET_STATES {\n  connecting = 0,\n  open = 1,\n  closing = 2,\n  closed = 3,\n}\n\nexport enum CHANNEL_STATES {\n  closed = 'closed',\n  errored = 'errored',\n  joined = 'joined',\n  joining = 'joining',\n  leaving = 'leaving',\n}\n\nexport enum CHANNEL_EVENTS {\n  close = 'phx_close',\n  error = 'phx_error',\n  join = 'phx_join',\n  reply = 'phx_reply',\n  leave = 'phx_leave',\n  access_token = 'access_token',\n}\n\nexport enum TRANSPORTS {\n  websocket = 'websocket',\n}\n\nexport enum CONNECTION_STATE {\n  Connecting = 'connecting',\n  Open = 'open',\n  Closing = 'closing',\n  Closed = 'closed',\n}\n", "// This file draws heavily from https://github.com/phoenixframework/phoenix/commit/cf098e9cf7a44ee6479d31d911a97d3c7430c6fe\n// License: https://github.com/phoenixframework/phoenix/blob/master/LICENSE.md\n\nexport default class Serializer {\n  HEADER_LENGTH = 1\n\n  decode(rawPayload: ArrayBuffer | string, callback: Function) {\n    if (rawPayload.constructor === ArrayBuffer) {\n      return callback(this._binaryDecode(rawPayload))\n    }\n\n    if (typeof rawPayload === 'string') {\n      return callback(JSON.parse(rawPayload))\n    }\n\n    return callback({})\n  }\n\n  private _binaryDecode(buffer: ArrayBuffer) {\n    const view = new DataView(buffer)\n    const decoder = new TextDecoder()\n\n    return this._decodeBroadcast(buffer, view, decoder)\n  }\n\n  private _decodeBroadcast(\n    buffer: <PERSON><PERSON><PERSON><PERSON><PERSON>er,\n    view: DataView,\n    decoder: TextDecoder\n  ): {\n    ref: null\n    topic: string\n    event: string\n    payload: { [key: string]: any }\n  } {\n    const topicSize = view.getUint8(1)\n    const eventSize = view.getUint8(2)\n    let offset = this.HEADER_LENGTH + 2\n    const topic = decoder.decode(buffer.slice(offset, offset + topicSize))\n    offset = offset + topicSize\n    const event = decoder.decode(buffer.slice(offset, offset + eventSize))\n    offset = offset + eventSize\n    const data = JSON.parse(\n      decoder.decode(buffer.slice(offset, buffer.byteLength))\n    )\n\n    return { ref: null, topic: topic, event: event, payload: data }\n  }\n}\n", "/**\n * Creates a timer that accepts a `timerCalc` function to perform calculated timeout retries, such as exponential backoff.\n *\n * @example\n *    let reconnectTimer = new Timer(() => this.connect(), function(tries){\n *      return [1000, 5000, 10000][tries - 1] || 10000\n *    })\n *    reconnectTimer.scheduleTimeout() // fires after 1000\n *    reconnectTimer.scheduleTimeout() // fires after 5000\n *    reconnectTimer.reset()\n *    reconnectTimer.scheduleTimeout() // fires after 1000\n */\nexport default class Timer {\n  timer: number | undefined = undefined\n  tries: number = 0\n\n  constructor(public callback: Function, public timerCalc: Function) {\n    this.callback = callback\n    this.timerCalc = timerCalc\n  }\n\n  reset() {\n    this.tries = 0\n    clearTimeout(this.timer)\n  }\n\n  // Cancels any previous scheduleTimeout and schedules callback\n  scheduleTimeout() {\n    clearTimeout(this.timer)\n\n    this.timer = <any>setTimeout(() => {\n      this.tries = this.tries + 1\n      this.callback()\n    }, this.timerCalc(this.tries + 1))\n  }\n}\n", "/**\n * Helpers to convert the change Payload into native JS types.\n */\n\n// Adapted from epgsql (src/epgsql_binary.erl), this module licensed under\n// 3-clause BSD found here: https://raw.githubusercontent.com/epgsql/epgsql/devel/LICENSE\n\nexport enum PostgresTypes {\n  abstime = 'abstime',\n  bool = 'bool',\n  date = 'date',\n  daterange = 'daterange',\n  float4 = 'float4',\n  float8 = 'float8',\n  int2 = 'int2',\n  int4 = 'int4',\n  int4range = 'int4range',\n  int8 = 'int8',\n  int8range = 'int8range',\n  json = 'json',\n  jsonb = 'jsonb',\n  money = 'money',\n  numeric = 'numeric',\n  oid = 'oid',\n  reltime = 'reltime',\n  text = 'text',\n  time = 'time',\n  timestamp = 'timestamp',\n  timestamptz = 'timestamptz',\n  timetz = 'timetz',\n  tsrange = 'tsrange',\n  tstzrange = 'tstzrange',\n}\n\ntype Columns = {\n  name: string // the column name. eg: \"user_id\"\n  type: string // the column type. eg: \"uuid\"\n  flags?: string[] // any special flags for the column. eg: [\"key\"]\n  type_modifier?: number // the type modifier. eg: **********\n}[]\n\ntype BaseValue = null | string | number | boolean\ntype RecordValue = BaseValue | BaseValue[]\n\ntype Record = {\n  [key: string]: RecordValue\n}\n\n/**\n * Takes an array of columns and an object of string values then converts each string value\n * to its mapped type.\n *\n * @param {{name: String, type: String}[]} columns\n * @param {Object} record\n * @param {Object} options The map of various options that can be applied to the mapper\n * @param {Array} options.skipTypes The array of types that should not be converted\n *\n * @example convertChangeData([{name: 'first_name', type: 'text'}, {name: 'age', type: 'int4'}], {first_name: 'Paul', age:'33'}, {})\n * //=>{ first_name: 'Paul', age: 33 }\n */\nexport const convertChangeData = (\n  columns: Columns,\n  record: Record,\n  options: { skipTypes?: string[] } = {}\n): Record => {\n  const skipTypes = options.skipTypes ?? []\n\n  return Object.keys(record).reduce((acc, rec_key) => {\n    acc[rec_key] = convertColumn(rec_key, columns, record, skipTypes)\n    return acc\n  }, {} as Record)\n}\n\n/**\n * Converts the value of an individual column.\n *\n * @param {String} columnName The column that you want to convert\n * @param {{name: String, type: String}[]} columns All of the columns\n * @param {Object} record The map of string values\n * @param {Array} skipTypes An array of types that should not be converted\n * @return {object} Useless information\n *\n * @example convertColumn('age', [{name: 'first_name', type: 'text'}, {name: 'age', type: 'int4'}], {first_name: 'Paul', age: '33'}, [])\n * //=> 33\n * @example convertColumn('age', [{name: 'first_name', type: 'text'}, {name: 'age', type: 'int4'}], {first_name: 'Paul', age: '33'}, ['int4'])\n * //=> \"33\"\n */\nexport const convertColumn = (\n  columnName: string,\n  columns: Columns,\n  record: Record,\n  skipTypes: string[]\n): RecordValue => {\n  const column = columns.find((x) => x.name === columnName)\n  const colType = column?.type\n  const value = record[columnName]\n\n  if (colType && !skipTypes.includes(colType)) {\n    return convertCell(colType, value)\n  }\n\n  return noop(value)\n}\n\n/**\n * If the value of the cell is `null`, returns null.\n * Otherwise converts the string value to the correct type.\n * @param {String} type A postgres column type\n * @param {String} value The cell value\n *\n * @example convertCell('bool', 't')\n * //=> true\n * @example convertCell('int8', '10')\n * //=> 10\n * @example convertCell('_int4', '{1,2,3,4}')\n * //=> [1,2,3,4]\n */\nexport const convertCell = (type: string, value: RecordValue): RecordValue => {\n  // if data type is an array\n  if (type.charAt(0) === '_') {\n    const dataType = type.slice(1, type.length)\n    return toArray(value, dataType)\n  }\n\n  // If not null, convert to correct type.\n  switch (type) {\n    case PostgresTypes.bool:\n      return toBoolean(value)\n    case PostgresTypes.float4:\n    case PostgresTypes.float8:\n    case PostgresTypes.int2:\n    case PostgresTypes.int4:\n    case PostgresTypes.int8:\n    case PostgresTypes.numeric:\n    case PostgresTypes.oid:\n      return toNumber(value)\n    case PostgresTypes.json:\n    case PostgresTypes.jsonb:\n      return toJson(value)\n    case PostgresTypes.timestamp:\n      return toTimestampString(value) // Format to be consistent with PostgREST\n    case PostgresTypes.abstime: // To allow users to cast it based on Timezone\n    case PostgresTypes.date: // To allow users to cast it based on Timezone\n    case PostgresTypes.daterange:\n    case PostgresTypes.int4range:\n    case PostgresTypes.int8range:\n    case PostgresTypes.money:\n    case PostgresTypes.reltime: // To allow users to cast it based on Timezone\n    case PostgresTypes.text:\n    case PostgresTypes.time: // To allow users to cast it based on Timezone\n    case PostgresTypes.timestamptz: // To allow users to cast it based on Timezone\n    case PostgresTypes.timetz: // To allow users to cast it based on Timezone\n    case PostgresTypes.tsrange:\n    case PostgresTypes.tstzrange:\n      return noop(value)\n    default:\n      // Return the value for remaining types\n      return noop(value)\n  }\n}\n\nconst noop = (value: RecordValue): RecordValue => {\n  return value\n}\nexport const toBoolean = (value: RecordValue): RecordValue => {\n  switch (value) {\n    case 't':\n      return true\n    case 'f':\n      return false\n    default:\n      return value\n  }\n}\nexport const toNumber = (value: RecordValue): RecordValue => {\n  if (typeof value === 'string') {\n    const parsedValue = parseFloat(value)\n    if (!Number.isNaN(parsedValue)) {\n      return parsedValue\n    }\n  }\n  return value\n}\nexport const toJson = (value: RecordValue): RecordValue => {\n  if (typeof value === 'string') {\n    try {\n      return JSON.parse(value)\n    } catch (error) {\n      console.log(`JSON parse error: ${error}`)\n      return value\n    }\n  }\n  return value\n}\n\n/**\n * Converts a Postgres Array into a native JS array\n *\n * @example toArray('{}', 'int4')\n * //=> []\n * @example toArray('{\"[2021-01-01,2021-12-31)\",\"(2021-01-01,2021-12-32]\"}', 'daterange')\n * //=> ['[2021-01-01,2021-12-31)', '(2021-01-01,2021-12-32]']\n * @example toArray([1,2,3,4], 'int4')\n * //=> [1,2,3,4]\n */\nexport const toArray = (value: RecordValue, type: string): RecordValue => {\n  if (typeof value !== 'string') {\n    return value\n  }\n\n  const lastIdx = value.length - 1\n  const closeBrace = value[lastIdx]\n  const openBrace = value[0]\n\n  // Confirm value is a Postgres array by checking curly brackets\n  if (openBrace === '{' && closeBrace === '}') {\n    let arr\n    const valTrim = value.slice(1, lastIdx)\n\n    // TODO: find a better solution to separate Postgres array data\n    try {\n      arr = JSON.parse('[' + valTrim + ']')\n    } catch (_) {\n      // WARNING: splitting on comma does not cover all edge cases\n      arr = valTrim ? valTrim.split(',') : []\n    }\n\n    return arr.map((val: BaseValue) => convertCell(type, val))\n  }\n\n  return value\n}\n\n/**\n * Fixes timestamp to be ISO-8601. Swaps the space between the date and time for a 'T'\n * See https://github.com/supabase/supabase/issues/18\n *\n * @example toTimestampString('2019-09-10 00:00:00')\n * //=> '2019-09-10T00:00:00'\n */\nexport const toTimestampString = (value: RecordValue): RecordValue => {\n  if (typeof value === 'string') {\n    return value.replace(' ', 'T')\n  }\n\n  return value\n}\n\nexport const httpEndpointURL = (socketUrl: string): string => {\n  let url = socketUrl\n  url = url.replace(/^ws/i, 'http')\n  url = url.replace(/(\\/socket\\/websocket|\\/socket|\\/websocket)\\/?$/i, '')\n  return url.replace(/\\/+$/, '')\n}\n", "import { DEFAULT_TIMEOUT } from '../lib/constants'\nimport type RealtimeChannel from '../RealtimeChannel'\n\nexport default class Push {\n  sent: boolean = false\n  timeoutTimer: number | undefined = undefined\n  ref: string = ''\n  receivedResp: {\n    status: string\n    response: { [key: string]: any }\n  } | null = null\n  recHooks: {\n    status: string\n    callback: Function\n  }[] = []\n  refEvent: string | null = null\n\n  /**\n   * Initializes the Push\n   *\n   * @param channel The Channel\n   * @param event The event, for example `\"phx_join\"`\n   * @param payload The payload, for example `{user_id: 123}`\n   * @param timeout The push timeout in milliseconds\n   */\n  constructor(\n    public channel: RealtimeChannel,\n    public event: string,\n    public payload: { [key: string]: any } = {},\n    public timeout: number = DEFAULT_TIMEOUT\n  ) {}\n\n  resend(timeout: number) {\n    this.timeout = timeout\n    this._cancelRefEvent()\n    this.ref = ''\n    this.refEvent = null\n    this.receivedResp = null\n    this.sent = false\n    this.send()\n  }\n\n  send() {\n    if (this._hasReceived('timeout')) {\n      return\n    }\n    this.startTimeout()\n    this.sent = true\n    this.channel.socket.push({\n      topic: this.channel.topic,\n      event: this.event,\n      payload: this.payload,\n      ref: this.ref,\n      join_ref: this.channel._joinRef(),\n    })\n  }\n\n  updatePayload(payload: { [key: string]: any }): void {\n    this.payload = { ...this.payload, ...payload }\n  }\n\n  receive(status: string, callback: Function) {\n    if (this._hasReceived(status)) {\n      callback(this.receivedResp?.response)\n    }\n\n    this.recHooks.push({ status, callback })\n    return this\n  }\n\n  startTimeout() {\n    if (this.timeoutTimer) {\n      return\n    }\n    this.ref = this.channel.socket._makeRef()\n    this.refEvent = this.channel._replyEventName(this.ref)\n\n    const callback = (payload: any) => {\n      this._cancelRefEvent()\n      this._cancelTimeout()\n      this.receivedResp = payload\n      this._matchReceive(payload)\n    }\n\n    this.channel._on(this.refEvent, {}, callback)\n\n    this.timeoutTimer = <any>setTimeout(() => {\n      this.trigger('timeout', {})\n    }, this.timeout)\n  }\n\n  trigger(status: string, response: any) {\n    if (this.refEvent)\n      this.channel._trigger(this.refEvent, { status, response })\n  }\n\n  destroy() {\n    this._cancelRefEvent()\n    this._cancelTimeout()\n  }\n\n  private _cancelRefEvent() {\n    if (!this.refEvent) {\n      return\n    }\n\n    this.channel._off(this.refEvent, {})\n  }\n\n  private _cancelTimeout() {\n    clearTimeout(this.timeoutTimer)\n    this.timeoutTimer = undefined\n  }\n\n  private _matchReceive({\n    status,\n    response,\n  }: {\n    status: string\n    response: Function\n  }) {\n    this.recHooks\n      .filter((h) => h.status === status)\n      .forEach((h) => h.callback(response))\n  }\n\n  private _hasReceived(status: string) {\n    return this.receivedResp && this.receivedResp.status === status\n  }\n}\n", "/*\n  This file draws heavily from https://github.com/phoenixframework/phoenix/blob/d344ec0a732ab4ee204215b31de69cf4be72e3bf/assets/js/phoenix/presence.js\n  License: https://github.com/phoenixframework/phoenix/blob/d344ec0a732ab4ee204215b31de69cf4be72e3bf/LICENSE.md\n*/\n\nimport type {\n  PresenceOpts,\n  PresenceOnJoinCallback,\n  PresenceOnLeaveCallback,\n} from 'phoenix'\nimport type RealtimeChannel from './RealtimeChannel'\n\ntype Presence<T extends { [key: string]: any } = {}> = {\n  presence_ref: string\n} & T\n\nexport type RealtimePresenceState<T extends { [key: string]: any } = {}> = {\n  [key: string]: Presence<T>[]\n}\n\nexport type RealtimePresenceJoinPayload<T extends { [key: string]: any }> = {\n  event: `${REALTIME_PRESENCE_LISTEN_EVENTS.JOIN}`\n  key: string\n  currentPresences: Presence<T>[]\n  newPresences: Presence<T>[]\n}\n\nexport type RealtimePresenceLeavePayload<T extends { [key: string]: any }> = {\n  event: `${REALTIME_PRESENCE_LISTEN_EVENTS.LEAVE}`\n  key: string\n  currentPresences: Presence<T>[]\n  leftPresences: Presence<T>[]\n}\n\nexport enum REALTIME_PRESENCE_LISTEN_EVENTS {\n  SYNC = 'sync',\n  JOIN = 'join',\n  LEAVE = 'leave',\n}\n\ntype PresenceDiff = {\n  joins: RealtimePresenceState\n  leaves: RealtimePresenceState\n}\n\ntype RawPresenceState = {\n  [key: string]: {\n    metas: {\n      phx_ref?: string\n      phx_ref_prev?: string\n      [key: string]: any\n    }[]\n  }\n}\n\ntype RawPresenceDiff = {\n  joins: RawPresenceState\n  leaves: RawPresenceState\n}\n\ntype PresenceChooser<T> = (key: string, presences: Presence[]) => T\n\nexport default class RealtimePresence {\n  state: RealtimePresenceState = {}\n  pendingDiffs: RawPresenceDiff[] = []\n  joinRef: string | null = null\n  caller: {\n    onJoin: PresenceOnJoinCallback\n    onLeave: PresenceOnLeaveCallback\n    onSync: () => void\n  } = {\n    onJoin: () => {},\n    onLeave: () => {},\n    onSync: () => {},\n  }\n\n  /**\n   * Initializes the Presence.\n   *\n   * @param channel - The RealtimeChannel\n   * @param opts - The options,\n   *        for example `{events: {state: 'state', diff: 'diff'}}`\n   */\n  constructor(public channel: RealtimeChannel, opts?: PresenceOpts) {\n    const events = opts?.events || {\n      state: 'presence_state',\n      diff: 'presence_diff',\n    }\n\n    this.channel._on(events.state, {}, (newState: RawPresenceState) => {\n      const { onJoin, onLeave, onSync } = this.caller\n\n      this.joinRef = this.channel._joinRef()\n\n      this.state = RealtimePresence.syncState(\n        this.state,\n        newState,\n        onJoin,\n        onLeave\n      )\n\n      this.pendingDiffs.forEach((diff) => {\n        this.state = RealtimePresence.syncDiff(\n          this.state,\n          diff,\n          onJoin,\n          onLeave\n        )\n      })\n\n      this.pendingDiffs = []\n\n      onSync()\n    })\n\n    this.channel._on(events.diff, {}, (diff: RawPresenceDiff) => {\n      const { onJoin, onLeave, onSync } = this.caller\n\n      if (this.inPendingSyncState()) {\n        this.pendingDiffs.push(diff)\n      } else {\n        this.state = RealtimePresence.syncDiff(\n          this.state,\n          diff,\n          onJoin,\n          onLeave\n        )\n\n        onSync()\n      }\n    })\n\n    this.onJoin((key, currentPresences, newPresences) => {\n      this.channel._trigger('presence', {\n        event: 'join',\n        key,\n        currentPresences,\n        newPresences,\n      })\n    })\n\n    this.onLeave((key, currentPresences, leftPresences) => {\n      this.channel._trigger('presence', {\n        event: 'leave',\n        key,\n        currentPresences,\n        leftPresences,\n      })\n    })\n\n    this.onSync(() => {\n      this.channel._trigger('presence', { event: 'sync' })\n    })\n  }\n\n  /**\n   * Used to sync the list of presences on the server with the\n   * client's state.\n   *\n   * An optional `onJoin` and `onLeave` callback can be provided to\n   * react to changes in the client's local presences across\n   * disconnects and reconnects with the server.\n   *\n   * @internal\n   */\n  private static syncState(\n    currentState: RealtimePresenceState,\n    newState: RawPresenceState | RealtimePresenceState,\n    onJoin: PresenceOnJoinCallback,\n    onLeave: PresenceOnLeaveCallback\n  ): RealtimePresenceState {\n    const state = this.cloneDeep(currentState)\n    const transformedState = this.transformState(newState)\n    const joins: RealtimePresenceState = {}\n    const leaves: RealtimePresenceState = {}\n\n    this.map(state, (key: string, presences: Presence[]) => {\n      if (!transformedState[key]) {\n        leaves[key] = presences\n      }\n    })\n\n    this.map(transformedState, (key, newPresences: Presence[]) => {\n      const currentPresences: Presence[] = state[key]\n\n      if (currentPresences) {\n        const newPresenceRefs = newPresences.map(\n          (m: Presence) => m.presence_ref\n        )\n        const curPresenceRefs = currentPresences.map(\n          (m: Presence) => m.presence_ref\n        )\n        const joinedPresences: Presence[] = newPresences.filter(\n          (m: Presence) => curPresenceRefs.indexOf(m.presence_ref) < 0\n        )\n        const leftPresences: Presence[] = currentPresences.filter(\n          (m: Presence) => newPresenceRefs.indexOf(m.presence_ref) < 0\n        )\n\n        if (joinedPresences.length > 0) {\n          joins[key] = joinedPresences\n        }\n\n        if (leftPresences.length > 0) {\n          leaves[key] = leftPresences\n        }\n      } else {\n        joins[key] = newPresences\n      }\n    })\n\n    return this.syncDiff(state, { joins, leaves }, onJoin, onLeave)\n  }\n\n  /**\n   * Used to sync a diff of presence join and leave events from the\n   * server, as they happen.\n   *\n   * Like `syncState`, `syncDiff` accepts optional `onJoin` and\n   * `onLeave` callbacks to react to a user joining or leaving from a\n   * device.\n   *\n   * @internal\n   */\n  private static syncDiff(\n    state: RealtimePresenceState,\n    diff: RawPresenceDiff | PresenceDiff,\n    onJoin: PresenceOnJoinCallback,\n    onLeave: PresenceOnLeaveCallback\n  ): RealtimePresenceState {\n    const { joins, leaves } = {\n      joins: this.transformState(diff.joins),\n      leaves: this.transformState(diff.leaves),\n    }\n\n    if (!onJoin) {\n      onJoin = () => {}\n    }\n\n    if (!onLeave) {\n      onLeave = () => {}\n    }\n\n    this.map(joins, (key, newPresences: Presence[]) => {\n      const currentPresences: Presence[] = state[key] ?? []\n      state[key] = this.cloneDeep(newPresences)\n\n      if (currentPresences.length > 0) {\n        const joinedPresenceRefs = state[key].map(\n          (m: Presence) => m.presence_ref\n        )\n        const curPresences: Presence[] = currentPresences.filter(\n          (m: Presence) => joinedPresenceRefs.indexOf(m.presence_ref) < 0\n        )\n\n        state[key].unshift(...curPresences)\n      }\n\n      onJoin(key, currentPresences, newPresences)\n    })\n\n    this.map(leaves, (key, leftPresences: Presence[]) => {\n      let currentPresences: Presence[] = state[key]\n\n      if (!currentPresences) return\n\n      const presenceRefsToRemove = leftPresences.map(\n        (m: Presence) => m.presence_ref\n      )\n      currentPresences = currentPresences.filter(\n        (m: Presence) => presenceRefsToRemove.indexOf(m.presence_ref) < 0\n      )\n\n      state[key] = currentPresences\n\n      onLeave(key, currentPresences, leftPresences)\n\n      if (currentPresences.length === 0) delete state[key]\n    })\n\n    return state\n  }\n\n  /** @internal */\n  private static map<T = any>(\n    obj: RealtimePresenceState,\n    func: PresenceChooser<T>\n  ): T[] {\n    return Object.getOwnPropertyNames(obj).map((key) => func(key, obj[key]))\n  }\n\n  /**\n   * Remove 'metas' key\n   * Change 'phx_ref' to 'presence_ref'\n   * Remove 'phx_ref' and 'phx_ref_prev'\n   *\n   * @example\n   * // returns {\n   *  abc123: [\n   *    { presence_ref: '2', user_id: 1 },\n   *    { presence_ref: '3', user_id: 2 }\n   *  ]\n   * }\n   * RealtimePresence.transformState({\n   *  abc123: {\n   *    metas: [\n   *      { phx_ref: '2', phx_ref_prev: '1' user_id: 1 },\n   *      { phx_ref: '3', user_id: 2 }\n   *    ]\n   *  }\n   * })\n   *\n   * @internal\n   */\n  private static transformState(\n    state: RawPresenceState | RealtimePresenceState\n  ): RealtimePresenceState {\n    state = this.cloneDeep(state)\n\n    return Object.getOwnPropertyNames(state).reduce((newState, key) => {\n      const presences = state[key]\n\n      if ('metas' in presences) {\n        newState[key] = presences.metas.map((presence) => {\n          presence['presence_ref'] = presence['phx_ref']\n\n          delete presence['phx_ref']\n          delete presence['phx_ref_prev']\n\n          return presence\n        }) as Presence[]\n      } else {\n        newState[key] = presences\n      }\n\n      return newState\n    }, {} as RealtimePresenceState)\n  }\n\n  /** @internal */\n  private static cloneDeep(obj: { [key: string]: any }) {\n    return JSON.parse(JSON.stringify(obj))\n  }\n\n  /** @internal */\n  private onJoin(callback: PresenceOnJoinCallback): void {\n    this.caller.onJoin = callback\n  }\n\n  /** @internal */\n  private onLeave(callback: PresenceOnLeaveCallback): void {\n    this.caller.onLeave = callback\n  }\n\n  /** @internal */\n  private onSync(callback: () => void): void {\n    this.caller.onSync = callback\n  }\n\n  /** @internal */\n  private inPendingSyncState(): boolean {\n    return !this.joinRef || this.joinRef !== this.channel._joinRef()\n  }\n}\n", "import { CHANNEL_EVENTS, CHANNEL_STATES } from './lib/constants'\nimport Push from './lib/push'\nimport type RealtimeClient from './RealtimeClient'\nimport Timer from './lib/timer'\nimport RealtimePresence, {\n  REALTIME_PRESENCE_LISTEN_EVENTS,\n} from './RealtimePresence'\nimport type {\n  RealtimePresenceJoinPayload,\n  RealtimePresenceLeavePayload,\n  RealtimePresenceState,\n} from './RealtimePresence'\nimport * as Transformers from './lib/transformers'\nimport { httpEndpointURL } from './lib/transformers'\n\nexport type RealtimeChannelOptions = {\n  config: {\n    /**\n     * self option enables client to receive message it broadcast\n     * ack option instructs server to acknowledge that broadcast message was received\n     */\n    broadcast?: { self?: boolean; ack?: boolean }\n    /**\n     * key option is used to track presence payload across clients\n     */\n    presence?: { key?: string }\n    /**\n     * defines if the channel is private or not and if RLS policies will be used to check data\n     */\n    private?: boolean\n  }\n}\n\ntype RealtimePostgresChangesPayloadBase = {\n  schema: string\n  table: string\n  commit_timestamp: string\n  errors: string[]\n}\n\nexport type RealtimePostgresInsertPayload<T extends { [key: string]: any }> =\n  RealtimePostgresChangesPayloadBase & {\n    eventType: `${REALTIME_POSTGRES_CHANGES_LISTEN_EVENT.INSERT}`\n    new: T\n    old: {}\n  }\n\nexport type RealtimePostgresUpdatePayload<T extends { [key: string]: any }> =\n  RealtimePostgresChangesPayloadBase & {\n    eventType: `${REALTIME_POSTGRES_CHANGES_LISTEN_EVENT.UPDATE}`\n    new: T\n    old: Partial<T>\n  }\n\nexport type RealtimePostgresDeletePayload<T extends { [key: string]: any }> =\n  RealtimePostgresChangesPayloadBase & {\n    eventType: `${REALTIME_POSTGRES_CHANGES_LISTEN_EVENT.DELETE}`\n    new: {}\n    old: Partial<T>\n  }\n\nexport type RealtimePostgresChangesPayload<T extends { [key: string]: any }> =\n  | RealtimePostgresInsertPayload<T>\n  | RealtimePostgresUpdatePayload<T>\n  | RealtimePostgresDeletePayload<T>\n\nexport type RealtimePostgresChangesFilter<\n  T extends `${REALTIME_POSTGRES_CHANGES_LISTEN_EVENT}`\n> = {\n  /**\n   * The type of database change to listen to.\n   */\n  event: T\n  /**\n   * The database schema to listen to.\n   */\n  schema: string\n  /**\n   * The database table to listen to.\n   */\n  table?: string\n  /**\n   * Receive database changes when filter is matched.\n   */\n  filter?: string\n}\n\nexport type RealtimeChannelSendResponse = 'ok' | 'timed out' | 'error'\n\nexport enum REALTIME_POSTGRES_CHANGES_LISTEN_EVENT {\n  ALL = '*',\n  INSERT = 'INSERT',\n  UPDATE = 'UPDATE',\n  DELETE = 'DELETE',\n}\n\nexport enum REALTIME_LISTEN_TYPES {\n  BROADCAST = 'broadcast',\n  PRESENCE = 'presence',\n  /**\n   * listen to Postgres changes.\n   */\n  POSTGRES_CHANGES = 'postgres_changes',\n}\n\nexport enum REALTIME_SUBSCRIBE_STATES {\n  SUBSCRIBED = 'SUBSCRIBED',\n  TIMED_OUT = 'TIMED_OUT',\n  CLOSED = 'CLOSED',\n  CHANNEL_ERROR = 'CHANNEL_ERROR',\n}\n\nexport const REALTIME_CHANNEL_STATES = CHANNEL_STATES\n\n/** A channel is the basic building block of Realtime\n * and narrows the scope of data flow to subscribed clients.\n * You can think of a channel as a chatroom where participants are able to see who's online\n * and send and receive messages.\n */\nexport default class RealtimeChannel {\n  bindings: {\n    [key: string]: {\n      type: string\n      filter: { [key: string]: any }\n      callback: Function\n      id?: string\n    }[]\n  } = {}\n  timeout: number\n  state = CHANNEL_STATES.closed\n  joinedOnce = false\n  joinPush: Push\n  rejoinTimer: Timer\n  pushBuffer: Push[] = []\n  presence: RealtimePresence\n  broadcastEndpointURL: string\n  subTopic: string\n\n  constructor(\n    /** Topic name can be any string. */\n    public topic: string,\n    public params: RealtimeChannelOptions = { config: {} },\n    public socket: RealtimeClient\n  ) {\n    this.subTopic = topic.replace(/^realtime:/i, '')\n    this.params.config = {\n      ...{\n        broadcast: { ack: false, self: false },\n        presence: { key: '' },\n        private: false,\n      },\n      ...params.config,\n    }\n    this.timeout = this.socket.timeout\n    this.joinPush = new Push(\n      this,\n      CHANNEL_EVENTS.join,\n      this.params,\n      this.timeout\n    )\n    this.rejoinTimer = new Timer(\n      () => this._rejoinUntilConnected(),\n      this.socket.reconnectAfterMs\n    )\n    this.joinPush.receive('ok', () => {\n      this.state = CHANNEL_STATES.joined\n      this.rejoinTimer.reset()\n      this.pushBuffer.forEach((pushEvent: Push) => pushEvent.send())\n      this.pushBuffer = []\n    })\n    this._onClose(() => {\n      this.rejoinTimer.reset()\n      this.socket.log('channel', `close ${this.topic} ${this._joinRef()}`)\n      this.state = CHANNEL_STATES.closed\n      this.socket._remove(this)\n    })\n    this._onError((reason: string) => {\n      if (this._isLeaving() || this._isClosed()) {\n        return\n      }\n      this.socket.log('channel', `error ${this.topic}`, reason)\n      this.state = CHANNEL_STATES.errored\n      this.rejoinTimer.scheduleTimeout()\n    })\n    this.joinPush.receive('timeout', () => {\n      if (!this._isJoining()) {\n        return\n      }\n      this.socket.log('channel', `timeout ${this.topic}`, this.joinPush.timeout)\n      this.state = CHANNEL_STATES.errored\n      this.rejoinTimer.scheduleTimeout()\n    })\n    this._on(CHANNEL_EVENTS.reply, {}, (payload: any, ref: string) => {\n      this._trigger(this._replyEventName(ref), payload)\n    })\n\n    this.presence = new RealtimePresence(this)\n\n    this.broadcastEndpointURL =\n      httpEndpointURL(this.socket.endPoint) + '/api/broadcast'\n  }\n\n  /** Subscribe registers your client with the server */\n  subscribe(\n    callback?: (status: `${REALTIME_SUBSCRIBE_STATES}`, err?: Error) => void,\n    timeout = this.timeout\n  ): RealtimeChannel {\n    if (!this.socket.isConnected()) {\n      this.socket.connect()\n    }\n\n    if (this.joinedOnce) {\n      throw `tried to subscribe multiple times. 'subscribe' can only be called a single time per channel instance`\n    } else {\n      const {\n        config: { broadcast, presence, private: isPrivate },\n      } = this.params\n      this._onError((e: Error) => callback && callback('CHANNEL_ERROR', e))\n      this._onClose(() => callback && callback('CLOSED'))\n\n      const accessTokenPayload: { access_token?: string } = {}\n      const config = {\n        broadcast,\n        presence,\n        postgres_changes:\n          this.bindings.postgres_changes?.map((r) => r.filter) ?? [],\n        private: isPrivate,\n      }\n\n      if (this.socket.accessToken) {\n        accessTokenPayload.access_token = this.socket.accessToken\n      }\n\n      this.updateJoinPayload({ ...{ config }, ...accessTokenPayload })\n\n      this.joinedOnce = true\n      this._rejoin(timeout)\n\n      this.joinPush\n        .receive(\n          'ok',\n          ({\n            postgres_changes: serverPostgresFilters,\n          }: {\n            postgres_changes: {\n              id: string\n              event: string\n              schema?: string\n              table?: string\n              filter?: string\n            }[]\n          }) => {\n            this.socket.accessToken &&\n              this.socket.setAuth(this.socket.accessToken)\n\n            if (serverPostgresFilters === undefined) {\n              callback && callback('SUBSCRIBED')\n              return\n            } else {\n              const clientPostgresBindings = this.bindings.postgres_changes\n              const bindingsLen = clientPostgresBindings?.length ?? 0\n              const newPostgresBindings = []\n\n              for (let i = 0; i < bindingsLen; i++) {\n                const clientPostgresBinding = clientPostgresBindings[i]\n                const {\n                  filter: { event, schema, table, filter },\n                } = clientPostgresBinding\n                const serverPostgresFilter =\n                  serverPostgresFilters && serverPostgresFilters[i]\n\n                if (\n                  serverPostgresFilter &&\n                  serverPostgresFilter.event === event &&\n                  serverPostgresFilter.schema === schema &&\n                  serverPostgresFilter.table === table &&\n                  serverPostgresFilter.filter === filter\n                ) {\n                  newPostgresBindings.push({\n                    ...clientPostgresBinding,\n                    id: serverPostgresFilter.id,\n                  })\n                } else {\n                  this.unsubscribe()\n                  callback &&\n                    callback(\n                      'CHANNEL_ERROR',\n                      new Error(\n                        'mismatch between server and client bindings for postgres changes'\n                      )\n                    )\n                  return\n                }\n              }\n\n              this.bindings.postgres_changes = newPostgresBindings\n\n              callback && callback('SUBSCRIBED')\n              return\n            }\n          }\n        )\n        .receive('error', (error: { [key: string]: any }) => {\n          callback &&\n            callback(\n              'CHANNEL_ERROR',\n              new Error(\n                JSON.stringify(Object.values(error).join(', ') || 'error')\n              )\n            )\n          return\n        })\n        .receive('timeout', () => {\n          callback && callback('TIMED_OUT')\n          return\n        })\n    }\n\n    return this\n  }\n\n  presenceState<\n    T extends { [key: string]: any } = {}\n  >(): RealtimePresenceState<T> {\n    return this.presence.state as RealtimePresenceState<T>\n  }\n\n  async track(\n    payload: { [key: string]: any },\n    opts: { [key: string]: any } = {}\n  ): Promise<RealtimeChannelSendResponse> {\n    return await this.send(\n      {\n        type: 'presence',\n        event: 'track',\n        payload,\n      },\n      opts.timeout || this.timeout\n    )\n  }\n\n  async untrack(\n    opts: { [key: string]: any } = {}\n  ): Promise<RealtimeChannelSendResponse> {\n    return await this.send(\n      {\n        type: 'presence',\n        event: 'untrack',\n      },\n      opts\n    )\n  }\n\n  /**\n   * Creates an event handler that listens to changes.\n   */\n  on(\n    type: `${REALTIME_LISTEN_TYPES.PRESENCE}`,\n    filter: { event: `${REALTIME_PRESENCE_LISTEN_EVENTS.SYNC}` },\n    callback: () => void\n  ): RealtimeChannel\n  on<T extends { [key: string]: any }>(\n    type: `${REALTIME_LISTEN_TYPES.PRESENCE}`,\n    filter: { event: `${REALTIME_PRESENCE_LISTEN_EVENTS.JOIN}` },\n    callback: (payload: RealtimePresenceJoinPayload<T>) => void\n  ): RealtimeChannel\n  on<T extends { [key: string]: any }>(\n    type: `${REALTIME_LISTEN_TYPES.PRESENCE}`,\n    filter: { event: `${REALTIME_PRESENCE_LISTEN_EVENTS.LEAVE}` },\n    callback: (payload: RealtimePresenceLeavePayload<T>) => void\n  ): RealtimeChannel\n  on<T extends { [key: string]: any }>(\n    type: `${REALTIME_LISTEN_TYPES.POSTGRES_CHANGES}`,\n    filter: RealtimePostgresChangesFilter<`${REALTIME_POSTGRES_CHANGES_LISTEN_EVENT.ALL}`>,\n    callback: (payload: RealtimePostgresChangesPayload<T>) => void\n  ): RealtimeChannel\n  on<T extends { [key: string]: any }>(\n    type: `${REALTIME_LISTEN_TYPES.POSTGRES_CHANGES}`,\n    filter: RealtimePostgresChangesFilter<`${REALTIME_POSTGRES_CHANGES_LISTEN_EVENT.INSERT}`>,\n    callback: (payload: RealtimePostgresInsertPayload<T>) => void\n  ): RealtimeChannel\n  on<T extends { [key: string]: any }>(\n    type: `${REALTIME_LISTEN_TYPES.POSTGRES_CHANGES}`,\n    filter: RealtimePostgresChangesFilter<`${REALTIME_POSTGRES_CHANGES_LISTEN_EVENT.UPDATE}`>,\n    callback: (payload: RealtimePostgresUpdatePayload<T>) => void\n  ): RealtimeChannel\n  on<T extends { [key: string]: any }>(\n    type: `${REALTIME_LISTEN_TYPES.POSTGRES_CHANGES}`,\n    filter: RealtimePostgresChangesFilter<`${REALTIME_POSTGRES_CHANGES_LISTEN_EVENT.DELETE}`>,\n    callback: (payload: RealtimePostgresDeletePayload<T>) => void\n  ): RealtimeChannel\n  /**\n   * The following is placed here to display on supabase.com/docs/reference/javascript/subscribe.\n   * @param type One of \"broadcast\", \"presence\", or \"postgres_changes\".\n   * @param filter Custom object specific to the Realtime feature detailing which payloads to receive.\n   * @param callback Function to be invoked when event handler is triggered.\n   */\n  on(\n    type: `${REALTIME_LISTEN_TYPES.BROADCAST}`,\n    filter: { event: string },\n    callback: (payload: {\n      type: `${REALTIME_LISTEN_TYPES.BROADCAST}`\n      event: string\n      [key: string]: any\n    }) => void\n  ): RealtimeChannel\n  on<T extends { [key: string]: any }>(\n    type: `${REALTIME_LISTEN_TYPES.BROADCAST}`,\n    filter: { event: string },\n    callback: (payload: {\n      type: `${REALTIME_LISTEN_TYPES.BROADCAST}`\n      event: string\n      payload: T\n    }) => void\n  ): RealtimeChannel\n  on(\n    type: `${REALTIME_LISTEN_TYPES}`,\n    filter: { event: string; [key: string]: string },\n    callback: (payload: any) => void\n  ): RealtimeChannel {\n    return this._on(type, filter, callback)\n  }\n  /**\n   * Sends a message into the channel.\n   *\n   * @param args Arguments to send to channel\n   * @param args.type The type of event to send\n   * @param args.event The name of the event being sent\n   * @param args.payload Payload to be sent\n   * @param opts Options to be used during the send process\n   */\n  async send(\n    args: {\n      type: 'broadcast' | 'presence' | 'postgres_changes'\n      event: string\n      payload?: any\n      [key: string]: any\n    },\n    opts: { [key: string]: any } = {}\n  ): Promise<RealtimeChannelSendResponse> {\n    if (!this._canPush() && args.type === 'broadcast') {\n      const { event, payload: endpoint_payload } = args\n      const options = {\n        method: 'POST',\n        headers: {\n          Authorization: this.socket.accessToken\n            ? `Bearer ${this.socket.accessToken}`\n            : '',\n          apikey: this.socket.apiKey ? this.socket.apiKey : '',\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          messages: [\n            { topic: this.subTopic, event, payload: endpoint_payload },\n          ],\n        }),\n      }\n\n      try {\n        const response = await this._fetchWithTimeout(\n          this.broadcastEndpointURL,\n          options,\n          opts.timeout ?? this.timeout\n        )\n\n        await response.body?.cancel()\n        return response.ok ? 'ok' : 'error'\n      } catch (error: any) {\n        if (error.name === 'AbortError') {\n          return 'timed out'\n        } else {\n          return 'error'\n        }\n      }\n    } else {\n      return new Promise((resolve) => {\n        const push = this._push(args.type, args, opts.timeout || this.timeout)\n\n        if (args.type === 'broadcast' && !this.params?.config?.broadcast?.ack) {\n          resolve('ok')\n        }\n\n        push.receive('ok', () => resolve('ok'))\n        push.receive('error', () => resolve('error'))\n        push.receive('timeout', () => resolve('timed out'))\n      })\n    }\n  }\n\n  updateJoinPayload(payload: { [key: string]: any }): void {\n    this.joinPush.updatePayload(payload)\n  }\n\n  /**\n   * Leaves the channel.\n   *\n   * Unsubscribes from server events, and instructs channel to terminate on server.\n   * Triggers onClose() hooks.\n   *\n   * To receive leave acknowledgements, use the a `receive` hook to bind to the server ack, ie:\n   * channel.unsubscribe().receive(\"ok\", () => alert(\"left!\") )\n   */\n  unsubscribe(timeout = this.timeout): Promise<'ok' | 'timed out' | 'error'> {\n    this.state = CHANNEL_STATES.leaving\n    const onClose = () => {\n      this.socket.log('channel', `leave ${this.topic}`)\n      this._trigger(CHANNEL_EVENTS.close, 'leave', this._joinRef())\n    }\n\n    this.rejoinTimer.reset()\n    // Destroy joinPush to avoid connection timeouts during unscription phase\n    this.joinPush.destroy()\n\n    return new Promise((resolve) => {\n      const leavePush = new Push(this, CHANNEL_EVENTS.leave, {}, timeout)\n\n      leavePush\n        .receive('ok', () => {\n          onClose()\n          resolve('ok')\n        })\n        .receive('timeout', () => {\n          onClose()\n          resolve('timed out')\n        })\n        .receive('error', () => {\n          resolve('error')\n        })\n\n      leavePush.send()\n\n      if (!this._canPush()) {\n        leavePush.trigger('ok', {})\n      }\n    })\n  }\n\n  /** @internal */\n\n  async _fetchWithTimeout(\n    url: string,\n    options: { [key: string]: any },\n    timeout: number\n  ) {\n    const controller = new AbortController()\n    const id = setTimeout(() => controller.abort(), timeout)\n\n    const response = await this.socket.fetch(url, {\n      ...options,\n      signal: controller.signal,\n    })\n\n    clearTimeout(id)\n\n    return response\n  }\n\n  /** @internal */\n  _push(\n    event: string,\n    payload: { [key: string]: any },\n    timeout = this.timeout\n  ) {\n    if (!this.joinedOnce) {\n      throw `tried to push '${event}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`\n    }\n    let pushEvent = new Push(this, event, payload, timeout)\n    if (this._canPush()) {\n      pushEvent.send()\n    } else {\n      pushEvent.startTimeout()\n      this.pushBuffer.push(pushEvent)\n    }\n\n    return pushEvent\n  }\n\n  /**\n   * Overridable message hook\n   *\n   * Receives all events for specialized message handling before dispatching to the channel callbacks.\n   * Must return the payload, modified or unmodified.\n   *\n   * @internal\n   */\n  _onMessage(_event: string, payload: any, _ref?: string) {\n    return payload\n  }\n\n  /** @internal */\n  _isMember(topic: string): boolean {\n    return this.topic === topic\n  }\n\n  /** @internal */\n  _joinRef(): string {\n    return this.joinPush.ref\n  }\n\n  /** @internal */\n  _trigger(type: string, payload?: any, ref?: string) {\n    const typeLower = type.toLocaleLowerCase()\n    const { close, error, leave, join } = CHANNEL_EVENTS\n    const events: string[] = [close, error, leave, join]\n    if (ref && events.indexOf(typeLower) >= 0 && ref !== this._joinRef()) {\n      return\n    }\n    let handledPayload = this._onMessage(typeLower, payload, ref)\n    if (payload && !handledPayload) {\n      throw 'channel onMessage callbacks must return the payload, modified or unmodified'\n    }\n\n    if (['insert', 'update', 'delete'].includes(typeLower)) {\n      this.bindings.postgres_changes\n        ?.filter((bind) => {\n          return (\n            bind.filter?.event === '*' ||\n            bind.filter?.event?.toLocaleLowerCase() === typeLower\n          )\n        })\n        .map((bind) => bind.callback(handledPayload, ref))\n    } else {\n      this.bindings[typeLower]\n        ?.filter((bind) => {\n          if (\n            ['broadcast', 'presence', 'postgres_changes'].includes(typeLower)\n          ) {\n            if ('id' in bind) {\n              const bindId = bind.id\n              const bindEvent = bind.filter?.event\n              return (\n                bindId &&\n                payload.ids?.includes(bindId) &&\n                (bindEvent === '*' ||\n                  bindEvent?.toLocaleLowerCase() ===\n                    payload.data?.type.toLocaleLowerCase())\n              )\n            } else {\n              const bindEvent = bind?.filter?.event?.toLocaleLowerCase()\n              return (\n                bindEvent === '*' ||\n                bindEvent === payload?.event?.toLocaleLowerCase()\n              )\n            }\n          } else {\n            return bind.type.toLocaleLowerCase() === typeLower\n          }\n        })\n        .map((bind) => {\n          if (typeof handledPayload === 'object' && 'ids' in handledPayload) {\n            const postgresChanges = handledPayload.data\n            const { schema, table, commit_timestamp, type, errors } =\n              postgresChanges\n            const enrichedPayload = {\n              schema: schema,\n              table: table,\n              commit_timestamp: commit_timestamp,\n              eventType: type,\n              new: {},\n              old: {},\n              errors: errors,\n            }\n            handledPayload = {\n              ...enrichedPayload,\n              ...this._getPayloadRecords(postgresChanges),\n            }\n          }\n          bind.callback(handledPayload, ref)\n        })\n    }\n  }\n\n  /** @internal */\n  _isClosed(): boolean {\n    return this.state === CHANNEL_STATES.closed\n  }\n\n  /** @internal */\n  _isJoined(): boolean {\n    return this.state === CHANNEL_STATES.joined\n  }\n\n  /** @internal */\n  _isJoining(): boolean {\n    return this.state === CHANNEL_STATES.joining\n  }\n\n  /** @internal */\n  _isLeaving(): boolean {\n    return this.state === CHANNEL_STATES.leaving\n  }\n\n  /** @internal */\n  _replyEventName(ref: string): string {\n    return `chan_reply_${ref}`\n  }\n\n  /** @internal */\n  _on(type: string, filter: { [key: string]: any }, callback: Function) {\n    const typeLower = type.toLocaleLowerCase()\n\n    const binding = {\n      type: typeLower,\n      filter: filter,\n      callback: callback,\n    }\n\n    if (this.bindings[typeLower]) {\n      this.bindings[typeLower].push(binding)\n    } else {\n      this.bindings[typeLower] = [binding]\n    }\n\n    return this\n  }\n\n  /** @internal */\n  _off(type: string, filter: { [key: string]: any }) {\n    const typeLower = type.toLocaleLowerCase()\n\n    this.bindings[typeLower] = this.bindings[typeLower].filter((bind) => {\n      return !(\n        bind.type?.toLocaleLowerCase() === typeLower &&\n        RealtimeChannel.isEqual(bind.filter, filter)\n      )\n    })\n    return this\n  }\n\n  /** @internal */\n  private static isEqual(\n    obj1: { [key: string]: string },\n    obj2: { [key: string]: string }\n  ) {\n    if (Object.keys(obj1).length !== Object.keys(obj2).length) {\n      return false\n    }\n\n    for (const k in obj1) {\n      if (obj1[k] !== obj2[k]) {\n        return false\n      }\n    }\n\n    return true\n  }\n\n  /** @internal */\n  private _rejoinUntilConnected() {\n    this.rejoinTimer.scheduleTimeout()\n    if (this.socket.isConnected()) {\n      this._rejoin()\n    }\n  }\n\n  /**\n   * Registers a callback that will be executed when the channel closes.\n   *\n   * @internal\n   */\n  private _onClose(callback: Function) {\n    this._on(CHANNEL_EVENTS.close, {}, callback)\n  }\n\n  /**\n   * Registers a callback that will be executed when the channel encounteres an error.\n   *\n   * @internal\n   */\n  private _onError(callback: Function) {\n    this._on(CHANNEL_EVENTS.error, {}, (reason: string) => callback(reason))\n  }\n\n  /**\n   * Returns `true` if the socket is connected and the channel has been joined.\n   *\n   * @internal\n   */\n  private _canPush(): boolean {\n    return this.socket.isConnected() && this._isJoined()\n  }\n\n  /** @internal */\n  private _rejoin(timeout = this.timeout): void {\n    if (this._isLeaving()) {\n      return\n    }\n    this.socket._leaveOpenTopic(this.topic)\n    this.state = CHANNEL_STATES.joining\n    this.joinPush.resend(timeout)\n  }\n\n  /** @internal */\n  private _getPayloadRecords(payload: any) {\n    const records = {\n      new: {},\n      old: {},\n    }\n\n    if (payload.type === 'INSERT' || payload.type === 'UPDATE') {\n      records.new = Transformers.convertChangeData(\n        payload.columns,\n        payload.record\n      )\n    }\n\n    if (payload.type === 'UPDATE' || payload.type === 'DELETE') {\n      records.old = Transformers.convertChangeData(\n        payload.columns,\n        payload.old_record\n      )\n    }\n\n    return records\n  }\n}\n", "import type { WebSocket as WSWebSocket } from 'ws'\n\nimport {\n  CHANNEL_EVENTS,\n  CONNECTION_STATE,\n  DEFAULT_HEADERS,\n  DEFAULT_TIMEOUT,\n  SOCKET_STATES,\n  TRANSPORTS,\n  VSN,\n  WS_CLOSE_NORMAL,\n} from './lib/constants'\nimport Serializer from './lib/serializer'\nimport Timer from './lib/timer'\n\nimport { httpEndpointURL } from './lib/transformers'\nimport RealtimeChannel from './RealtimeChannel'\nimport type { RealtimeChannelOptions } from './RealtimeChannel'\n\ntype Fetch = typeof fetch\n\nexport type Channel = {\n  name: string\n  inserted_at: string\n  updated_at: string\n  id: number\n}\n\nexport type RealtimeClientOptions = {\n  transport?: WebSocketLikeConstructor\n  timeout?: number\n  heartbeatIntervalMs?: number\n  logger?: Function\n  encode?: Function\n  decode?: Function\n  reconnectAfterMs?: Function\n  headers?: { [key: string]: string }\n  params?: { [key: string]: any }\n  log_level?: 'info' | 'debug' | 'warn' | 'error'\n  fetch?: Fetch\n}\n\nexport type RealtimeMessage = {\n  topic: string\n  event: string\n  payload: any\n  ref: string\n  join_ref?: string\n}\n\nexport type RealtimeRemoveChannelResponse = 'ok' | 'timed out' | 'error'\n\nconst noop = () => {}\n\ninterface WebSocketLikeConstructor {\n  new (\n    address: string | URL,\n    _ignored?: any,\n    options?: { headers: Object | undefined }\n  ): WebSocketLike\n}\n\ntype WebSocketLike = WebSocket | WSWebSocket | WSWebSocketDummy\n\ninterface WebSocketLikeError {\n  error: any\n  message: string\n  type: string\n}\n\nconst NATIVE_WEBSOCKET_AVAILABLE = typeof WebSocket !== 'undefined'\n\nexport default class RealtimeClient {\n  accessToken: string | null = null\n  apiKey: string | null = null\n  channels: RealtimeChannel[] = []\n  endPoint: string = ''\n  httpEndpoint: string = ''\n  headers?: { [key: string]: string } = DEFAULT_HEADERS\n  params?: { [key: string]: string } = {}\n  timeout: number = DEFAULT_TIMEOUT\n  transport: WebSocketLikeConstructor | null\n  heartbeatIntervalMs: number = 30000\n  heartbeatTimer: ReturnType<typeof setInterval> | undefined = undefined\n  pendingHeartbeatRef: string | null = null\n  ref: number = 0\n  reconnectTimer: Timer\n  logger: Function = noop\n  encode: Function\n  decode: Function\n  reconnectAfterMs: Function\n  conn: WebSocketLike | null = null\n  sendBuffer: Function[] = []\n  serializer: Serializer = new Serializer()\n  stateChangeCallbacks: {\n    open: Function[]\n    close: Function[]\n    error: Function[]\n    message: Function[]\n  } = {\n    open: [],\n    close: [],\n    error: [],\n    message: [],\n  }\n  fetch: Fetch\n\n  /**\n   * Initializes the Socket.\n   *\n   * @param endPoint The string WebSocket endpoint, ie, \"ws://example.com/socket\", \"wss://example.com\", \"/socket\" (inherited host & protocol)\n   * @param httpEndpoint The string HTTP endpoint, ie, \"https://example.com\", \"/\" (inherited host & protocol)\n   * @param options.transport The Websocket Transport, for example WebSocket.\n   * @param options.timeout The default timeout in milliseconds to trigger push timeouts.\n   * @param options.params The optional params to pass when connecting.\n   * @param options.headers The optional headers to pass when connecting.\n   * @param options.heartbeatIntervalMs The millisec interval to send a heartbeat message.\n   * @param options.logger The optional function for specialized logging, ie: logger: (kind, msg, data) => { console.log(`${kind}: ${msg}`, data) }\n   * @param options.encode The function to encode outgoing messages. Defaults to JSON: (payload, callback) => callback(JSON.stringify(payload))\n   * @param options.decode The function to decode incoming messages. Defaults to Serializer's decode.\n   * @param options.reconnectAfterMs he optional function that returns the millsec reconnect interval. Defaults to stepped backoff off.\n   */\n  constructor(endPoint: string, options?: RealtimeClientOptions) {\n    this.endPoint = `${endPoint}/${TRANSPORTS.websocket}`\n    this.httpEndpoint = httpEndpointURL(endPoint)\n    if (options?.transport) {\n      this.transport = options.transport\n    } else {\n      this.transport = null\n    }\n    if (options?.params) this.params = options.params\n    if (options?.headers) this.headers = { ...this.headers, ...options.headers }\n    if (options?.timeout) this.timeout = options.timeout\n    if (options?.logger) this.logger = options.logger\n    if (options?.heartbeatIntervalMs)\n      this.heartbeatIntervalMs = options.heartbeatIntervalMs\n\n    const accessToken = options?.params?.apikey\n    if (accessToken) {\n      this.accessToken = accessToken\n      this.apiKey = accessToken\n    }\n\n    this.reconnectAfterMs = options?.reconnectAfterMs\n      ? options.reconnectAfterMs\n      : (tries: number) => {\n          return [1000, 2000, 5000, 10000][tries - 1] || 10000\n        }\n    this.encode = options?.encode\n      ? options.encode\n      : (payload: JSON, callback: Function) => {\n          return callback(JSON.stringify(payload))\n        }\n    this.decode = options?.decode\n      ? options.decode\n      : this.serializer.decode.bind(this.serializer)\n    this.reconnectTimer = new Timer(async () => {\n      this.disconnect()\n      this.connect()\n    }, this.reconnectAfterMs)\n\n    this.fetch = this._resolveFetch(options?.fetch)\n  }\n\n  /**\n   * Connects the socket, unless already connected.\n   */\n  connect(): void {\n    if (this.conn) {\n      return\n    }\n\n    if (this.transport) {\n      this.conn = new this.transport(this._endPointURL(), undefined, {\n        headers: this.headers,\n      })\n      return\n    }\n\n    if (NATIVE_WEBSOCKET_AVAILABLE) {\n      this.conn = new WebSocket(this._endPointURL())\n      this.setupConnection()\n      return\n    }\n\n    this.conn = new WSWebSocketDummy(this._endPointURL(), undefined, {\n      close: () => {\n        this.conn = null\n      },\n    })\n\n    import('ws').then(({ default: WS }) => {\n      this.conn = new WS(this._endPointURL(), undefined, {\n        headers: this.headers,\n      })\n      this.setupConnection()\n    })\n  }\n\n  /**\n   * Disconnects the socket.\n   *\n   * @param code A numeric status code to send on disconnect.\n   * @param reason A custom reason for the disconnect.\n   */\n  disconnect(code?: number, reason?: string): void {\n    if (this.conn) {\n      this.conn.onclose = function () {} // noop\n      if (code) {\n        this.conn.close(code, reason ?? '')\n      } else {\n        this.conn.close()\n      }\n      this.conn = null\n      // remove open handles\n      this.heartbeatTimer && clearInterval(this.heartbeatTimer)\n      this.reconnectTimer.reset()\n    }\n  }\n\n  /**\n   * Returns all created channels\n   */\n  getChannels(): RealtimeChannel[] {\n    return this.channels\n  }\n\n  /**\n   * Unsubscribes and removes a single channel\n   * @param channel A RealtimeChannel instance\n   */\n  async removeChannel(\n    channel: RealtimeChannel\n  ): Promise<RealtimeRemoveChannelResponse> {\n    const status = await channel.unsubscribe()\n    if (this.channels.length === 0) {\n      this.disconnect()\n    }\n    return status\n  }\n\n  /**\n   * Unsubscribes and removes all channels\n   */\n  async removeAllChannels(): Promise<RealtimeRemoveChannelResponse[]> {\n    const values_1 = await Promise.all(\n      this.channels.map((channel) => channel.unsubscribe())\n    )\n    this.disconnect()\n    return values_1\n  }\n\n  /**\n   * Logs the message.\n   *\n   * For customized logging, `this.logger` can be overridden.\n   */\n  log(kind: string, msg: string, data?: any) {\n    this.logger(kind, msg, data)\n  }\n\n  /**\n   * Returns the current state of the socket.\n   */\n  connectionState(): CONNECTION_STATE {\n    switch (this.conn && this.conn.readyState) {\n      case SOCKET_STATES.connecting:\n        return CONNECTION_STATE.Connecting\n      case SOCKET_STATES.open:\n        return CONNECTION_STATE.Open\n      case SOCKET_STATES.closing:\n        return CONNECTION_STATE.Closing\n      default:\n        return CONNECTION_STATE.Closed\n    }\n  }\n\n  /**\n   * Returns `true` is the connection is open.\n   */\n  isConnected(): boolean {\n    return this.connectionState() === CONNECTION_STATE.Open\n  }\n\n  channel(\n    topic: string,\n    params: RealtimeChannelOptions = { config: {} }\n  ): RealtimeChannel {\n    const chan = new RealtimeChannel(`realtime:${topic}`, params, this)\n    this.channels.push(chan)\n    return chan\n  }\n\n  /**\n   * Push out a message if the socket is connected.\n   *\n   * If the socket is not connected, the message gets enqueued within a local buffer, and sent out when a connection is next established.\n   */\n  push(data: RealtimeMessage): void {\n    const { topic, event, payload, ref } = data\n    const callback = () => {\n      this.encode(data, (result: any) => {\n        this.conn?.send(result)\n      })\n    }\n    this.log('push', `${topic} ${event} (${ref})`, payload)\n    if (this.isConnected()) {\n      callback()\n    } else {\n      this.sendBuffer.push(callback)\n    }\n  }\n\n  /**\n   * Sets the JWT access token used for channel subscription authorization and Realtime RLS.\n   *\n   * @param token A JWT string.\n   */\n  setAuth(token: string | null): void {\n    this.accessToken = token\n\n    this.channels.forEach((channel) => {\n      token && channel.updateJoinPayload({ access_token: token })\n\n      if (channel.joinedOnce && channel._isJoined()) {\n        channel._push(CHANNEL_EVENTS.access_token, { access_token: token })\n      }\n    })\n  }\n\n  /**\n   * Use either custom fetch, if provided, or default fetch to make HTTP requests\n   *\n   * @internal\n   */\n  _resolveFetch = (customFetch?: Fetch): Fetch => {\n    let _fetch: Fetch\n    if (customFetch) {\n      _fetch = customFetch\n    } else if (typeof fetch === 'undefined') {\n      _fetch = (...args) =>\n        import('@supabase/node-fetch' as any).then(({ default: fetch }) =>\n          fetch(...args)\n        )\n    } else {\n      _fetch = fetch\n    }\n    return (...args) => _fetch(...args)\n  }\n\n  /**\n   * Return the next message ref, accounting for overflows\n   *\n   * @internal\n   */\n  _makeRef(): string {\n    let newRef = this.ref + 1\n    if (newRef === this.ref) {\n      this.ref = 0\n    } else {\n      this.ref = newRef\n    }\n\n    return this.ref.toString()\n  }\n\n  /**\n   * Unsubscribe from channels with the specified topic.\n   *\n   * @internal\n   */\n  _leaveOpenTopic(topic: string): void {\n    let dupChannel = this.channels.find(\n      (c) => c.topic === topic && (c._isJoined() || c._isJoining())\n    )\n    if (dupChannel) {\n      this.log('transport', `leaving duplicate topic \"${topic}\"`)\n      dupChannel.unsubscribe()\n    }\n  }\n\n  /**\n   * Removes a subscription from the socket.\n   *\n   * @param channel An open subscription.\n   *\n   * @internal\n   */\n  _remove(channel: RealtimeChannel) {\n    this.channels = this.channels.filter(\n      (c: RealtimeChannel) => c._joinRef() !== channel._joinRef()\n    )\n  }\n\n  /**\n   * Sets up connection handlers.\n   *\n   * @internal\n   */\n  private setupConnection(): void {\n    if (this.conn) {\n      this.conn.binaryType = 'arraybuffer'\n      this.conn.onopen = () => this._onConnOpen()\n      this.conn.onerror = (error: WebSocketLikeError) =>\n        this._onConnError(error as WebSocketLikeError)\n      this.conn.onmessage = (event: any) => this._onConnMessage(event)\n      this.conn.onclose = (event: any) => this._onConnClose(event)\n    }\n  }\n\n  /**\n   * Returns the URL of the websocket.\n   *\n   * @internal\n   */\n  private _endPointURL(): string {\n    return this._appendParams(\n      this.endPoint,\n      Object.assign({}, this.params, { vsn: VSN })\n    )\n  }\n\n  /** @internal */\n  private _onConnMessage(rawMessage: { data: any }) {\n    this.decode(rawMessage.data, (msg: RealtimeMessage) => {\n      let { topic, event, payload, ref } = msg\n\n      if (\n        (ref && ref === this.pendingHeartbeatRef) ||\n        event === payload?.type\n      ) {\n        this.pendingHeartbeatRef = null\n      }\n\n      this.log(\n        'receive',\n        `${payload.status || ''} ${topic} ${event} ${\n          (ref && '(' + ref + ')') || ''\n        }`,\n        payload\n      )\n      this.channels\n        .filter((channel: RealtimeChannel) => channel._isMember(topic))\n        .forEach((channel: RealtimeChannel) =>\n          channel._trigger(event, payload, ref)\n        )\n      this.stateChangeCallbacks.message.forEach((callback) => callback(msg))\n    })\n  }\n\n  /** @internal */\n  private _onConnOpen() {\n    this.log('transport', `connected to ${this._endPointURL()}`)\n    this._flushSendBuffer()\n    this.reconnectTimer.reset()\n    this.heartbeatTimer && clearInterval(this.heartbeatTimer)\n    this.heartbeatTimer = setInterval(\n      () => this._sendHeartbeat(),\n      this.heartbeatIntervalMs\n    )\n    this.stateChangeCallbacks.open.forEach((callback) => callback())!\n  }\n\n  /** @internal */\n  private _onConnClose(event: any) {\n    this.log('transport', 'close', event)\n    this._triggerChanError()\n    this.heartbeatTimer && clearInterval(this.heartbeatTimer)\n    this.reconnectTimer.scheduleTimeout()\n    this.stateChangeCallbacks.close.forEach((callback) => callback(event))\n  }\n\n  /** @internal */\n  private _onConnError(error: WebSocketLikeError) {\n    this.log('transport', error.message)\n    this._triggerChanError()\n    this.stateChangeCallbacks.error.forEach((callback) => callback(error))\n  }\n\n  /** @internal */\n  private _triggerChanError() {\n    this.channels.forEach((channel: RealtimeChannel) =>\n      channel._trigger(CHANNEL_EVENTS.error)\n    )\n  }\n\n  /** @internal */\n  private _appendParams(\n    url: string,\n    params: { [key: string]: string }\n  ): string {\n    if (Object.keys(params).length === 0) {\n      return url\n    }\n    const prefix = url.match(/\\?/) ? '&' : '?'\n    const query = new URLSearchParams(params)\n\n    return `${url}${prefix}${query}`\n  }\n\n  /** @internal */\n  private _flushSendBuffer() {\n    if (this.isConnected() && this.sendBuffer.length > 0) {\n      this.sendBuffer.forEach((callback) => callback())\n      this.sendBuffer = []\n    }\n  }\n  /** @internal */\n  private _sendHeartbeat() {\n    if (!this.isConnected()) {\n      return\n    }\n    if (this.pendingHeartbeatRef) {\n      this.pendingHeartbeatRef = null\n      this.log(\n        'transport',\n        'heartbeat timeout. Attempting to re-establish connection'\n      )\n      this.conn?.close(WS_CLOSE_NORMAL, 'hearbeat timeout')\n      return\n    }\n    this.pendingHeartbeatRef = this._makeRef()\n    this.push({\n      topic: 'phoenix',\n      event: 'heartbeat',\n      payload: {},\n      ref: this.pendingHeartbeatRef,\n    })\n    this.setAuth(this.accessToken)\n  }\n}\n\nclass WSWebSocketDummy {\n  binaryType: string = 'arraybuffer'\n  close: Function\n  onclose: Function = () => {}\n  onerror: Function = () => {}\n  onmessage: Function = () => {}\n  onopen: Function = () => {}\n  readyState: number = SOCKET_STATES.connecting\n  send: Function = () => {}\n  url: string | URL | null = null\n\n  constructor(\n    address: string,\n    _protocols: undefined,\n    options: { close: Function }\n  ) {\n    this.url = address\n    this.close = options.close\n  }\n}\n", "export class StorageError extends Error {\n  protected __isStorageError = true\n\n  constructor(message: string) {\n    super(message)\n    this.name = 'StorageError'\n  }\n}\n\nexport function isStorageError(error: unknown): error is StorageError {\n  return typeof error === 'object' && error !== null && '__isStorageError' in error\n}\n\nexport class StorageApiError extends StorageError {\n  status: number\n\n  constructor(message: string, status: number) {\n    super(message)\n    this.name = 'StorageApiError'\n    this.status = status\n  }\n\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      status: this.status,\n    }\n  }\n}\n\nexport class StorageUnknownError extends StorageError {\n  originalError: unknown\n\n  constructor(message: string, originalError: unknown) {\n    super(message)\n    this.name = 'StorageUnknownError'\n    this.originalError = originalError\n  }\n}\n", "type Fetch = typeof fetch\n\nexport const resolveFetch = (customFetch?: Fetch): Fetch => {\n  let _fetch: Fetch\n  if (customFetch) {\n    _fetch = customFetch\n  } else if (typeof fetch === 'undefined') {\n    _fetch = (...args) =>\n      import('@supabase/node-fetch' as any).then(({ default: fetch }) => fetch(...args))\n  } else {\n    _fetch = fetch\n  }\n  return (...args) => _fetch(...args)\n}\n\nexport const resolveResponse = async (): Promise<typeof Response> => {\n  if (typeof Response === 'undefined') {\n    // @ts-ignore\n    return (await import('@supabase/node-fetch' as any)).Response\n  }\n\n  return Response\n}\n\nexport const recursiveToCamel = (item: Record<string, any>): unknown => {\n  if (Array.isArray(item)) {\n    return item.map((el) => recursiveToCamel(el))\n  } else if (typeof item === 'function' || item !== Object(item)) {\n    return item\n  }\n\n  const result: Record<string, any> = {}\n  Object.entries(item).forEach(([key, value]) => {\n    const newKey = key.replace(/([-_][a-z])/gi, (c) => c.toUpperCase().replace(/[-_]/g, ''))\n    result[newKey] = recursiveToCamel(value)\n  })\n\n  return result\n}\n", "import { StorageApiError, StorageUnknownError } from './errors'\nimport { resolveResponse } from './helpers'\nimport { FetchParameters } from './types'\n\nexport type Fetch = typeof fetch\n\nexport interface FetchOptions {\n  headers?: {\n    [key: string]: string\n  }\n  noResolveJson?: boolean\n}\n\nexport type RequestMethodType = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'HEAD'\n\nconst _getErrorMessage = (err: any): string =>\n  err.msg || err.message || err.error_description || err.error || JSON.stringify(err)\n\nconst handleError = async (\n  error: unknown,\n  reject: (reason?: any) => void,\n  options?: FetchOptions\n) => {\n  const Res = await resolveResponse()\n\n  if (error instanceof Res && !options?.noResolveJson) {\n    error\n      .json()\n      .then((err) => {\n        reject(new StorageApiError(_getErrorMessage(err), error.status || 500))\n      })\n      .catch((err) => {\n        reject(new StorageUnknownError(_getErrorMessage(err), err))\n      })\n  } else {\n    reject(new StorageUnknownError(_getErrorMessage(error), error))\n  }\n}\n\nconst _getRequestParams = (\n  method: RequestMethodType,\n  options?: FetchOptions,\n  parameters?: FetchParameters,\n  body?: object\n) => {\n  const params: { [k: string]: any } = { method, headers: options?.headers || {} }\n\n  if (method === 'GET') {\n    return params\n  }\n\n  params.headers = { 'Content-Type': 'application/json', ...options?.headers }\n\n  if (body) {\n    params.body = JSON.stringify(body)\n  }\n  return { ...params, ...parameters }\n}\n\nasync function _handleRequest(\n  fetcher: Fetch,\n  method: RequestMethodType,\n  url: string,\n  options?: FetchOptions,\n  parameters?: FetchParameters,\n  body?: object\n): Promise<any> {\n  return new Promise((resolve, reject) => {\n    fetcher(url, _getRequestParams(method, options, parameters, body))\n      .then((result) => {\n        if (!result.ok) throw result\n        if (options?.noResolveJson) return result\n        return result.json()\n      })\n      .then((data) => resolve(data))\n      .catch((error) => handleError(error, reject, options))\n  })\n}\n\nexport async function get(\n  fetcher: Fetch,\n  url: string,\n  options?: FetchOptions,\n  parameters?: FetchParameters\n): Promise<any> {\n  return _handleRequest(fetcher, 'GET', url, options, parameters)\n}\n\nexport async function post(\n  fetcher: Fetch,\n  url: string,\n  body: object,\n  options?: FetchOptions,\n  parameters?: FetchParameters\n): Promise<any> {\n  return _handleRequest(fetcher, 'POST', url, options, parameters, body)\n}\n\nexport async function put(\n  fetcher: Fetch,\n  url: string,\n  body: object,\n  options?: FetchOptions,\n  parameters?: FetchParameters\n): Promise<any> {\n  return _handleRequest(fetcher, 'PUT', url, options, parameters, body)\n}\n\nexport async function head(\n  fetcher: Fetch,\n  url: string,\n  options?: FetchOptions,\n  parameters?: FetchParameters\n): Promise<any> {\n  return _handleRequest(\n    fetcher,\n    'HEAD',\n    url,\n    {\n      ...options,\n      noResolveJson: true,\n    },\n    parameters\n  )\n}\n\nexport async function remove(\n  fetcher: Fetch,\n  url: string,\n  body: object,\n  options?: FetchOptions,\n  parameters?: FetchParameters\n): Promise<any> {\n  return _handleRequest(fetcher, 'DELETE', url, options, parameters, body)\n}\n", "import { isStorageError, StorageError, StorageUnknownError } from '../lib/errors'\nimport { Fetch, get, head, post, remove } from '../lib/fetch'\nimport { recursiveToCamel, resolveFetch } from '../lib/helpers'\nimport {\n  FileObject,\n  FileOptions,\n  SearchOptions,\n  FetchParameters,\n  TransformOptions,\n  DestinationOptions,\n  FileObjectV2,\n  Camelize,\n} from '../lib/types'\n\nconst DEFAULT_SEARCH_OPTIONS = {\n  limit: 100,\n  offset: 0,\n  sortBy: {\n    column: 'name',\n    order: 'asc',\n  },\n}\n\nconst DEFAULT_FILE_OPTIONS: FileOptions = {\n  cacheControl: '3600',\n  contentType: 'text/plain;charset=UTF-8',\n  upsert: false,\n}\n\ntype FileBody =\n  | ArrayBuffer\n  | ArrayBufferView\n  | Blob\n  | Buffer\n  | File\n  | FormData\n  | NodeJS.ReadableStream\n  | ReadableStream<Uint8Array>\n  | URLSearchParams\n  | string\n\nexport default class StorageFileApi {\n  protected url: string\n  protected headers: { [key: string]: string }\n  protected bucketId?: string\n  protected fetch: Fetch\n\n  constructor(\n    url: string,\n    headers: { [key: string]: string } = {},\n    bucketId?: string,\n    fetch?: Fetch\n  ) {\n    this.url = url\n    this.headers = headers\n    this.bucketId = bucketId\n    this.fetch = resolveFetch(fetch)\n  }\n\n  /**\n   * Uploads a file to an existing bucket or replaces an existing file at the specified path with a new one.\n   *\n   * @param method HTTP method.\n   * @param path The relative file path. Should be of the format `folder/subfolder/filename.png`. The bucket must already exist before attempting to upload.\n   * @param fileBody The body of the file to be stored in the bucket.\n   */\n  private async uploadOrUpdate(\n    method: 'POST' | 'PUT',\n    path: string,\n    fileBody: FileBody,\n    fileOptions?: FileOptions\n  ): Promise<\n    | {\n        data: { id: string; path: string; fullPath: string }\n        error: null\n      }\n    | {\n        data: null\n        error: StorageError\n      }\n  > {\n    try {\n      let body\n      const options = { ...DEFAULT_FILE_OPTIONS, ...fileOptions }\n      let headers: Record<string, string> = {\n        ...this.headers,\n        ...(method === 'POST' && { 'x-upsert': String(options.upsert as boolean) }),\n      }\n\n      const metadata = options.metadata\n\n      if (typeof Blob !== 'undefined' && fileBody instanceof Blob) {\n        body = new FormData()\n        body.append('cacheControl', options.cacheControl as string)\n        body.append('', fileBody)\n\n        if (metadata) {\n          body.append('metadata', this.encodeMetadata(metadata))\n        }\n      } else if (typeof FormData !== 'undefined' && fileBody instanceof FormData) {\n        body = fileBody\n        body.append('cacheControl', options.cacheControl as string)\n        if (metadata) {\n          body.append('metadata', this.encodeMetadata(metadata))\n        }\n      } else {\n        body = fileBody\n        headers['cache-control'] = `max-age=${options.cacheControl}`\n        headers['content-type'] = options.contentType as string\n\n        if (metadata) {\n          headers['x-metadata'] = this.toBase64(this.encodeMetadata(metadata))\n        }\n      }\n\n      if (fileOptions?.headers) {\n        headers = { ...headers, ...fileOptions.headers }\n      }\n\n      const cleanPath = this._removeEmptyFolders(path)\n      const _path = this._getFinalPath(cleanPath)\n      const res = await this.fetch(`${this.url}/object/${_path}`, {\n        method,\n        body: body as BodyInit,\n        headers,\n        ...(options?.duplex ? { duplex: options.duplex } : {}),\n      })\n\n      const data = await res.json()\n\n      if (res.ok) {\n        return {\n          data: { path: cleanPath, id: data.Id, fullPath: data.Key },\n          error: null,\n        }\n      } else {\n        const error = data\n        return { data: null, error }\n      }\n    } catch (error) {\n      if (isStorageError(error)) {\n        return { data: null, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Uploads a file to an existing bucket.\n   *\n   * @param path The file path, including the file name. Should be of the format `folder/subfolder/filename.png`. The bucket must already exist before attempting to upload.\n   * @param fileBody The body of the file to be stored in the bucket.\n   */\n  async upload(\n    path: string,\n    fileBody: FileBody,\n    fileOptions?: FileOptions\n  ): Promise<\n    | {\n        data: { id: string; path: string; fullPath: string }\n        error: null\n      }\n    | {\n        data: null\n        error: StorageError\n      }\n  > {\n    return this.uploadOrUpdate('POST', path, fileBody, fileOptions)\n  }\n\n  /**\n   * Upload a file with a token generated from `createSignedUploadUrl`.\n   * @param path The file path, including the file name. Should be of the format `folder/subfolder/filename.png`. The bucket must already exist before attempting to upload.\n   * @param token The token generated from `createSignedUploadUrl`\n   * @param fileBody The body of the file to be stored in the bucket.\n   */\n  async uploadToSignedUrl(\n    path: string,\n    token: string,\n    fileBody: FileBody,\n    fileOptions?: FileOptions\n  ) {\n    const cleanPath = this._removeEmptyFolders(path)\n    const _path = this._getFinalPath(cleanPath)\n\n    const url = new URL(this.url + `/object/upload/sign/${_path}`)\n    url.searchParams.set('token', token)\n\n    try {\n      let body\n      const options = { upsert: DEFAULT_FILE_OPTIONS.upsert, ...fileOptions }\n      const headers: Record<string, string> = {\n        ...this.headers,\n        ...{ 'x-upsert': String(options.upsert as boolean) },\n      }\n\n      if (typeof Blob !== 'undefined' && fileBody instanceof Blob) {\n        body = new FormData()\n        body.append('cacheControl', options.cacheControl as string)\n        body.append('', fileBody)\n      } else if (typeof FormData !== 'undefined' && fileBody instanceof FormData) {\n        body = fileBody\n        body.append('cacheControl', options.cacheControl as string)\n      } else {\n        body = fileBody\n        headers['cache-control'] = `max-age=${options.cacheControl}`\n        headers['content-type'] = options.contentType as string\n      }\n\n      const res = await this.fetch(url.toString(), {\n        method: 'PUT',\n        body: body as BodyInit,\n        headers,\n      })\n\n      const data = await res.json()\n\n      if (res.ok) {\n        return {\n          data: { path: cleanPath, fullPath: data.Key },\n          error: null,\n        }\n      } else {\n        const error = data\n        return { data: null, error }\n      }\n    } catch (error) {\n      if (isStorageError(error)) {\n        return { data: null, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Creates a signed upload URL.\n   * Signed upload URLs can be used to upload files to the bucket without further authentication.\n   * They are valid for 2 hours.\n   * @param path The file path, including the current file name. For example `folder/image.png`.\n   * @param options.upsert If set to true, allows the file to be overwritten if it already exists.\n   */\n  async createSignedUploadUrl(\n    path: string,\n    options?: { upsert: boolean }\n  ): Promise<\n    | {\n        data: { signedUrl: string; token: string; path: string }\n        error: null\n      }\n    | {\n        data: null\n        error: StorageError\n      }\n  > {\n    try {\n      let _path = this._getFinalPath(path)\n\n      const headers = { ...this.headers }\n\n      if (options?.upsert) {\n        headers['x-upsert'] = 'true'\n      }\n\n      const data = await post(\n        this.fetch,\n        `${this.url}/object/upload/sign/${_path}`,\n        {},\n        { headers }\n      )\n\n      const url = new URL(this.url + data.url)\n\n      const token = url.searchParams.get('token')\n\n      if (!token) {\n        throw new StorageError('No token returned by API')\n      }\n\n      return { data: { signedUrl: url.toString(), path, token }, error: null }\n    } catch (error) {\n      if (isStorageError(error)) {\n        return { data: null, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Replaces an existing file at the specified path with a new one.\n   *\n   * @param path The relative file path. Should be of the format `folder/subfolder/filename.png`. The bucket must already exist before attempting to update.\n   * @param fileBody The body of the file to be stored in the bucket.\n   */\n  async update(\n    path: string,\n    fileBody:\n      | ArrayBuffer\n      | ArrayBufferView\n      | Blob\n      | Buffer\n      | File\n      | FormData\n      | NodeJS.ReadableStream\n      | ReadableStream<Uint8Array>\n      | URLSearchParams\n      | string,\n    fileOptions?: FileOptions\n  ): Promise<\n    | {\n        data: { id: string; path: string; fullPath: string }\n        error: null\n      }\n    | {\n        data: null\n        error: StorageError\n      }\n  > {\n    return this.uploadOrUpdate('PUT', path, fileBody, fileOptions)\n  }\n\n  /**\n   * Moves an existing file to a new path in the same bucket.\n   *\n   * @param fromPath The original file path, including the current file name. For example `folder/image.png`.\n   * @param toPath The new file path, including the new file name. For example `folder/image-new.png`.\n   * @param options The destination options.\n   */\n  async move(\n    fromPath: string,\n    toPath: string,\n    options?: DestinationOptions\n  ): Promise<\n    | {\n        data: { message: string }\n        error: null\n      }\n    | {\n        data: null\n        error: StorageError\n      }\n  > {\n    try {\n      const data = await post(\n        this.fetch,\n        `${this.url}/object/move`,\n        {\n          bucketId: this.bucketId,\n          sourceKey: fromPath,\n          destinationKey: toPath,\n          destinationBucket: options?.destinationBucket,\n        },\n        { headers: this.headers }\n      )\n      return { data, error: null }\n    } catch (error) {\n      if (isStorageError(error)) {\n        return { data: null, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Copies an existing file to a new path in the same bucket.\n   *\n   * @param fromPath The original file path, including the current file name. For example `folder/image.png`.\n   * @param toPath The new file path, including the new file name. For example `folder/image-copy.png`.\n   * @param options The destination options.\n   */\n  async copy(\n    fromPath: string,\n    toPath: string,\n    options?: DestinationOptions\n  ): Promise<\n    | {\n        data: { path: string }\n        error: null\n      }\n    | {\n        data: null\n        error: StorageError\n      }\n  > {\n    try {\n      const data = await post(\n        this.fetch,\n        `${this.url}/object/copy`,\n        {\n          bucketId: this.bucketId,\n          sourceKey: fromPath,\n          destinationKey: toPath,\n          destinationBucket: options?.destinationBucket,\n        },\n        { headers: this.headers }\n      )\n      return { data: { path: data.Key }, error: null }\n    } catch (error) {\n      if (isStorageError(error)) {\n        return { data: null, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Creates a signed URL. Use a signed URL to share a file for a fixed amount of time.\n   *\n   * @param path The file path, including the current file name. For example `folder/image.png`.\n   * @param expiresIn The number of seconds until the signed URL expires. For example, `60` for a URL which is valid for one minute.\n   * @param options.download triggers the file as a download if set to true. Set this parameter as the name of the file if you want to trigger the download with a different filename.\n   * @param options.transform Transform the asset before serving it to the client.\n   */\n  async createSignedUrl(\n    path: string,\n    expiresIn: number,\n    options?: { download?: string | boolean; transform?: TransformOptions }\n  ): Promise<\n    | {\n        data: { signedUrl: string }\n        error: null\n      }\n    | {\n        data: null\n        error: StorageError\n      }\n  > {\n    try {\n      let _path = this._getFinalPath(path)\n\n      let data = await post(\n        this.fetch,\n        `${this.url}/object/sign/${_path}`,\n        { expiresIn, ...(options?.transform ? { transform: options.transform } : {}) },\n        { headers: this.headers }\n      )\n      const downloadQueryParam = options?.download\n        ? `&download=${options.download === true ? '' : options.download}`\n        : ''\n      const signedUrl = encodeURI(`${this.url}${data.signedURL}${downloadQueryParam}`)\n      data = { signedUrl }\n      return { data, error: null }\n    } catch (error) {\n      if (isStorageError(error)) {\n        return { data: null, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Creates multiple signed URLs. Use a signed URL to share a file for a fixed amount of time.\n   *\n   * @param paths The file paths to be downloaded, including the current file names. For example `['folder/image.png', 'folder2/image2.png']`.\n   * @param expiresIn The number of seconds until the signed URLs expire. For example, `60` for URLs which are valid for one minute.\n   * @param options.download triggers the file as a download if set to true. Set this parameter as the name of the file if you want to trigger the download with a different filename.\n   */\n  async createSignedUrls(\n    paths: string[],\n    expiresIn: number,\n    options?: { download: string | boolean }\n  ): Promise<\n    | {\n        data: { error: string | null; path: string | null; signedUrl: string }[]\n        error: null\n      }\n    | {\n        data: null\n        error: StorageError\n      }\n  > {\n    try {\n      const data = await post(\n        this.fetch,\n        `${this.url}/object/sign/${this.bucketId}`,\n        { expiresIn, paths },\n        { headers: this.headers }\n      )\n\n      const downloadQueryParam = options?.download\n        ? `&download=${options.download === true ? '' : options.download}`\n        : ''\n      return {\n        data: data.map((datum: { signedURL: string }) => ({\n          ...datum,\n          signedUrl: datum.signedURL\n            ? encodeURI(`${this.url}${datum.signedURL}${downloadQueryParam}`)\n            : null,\n        })),\n        error: null,\n      }\n    } catch (error) {\n      if (isStorageError(error)) {\n        return { data: null, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Downloads a file from a private bucket. For public buckets, make a request to the URL returned from `getPublicUrl` instead.\n   *\n   * @param path The full path and file name of the file to be downloaded. For example `folder/image.png`.\n   * @param options.transform Transform the asset before serving it to the client.\n   */\n  async download(\n    path: string,\n    options?: { transform?: TransformOptions }\n  ): Promise<\n    | {\n        data: Blob\n        error: null\n      }\n    | {\n        data: null\n        error: StorageError\n      }\n  > {\n    const wantsTransformation = typeof options?.transform !== 'undefined'\n    const renderPath = wantsTransformation ? 'render/image/authenticated' : 'object'\n    const transformationQuery = this.transformOptsToQueryString(options?.transform || {})\n    const queryString = transformationQuery ? `?${transformationQuery}` : ''\n\n    try {\n      const _path = this._getFinalPath(path)\n      const res = await get(this.fetch, `${this.url}/${renderPath}/${_path}${queryString}`, {\n        headers: this.headers,\n        noResolveJson: true,\n      })\n      const data = await res.blob()\n      return { data, error: null }\n    } catch (error) {\n      if (isStorageError(error)) {\n        return { data: null, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Retrieves the details of an existing file.\n   * @param path\n   */\n  async info(\n    path: string\n  ): Promise<\n    | {\n        data: Camelize<FileObjectV2>\n        error: null\n      }\n    | {\n        data: null\n        error: StorageError\n      }\n  > {\n    const _path = this._getFinalPath(path)\n\n    try {\n      const data = await get(this.fetch, `${this.url}/object/info/${_path}`, {\n        headers: this.headers,\n      })\n\n      return { data: recursiveToCamel(data) as Camelize<FileObjectV2>, error: null }\n    } catch (error) {\n      if (isStorageError(error)) {\n        return { data: null, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Checks the existence of a file.\n   * @param path\n   */\n  async exists(\n    path: string\n  ): Promise<\n    | {\n        data: boolean\n        error: null\n      }\n    | {\n        data: boolean\n        error: StorageError\n      }\n  > {\n    const _path = this._getFinalPath(path)\n\n    try {\n      await head(this.fetch, `${this.url}/object/${_path}`, {\n        headers: this.headers,\n      })\n\n      return { data: true, error: null }\n    } catch (error) {\n      if (isStorageError(error) && error instanceof StorageUnknownError) {\n        const originalError = (error.originalError as unknown) as { status: number }\n\n        if ([400, 404].includes(originalError?.status)) {\n          return { data: false, error }\n        }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * A simple convenience function to get the URL for an asset in a public bucket. If you do not want to use this function, you can construct the public URL by concatenating the bucket URL with the path to the asset.\n   * This function does not verify if the bucket is public. If a public URL is created for a bucket which is not public, you will not be able to download the asset.\n   *\n   * @param path The path and name of the file to generate the public URL for. For example `folder/image.png`.\n   * @param options.download Triggers the file as a download if set to true. Set this parameter as the name of the file if you want to trigger the download with a different filename.\n   * @param options.transform Transform the asset before serving it to the client.\n   */\n  getPublicUrl(\n    path: string,\n    options?: { download?: string | boolean; transform?: TransformOptions }\n  ): { data: { publicUrl: string } } {\n    const _path = this._getFinalPath(path)\n    const _queryString = []\n\n    const downloadQueryParam = options?.download\n      ? `download=${options.download === true ? '' : options.download}`\n      : ''\n\n    if (downloadQueryParam !== '') {\n      _queryString.push(downloadQueryParam)\n    }\n\n    const wantsTransformation = typeof options?.transform !== 'undefined'\n    const renderPath = wantsTransformation ? 'render/image' : 'object'\n    const transformationQuery = this.transformOptsToQueryString(options?.transform || {})\n\n    if (transformationQuery !== '') {\n      _queryString.push(transformationQuery)\n    }\n\n    let queryString = _queryString.join('&')\n    if (queryString !== '') {\n      queryString = `?${queryString}`\n    }\n\n    return {\n      data: { publicUrl: encodeURI(`${this.url}/${renderPath}/public/${_path}${queryString}`) },\n    }\n  }\n\n  /**\n   * Deletes files within the same bucket\n   *\n   * @param paths An array of files to delete, including the path and file name. For example [`'folder/image.png'`].\n   */\n  async remove(\n    paths: string[]\n  ): Promise<\n    | {\n        data: FileObject[]\n        error: null\n      }\n    | {\n        data: null\n        error: StorageError\n      }\n  > {\n    try {\n      const data = await remove(\n        this.fetch,\n        `${this.url}/object/${this.bucketId}`,\n        { prefixes: paths },\n        { headers: this.headers }\n      )\n      return { data, error: null }\n    } catch (error) {\n      if (isStorageError(error)) {\n        return { data: null, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Get file metadata\n   * @param id the file id to retrieve metadata\n   */\n  // async getMetadata(\n  //   id: string\n  // ): Promise<\n  //   | {\n  //       data: Metadata\n  //       error: null\n  //     }\n  //   | {\n  //       data: null\n  //       error: StorageError\n  //     }\n  // > {\n  //   try {\n  //     const data = await get(this.fetch, `${this.url}/metadata/${id}`, { headers: this.headers })\n  //     return { data, error: null }\n  //   } catch (error) {\n  //     if (isStorageError(error)) {\n  //       return { data: null, error }\n  //     }\n\n  //     throw error\n  //   }\n  // }\n\n  /**\n   * Update file metadata\n   * @param id the file id to update metadata\n   * @param meta the new file metadata\n   */\n  // async updateMetadata(\n  //   id: string,\n  //   meta: Metadata\n  // ): Promise<\n  //   | {\n  //       data: Metadata\n  //       error: null\n  //     }\n  //   | {\n  //       data: null\n  //       error: StorageError\n  //     }\n  // > {\n  //   try {\n  //     const data = await post(\n  //       this.fetch,\n  //       `${this.url}/metadata/${id}`,\n  //       { ...meta },\n  //       { headers: this.headers }\n  //     )\n  //     return { data, error: null }\n  //   } catch (error) {\n  //     if (isStorageError(error)) {\n  //       return { data: null, error }\n  //     }\n\n  //     throw error\n  //   }\n  // }\n\n  /**\n   * Lists all the files within a bucket.\n   * @param path The folder path.\n   */\n  async list(\n    path?: string,\n    options?: SearchOptions,\n    parameters?: FetchParameters\n  ): Promise<\n    | {\n        data: FileObject[]\n        error: null\n      }\n    | {\n        data: null\n        error: StorageError\n      }\n  > {\n    try {\n      const body = { ...DEFAULT_SEARCH_OPTIONS, ...options, prefix: path || '' }\n      const data = await post(\n        this.fetch,\n        `${this.url}/object/list/${this.bucketId}`,\n        body,\n        { headers: this.headers },\n        parameters\n      )\n      return { data, error: null }\n    } catch (error) {\n      if (isStorageError(error)) {\n        return { data: null, error }\n      }\n\n      throw error\n    }\n  }\n\n  protected encodeMetadata(metadata: Record<string, any>) {\n    return JSON.stringify(metadata)\n  }\n\n  toBase64(data: string) {\n    if (typeof Buffer !== 'undefined') {\n      return Buffer.from(data).toString('base64')\n    }\n    return btoa(data)\n  }\n\n  private _getFinalPath(path: string) {\n    return `${this.bucketId}/${path}`\n  }\n\n  private _removeEmptyFolders(path: string) {\n    return path.replace(/^\\/|\\/$/g, '').replace(/\\/+/g, '/')\n  }\n\n  private transformOptsToQueryString(transform: TransformOptions) {\n    const params = []\n    if (transform.width) {\n      params.push(`width=${transform.width}`)\n    }\n\n    if (transform.height) {\n      params.push(`height=${transform.height}`)\n    }\n\n    if (transform.resize) {\n      params.push(`resize=${transform.resize}`)\n    }\n\n    if (transform.format) {\n      params.push(`format=${transform.format}`)\n    }\n\n    if (transform.quality) {\n      params.push(`quality=${transform.quality}`)\n    }\n\n    return params.join('&')\n  }\n}\n", "// generated by genversion\nexport const version = '2.7.0'\n", "import { version } from './version'\nexport const DEFAULT_HEADERS = { 'X-Client-Info': `storage-js/${version}` }\n", "import { DEFAULT_HEADERS } from '../lib/constants'\nimport { isStorageError, StorageError } from '../lib/errors'\nimport { Fetch, get, post, put, remove } from '../lib/fetch'\nimport { resolveFetch } from '../lib/helpers'\nimport { Bucket } from '../lib/types'\n\nexport default class StorageBucketApi {\n  protected url: string\n  protected headers: { [key: string]: string }\n  protected fetch: Fetch\n\n  constructor(url: string, headers: { [key: string]: string } = {}, fetch?: Fetch) {\n    this.url = url\n    this.headers = { ...DEFAULT_HEADERS, ...headers }\n    this.fetch = resolveFetch(fetch)\n  }\n\n  /**\n   * Retrieves the details of all Storage buckets within an existing project.\n   */\n  async listBuckets(): Promise<\n    | {\n        data: Bucket[]\n        error: null\n      }\n    | {\n        data: null\n        error: StorageError\n      }\n  > {\n    try {\n      const data = await get(this.fetch, `${this.url}/bucket`, { headers: this.headers })\n      return { data, error: null }\n    } catch (error) {\n      if (isStorageError(error)) {\n        return { data: null, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Retrieves the details of an existing Storage bucket.\n   *\n   * @param id The unique identifier of the bucket you would like to retrieve.\n   */\n  async getBucket(\n    id: string\n  ): Promise<\n    | {\n        data: Bucket\n        error: null\n      }\n    | {\n        data: null\n        error: StorageError\n      }\n  > {\n    try {\n      const data = await get(this.fetch, `${this.url}/bucket/${id}`, { headers: this.headers })\n      return { data, error: null }\n    } catch (error) {\n      if (isStorageError(error)) {\n        return { data: null, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Creates a new Storage bucket\n   *\n   * @param id A unique identifier for the bucket you are creating.\n   * @param options.public The visibility of the bucket. Public buckets don't require an authorization token to download objects, but still require a valid token for all other operations. By default, buckets are private.\n   * @param options.fileSizeLimit specifies the max file size in bytes that can be uploaded to this bucket.\n   * The global file size limit takes precedence over this value.\n   * The default value is null, which doesn't set a per bucket file size limit.\n   * @param options.allowedMimeTypes specifies the allowed mime types that this bucket can accept during upload.\n   * The default value is null, which allows files with all mime types to be uploaded.\n   * Each mime type specified can be a wildcard, e.g. image/*, or a specific mime type, e.g. image/png.\n   * @returns newly created bucket id\n   */\n  async createBucket(\n    id: string,\n    options: {\n      public: boolean\n      fileSizeLimit?: number | string | null\n      allowedMimeTypes?: string[] | null\n    } = {\n      public: false,\n    }\n  ): Promise<\n    | {\n        data: Pick<Bucket, 'name'>\n        error: null\n      }\n    | {\n        data: null\n        error: StorageError\n      }\n  > {\n    try {\n      const data = await post(\n        this.fetch,\n        `${this.url}/bucket`,\n        {\n          id,\n          name: id,\n          public: options.public,\n          file_size_limit: options.fileSizeLimit,\n          allowed_mime_types: options.allowedMimeTypes,\n        },\n        { headers: this.headers }\n      )\n      return { data, error: null }\n    } catch (error) {\n      if (isStorageError(error)) {\n        return { data: null, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Updates a Storage bucket\n   *\n   * @param id A unique identifier for the bucket you are updating.\n   * @param options.public The visibility of the bucket. Public buckets don't require an authorization token to download objects, but still require a valid token for all other operations.\n   * @param options.fileSizeLimit specifies the max file size in bytes that can be uploaded to this bucket.\n   * The global file size limit takes precedence over this value.\n   * The default value is null, which doesn't set a per bucket file size limit.\n   * @param options.allowedMimeTypes specifies the allowed mime types that this bucket can accept during upload.\n   * The default value is null, which allows files with all mime types to be uploaded.\n   * Each mime type specified can be a wildcard, e.g. image/*, or a specific mime type, e.g. image/png.\n   */\n  async updateBucket(\n    id: string,\n    options: {\n      public: boolean\n      fileSizeLimit?: number | string | null\n      allowedMimeTypes?: string[] | null\n    }\n  ): Promise<\n    | {\n        data: { message: string }\n        error: null\n      }\n    | {\n        data: null\n        error: StorageError\n      }\n  > {\n    try {\n      const data = await put(\n        this.fetch,\n        `${this.url}/bucket/${id}`,\n        {\n          id,\n          name: id,\n          public: options.public,\n          file_size_limit: options.fileSizeLimit,\n          allowed_mime_types: options.allowedMimeTypes,\n        },\n        { headers: this.headers }\n      )\n      return { data, error: null }\n    } catch (error) {\n      if (isStorageError(error)) {\n        return { data: null, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Removes all objects inside a single bucket.\n   *\n   * @param id The unique identifier of the bucket you would like to empty.\n   */\n  async emptyBucket(\n    id: string\n  ): Promise<\n    | {\n        data: { message: string }\n        error: null\n      }\n    | {\n        data: null\n        error: StorageError\n      }\n  > {\n    try {\n      const data = await post(\n        this.fetch,\n        `${this.url}/bucket/${id}/empty`,\n        {},\n        { headers: this.headers }\n      )\n      return { data, error: null }\n    } catch (error) {\n      if (isStorageError(error)) {\n        return { data: null, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Deletes an existing bucket. A bucket can't be deleted with existing objects inside it.\n   * You must first `empty()` the bucket.\n   *\n   * @param id The unique identifier of the bucket you would like to delete.\n   */\n  async deleteBucket(\n    id: string\n  ): Promise<\n    | {\n        data: { message: string }\n        error: null\n      }\n    | {\n        data: null\n        error: StorageError\n      }\n  > {\n    try {\n      const data = await remove(\n        this.fetch,\n        `${this.url}/bucket/${id}`,\n        {},\n        { headers: this.headers }\n      )\n      return { data, error: null }\n    } catch (error) {\n      if (isStorageError(error)) {\n        return { data: null, error }\n      }\n\n      throw error\n    }\n  }\n}\n", "import StorageFileApi from './packages/StorageFileApi'\nimport StorageBucketApi from './packages/StorageBucketApi'\nimport { Fetch } from './lib/fetch'\n\nexport class StorageClient extends StorageBucketApi {\n  constructor(url: string, headers: { [key: string]: string } = {}, fetch?: Fetch) {\n    super(url, headers, fetch)\n  }\n\n  /**\n   * Perform file operation in a bucket.\n   *\n   * @param id The bucket id to operate on.\n   */\n  from(id: string): StorageFileApi {\n    return new StorageFileApi(this.url, this.headers, id, this.fetch)\n  }\n}\n", "export const version = '2.45.4'\n", "// constants.ts\nimport { RealtimeClientOptions } from '@supabase/realtime-js'\nimport { SupabaseAuthClientOptions } from './types'\nimport { version } from './version'\n\nlet JS_ENV = ''\n// @ts-ignore\nif (typeof Deno !== 'undefined') {\n  JS_ENV = 'deno'\n} else if (typeof document !== 'undefined') {\n  JS_ENV = 'web'\n} else if (typeof navigator !== 'undefined' && navigator.product === 'ReactNative') {\n  JS_ENV = 'react-native'\n} else {\n  JS_ENV = 'node'\n}\n\nexport const DEFAULT_HEADERS = { 'X-Client-Info': `supabase-js-${JS_ENV}/${version}` }\n\nexport const DEFAULT_GLOBAL_OPTIONS = {\n  headers: DEFAULT_HEADERS,\n}\n\nexport const DEFAULT_DB_OPTIONS = {\n  schema: 'public',\n}\n\nexport const DEFAULT_AUTH_OPTIONS: SupabaseAuthClientOptions = {\n  autoRefreshToken: true,\n  persistSession: true,\n  detectSessionInUrl: true,\n  flowType: 'implicit',\n}\n\nexport const DEFAULT_REALTIME_OPTIONS: RealtimeClientOptions = {}\n", "// @ts-ignore\nimport nodeFetch, { Headers as NodeFetchHeaders } from '@supabase/node-fetch'\n\ntype Fetch = typeof fetch\n\nexport const resolveFetch = (customFetch?: Fetch): Fetch => {\n  let _fetch: Fetch\n  if (customFetch) {\n    _fetch = customFetch\n  } else if (typeof fetch === 'undefined') {\n    _fetch = nodeFetch as unknown as Fetch\n  } else {\n    _fetch = fetch\n  }\n  return (...args: Parameters<Fetch>) => _fetch(...args)\n}\n\nexport const resolveHeadersConstructor = () => {\n  if (typeof Headers === 'undefined') {\n    return NodeFetchHeaders\n  }\n\n  return Headers\n}\n\nexport const fetchWithAuth = (\n  supabaseKey: string,\n  getAccessToken: () => Promise<string | null>,\n  customFetch?: Fetch\n): Fetch => {\n  const fetch = resolveFetch(customFetch)\n  const HeadersConstructor = resolveHeadersConstructor()\n\n  return async (input, init) => {\n    const accessToken = (await getAccessToken()) ?? supabaseKey\n    let headers = new HeadersConstructor(init?.headers)\n\n    if (!headers.has('apikey')) {\n      headers.set('apikey', supabaseKey)\n    }\n\n    if (!headers.has('Authorization')) {\n      headers.set('Authorization', `Bearer ${accessToken}`)\n    }\n\n    return fetch(input, { ...init, headers })\n  }\n}\n", "// helpers.ts\nimport { SupabaseClientOptions } from './types'\n\nexport function uuid() {\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n    var r = (Math.random() * 16) | 0,\n      v = c == 'x' ? r : (r & 0x3) | 0x8\n    return v.toString(16)\n  })\n}\n\nexport function stripTrailingSlash(url: string): string {\n  return url.replace(/\\/$/, '')\n}\n\nexport const isBrowser = () => typeof window !== 'undefined'\n\nexport function applySettingDefaults<\n  Database = any,\n  SchemaName extends string & keyof Database = 'public' extends keyof Database\n    ? 'public'\n    : string & keyof Database\n>(\n  options: SupabaseClientOptions<SchemaName>,\n  defaults: SupabaseClientOptions<any>\n): Required<SupabaseClientOptions<SchemaName>> {\n  const {\n    db: dbOptions,\n    auth: authOptions,\n    realtime: realtimeOptions,\n    global: globalOptions,\n  } = options\n  const {\n    db: DEFAULT_DB_OPTIONS,\n    auth: DEFAULT_AUTH_OPTIONS,\n    realtime: DEFAULT_REALTIME_OPTIONS,\n    global: DEFAULT_GLOBAL_OPTIONS,\n  } = defaults\n\n  const result: Required<SupabaseClientOptions<SchemaName>> = {\n    db: {\n      ...DEFAULT_DB_OPTIONS,\n      ...dbOptions,\n    },\n    auth: {\n      ...DEFAULT_AUTH_OPTIONS,\n      ...authOptions,\n    },\n    realtime: {\n      ...DEFAULT_REALTIME_OPTIONS,\n      ...realtimeOptions,\n    },\n    global: {\n      ...DEFAULT_GLOBAL_OPTIONS,\n      ...globalOptions,\n    },\n    accessToken: async () => '',\n  }\n\n  if (options.accessToken) {\n    result.accessToken = options.accessToken\n  } else {\n    // hack around Required<>\n    delete (result as any).accessToken\n  }\n\n  return result\n}\n", "export const version = '2.65.0'\n", "import { version } from './version'\nexport const GOTRUE_URL = 'http://localhost:9999'\nexport const STORAGE_KEY = 'supabase.auth.token'\nexport const AUDIENCE = ''\nexport const DEFAULT_HEADERS = { 'X-Client-Info': `gotrue-js/${version}` }\nexport const EXPIRY_MARGIN = 10 // in seconds\nexport const NETWORK_FAILURE = {\n  MAX_RETRIES: 10,\n  RETRY_INTERVAL: 2, // in deciseconds\n}\n\nexport const API_VERSION_HEADER_NAME = 'X-Supabase-Api-Version'\nexport const API_VERSIONS = {\n  '2024-01-01': {\n    timestamp: Date.parse('2024-01-01T00:00:00.0Z'),\n    name: '2024-01-01',\n  },\n}\n", "import { API_VERSION_HEADER_NAME } from './constants'\nimport { SupportedStorage } from './types'\n\nexport function expiresAt(expiresIn: number) {\n  const timeNow = Math.round(Date.now() / 1000)\n  return timeNow + expiresIn\n}\n\nexport function uuid() {\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n    const r = (Math.random() * 16) | 0,\n      v = c == 'x' ? r : (r & 0x3) | 0x8\n    return v.toString(16)\n  })\n}\n\nexport const isBrowser = () => typeof document !== 'undefined'\n\nconst localStorageWriteTests = {\n  tested: false,\n  writable: false,\n}\n\n/**\n * Checks whether localStorage is supported on this browser.\n */\nexport const supportsLocalStorage = () => {\n  if (!isBrowser()) {\n    return false\n  }\n\n  try {\n    if (typeof globalThis.localStorage !== 'object') {\n      return false\n    }\n  } catch (e) {\n    // DOM exception when accessing `localStorage`\n    return false\n  }\n\n  if (localStorageWriteTests.tested) {\n    return localStorageWriteTests.writable\n  }\n\n  const randomKey = `lswt-${Math.random()}${Math.random()}`\n\n  try {\n    globalThis.localStorage.setItem(randomKey, randomKey)\n    globalThis.localStorage.removeItem(randomKey)\n\n    localStorageWriteTests.tested = true\n    localStorageWriteTests.writable = true\n  } catch (e) {\n    // localStorage can't be written to\n    // https://www.chromium.org/for-testers/bug-reporting-guidelines/uncaught-securityerror-failed-to-read-the-localstorage-property-from-window-access-is-denied-for-this-document\n\n    localStorageWriteTests.tested = true\n    localStorageWriteTests.writable = false\n  }\n\n  return localStorageWriteTests.writable\n}\n\n/**\n * Extracts parameters encoded in the URL both in the query and fragment.\n */\nexport function parseParametersFromURL(href: string) {\n  const result: { [parameter: string]: string } = {}\n\n  const url = new URL(href)\n\n  if (url.hash && url.hash[0] === '#') {\n    try {\n      const hashSearchParams = new URLSearchParams(url.hash.substring(1))\n      hashSearchParams.forEach((value, key) => {\n        result[key] = value\n      })\n    } catch (e: any) {\n      // hash is not a query string\n    }\n  }\n\n  // search parameters take precedence over hash parameters\n  url.searchParams.forEach((value, key) => {\n    result[key] = value\n  })\n\n  return result\n}\n\ntype Fetch = typeof fetch\n\nexport const resolveFetch = (customFetch?: Fetch): Fetch => {\n  let _fetch: Fetch\n  if (customFetch) {\n    _fetch = customFetch\n  } else if (typeof fetch === 'undefined') {\n    _fetch = (...args) =>\n      import('@supabase/node-fetch' as any).then(({ default: fetch }) => fetch(...args))\n  } else {\n    _fetch = fetch\n  }\n  return (...args) => _fetch(...args)\n}\n\nexport const looksLikeFetchResponse = (maybeResponse: unknown): maybeResponse is Response => {\n  return (\n    typeof maybeResponse === 'object' &&\n    maybeResponse !== null &&\n    'status' in maybeResponse &&\n    'ok' in maybeResponse &&\n    'json' in maybeResponse &&\n    typeof (maybeResponse as any).json === 'function'\n  )\n}\n\n// Storage helpers\nexport const setItemAsync = async (\n  storage: SupportedStorage,\n  key: string,\n  data: any\n): Promise<void> => {\n  await storage.setItem(key, JSON.stringify(data))\n}\n\nexport const getItemAsync = async (storage: SupportedStorage, key: string): Promise<unknown> => {\n  const value = await storage.getItem(key)\n\n  if (!value) {\n    return null\n  }\n\n  try {\n    return JSON.parse(value)\n  } catch {\n    return value\n  }\n}\n\nexport const removeItemAsync = async (storage: SupportedStorage, key: string): Promise<void> => {\n  await storage.removeItem(key)\n}\n\nexport function decodeBase64URL(value: string): string {\n  const key = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/='\n  let base64 = ''\n  let chr1, chr2, chr3\n  let enc1, enc2, enc3, enc4\n  let i = 0\n  value = value.replace('-', '+').replace('_', '/')\n\n  while (i < value.length) {\n    enc1 = key.indexOf(value.charAt(i++))\n    enc2 = key.indexOf(value.charAt(i++))\n    enc3 = key.indexOf(value.charAt(i++))\n    enc4 = key.indexOf(value.charAt(i++))\n    chr1 = (enc1 << 2) | (enc2 >> 4)\n    chr2 = ((enc2 & 15) << 4) | (enc3 >> 2)\n    chr3 = ((enc3 & 3) << 6) | enc4\n    base64 = base64 + String.fromCharCode(chr1)\n\n    if (enc3 != 64 && chr2 != 0) {\n      base64 = base64 + String.fromCharCode(chr2)\n    }\n    if (enc4 != 64 && chr3 != 0) {\n      base64 = base64 + String.fromCharCode(chr3)\n    }\n  }\n  return base64\n}\n\n/**\n * A deferred represents some asynchronous work that is not yet finished, which\n * may or may not culminate in a value.\n * Taken from: https://github.com/mike-north/types/blob/master/src/async.ts\n */\nexport class Deferred<T = any> {\n  public static promiseConstructor: PromiseConstructor = Promise\n\n  public readonly promise!: PromiseLike<T>\n\n  public readonly resolve!: (value?: T | PromiseLike<T>) => void\n\n  public readonly reject!: (reason?: any) => any\n\n  public constructor() {\n    // eslint-disable-next-line @typescript-eslint/no-extra-semi\n    ;(this as any).promise = new Deferred.promiseConstructor((res, rej) => {\n      // eslint-disable-next-line @typescript-eslint/no-extra-semi\n      ;(this as any).resolve = res\n      // eslint-disable-next-line @typescript-eslint/no-extra-semi\n      ;(this as any).reject = rej\n    })\n  }\n}\n\n// Taken from: https://stackoverflow.com/questions/38552003/how-to-decode-jwt-token-in-javascript-without-using-a-library\nexport function decodeJWTPayload(token: string) {\n  // Regex checks for base64url format\n  const base64UrlRegex = /^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}=?$|[a-z0-9_-]{2}(==)?$)$/i\n\n  const parts = token.split('.')\n\n  if (parts.length !== 3) {\n    throw new Error('JWT is not valid: not a JWT structure')\n  }\n\n  if (!base64UrlRegex.test(parts[1])) {\n    throw new Error('JWT is not valid: payload is not in base64url format')\n  }\n\n  const base64Url = parts[1]\n  return JSON.parse(decodeBase64URL(base64Url))\n}\n\n/**\n * Creates a promise that resolves to null after some time.\n */\nexport async function sleep(time: number): Promise<null> {\n  return await new Promise((accept) => {\n    setTimeout(() => accept(null), time)\n  })\n}\n\n/**\n * Converts the provided async function into a retryable function. Each result\n * or thrown error is sent to the isRetryable function which should return true\n * if the function should run again.\n */\nexport function retryable<T>(\n  fn: (attempt: number) => Promise<T>,\n  isRetryable: (attempt: number, error: any | null, result?: T) => boolean\n): Promise<T> {\n  const promise = new Promise<T>((accept, reject) => {\n    // eslint-disable-next-line @typescript-eslint/no-extra-semi\n    ;(async () => {\n      for (let attempt = 0; attempt < Infinity; attempt++) {\n        try {\n          const result = await fn(attempt)\n\n          if (!isRetryable(attempt, null, result)) {\n            accept(result)\n            return\n          }\n        } catch (e: any) {\n          if (!isRetryable(attempt, e)) {\n            reject(e)\n            return\n          }\n        }\n      }\n    })()\n  })\n\n  return promise\n}\n\nfunction dec2hex(dec: number) {\n  return ('0' + dec.toString(16)).substr(-2)\n}\n\n// Functions below taken from: https://stackoverflow.com/questions/63309409/creating-a-code-verifier-and-challenge-for-pkce-auth-on-spotify-api-in-reactjs\nexport function generatePKCEVerifier() {\n  const verifierLength = 56\n  const array = new Uint32Array(verifierLength)\n  if (typeof crypto === 'undefined') {\n    const charSet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~'\n    const charSetLen = charSet.length\n    let verifier = ''\n    for (let i = 0; i < verifierLength; i++) {\n      verifier += charSet.charAt(Math.floor(Math.random() * charSetLen))\n    }\n    return verifier\n  }\n  crypto.getRandomValues(array)\n  return Array.from(array, dec2hex).join('')\n}\n\nasync function sha256(randomString: string) {\n  const encoder = new TextEncoder()\n  const encodedData = encoder.encode(randomString)\n  const hash = await crypto.subtle.digest('SHA-256', encodedData)\n  const bytes = new Uint8Array(hash)\n\n  return Array.from(bytes)\n    .map((c) => String.fromCharCode(c))\n    .join('')\n}\n\nfunction base64urlencode(str: string) {\n  return btoa(str).replace(/\\+/g, '-').replace(/\\//g, '_').replace(/=+$/, '')\n}\n\nexport async function generatePKCEChallenge(verifier: string) {\n  const hasCryptoSupport =\n    typeof crypto !== 'undefined' &&\n    typeof crypto.subtle !== 'undefined' &&\n    typeof TextEncoder !== 'undefined'\n\n  if (!hasCryptoSupport) {\n    console.warn(\n      'WebCrypto API is not supported. Code challenge method will default to use plain instead of sha256.'\n    )\n    return verifier\n  }\n  const hashed = await sha256(verifier)\n  return base64urlencode(hashed)\n}\n\nexport async function getCodeChallengeAndMethod(\n  storage: SupportedStorage,\n  storageKey: string,\n  isPasswordRecovery = false\n) {\n  const codeVerifier = generatePKCEVerifier()\n  let storedCodeVerifier = codeVerifier\n  if (isPasswordRecovery) {\n    storedCodeVerifier += '/PASSWORD_RECOVERY'\n  }\n  await setItemAsync(storage, `${storageKey}-code-verifier`, storedCodeVerifier)\n  const codeChallenge = await generatePKCEChallenge(codeVerifier)\n  const codeChallengeMethod = codeVerifier === codeChallenge ? 'plain' : 's256'\n  return [codeChallenge, codeChallengeMethod]\n}\n\n/** Parses the API version which is 2YYY-MM-DD. */\nconst API_VERSION_REGEX = /^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i\n\nexport function parseResponseAPIVersion(response: Response) {\n  const apiVersion = response.headers.get(API_VERSION_HEADER_NAME)\n\n  if (!apiVersion) {\n    return null\n  }\n\n  if (!apiVersion.match(API_VERSION_REGEX)) {\n    return null\n  }\n\n  try {\n    const date = new Date(`${apiVersion}T00:00:00.0Z`)\n    return date\n  } catch (e: any) {\n    return null\n  }\n}\n", "import { WeakPasswordReasons } from './types'\nimport { ErrorCode } from './error-codes'\n\nexport class AuthError extends Error {\n  /**\n   * Error code associated with the error. Most errors coming from\n   * HTTP responses will have a code, though some errors that occur\n   * before a response is received will not have one present. In that\n   * case {@link #status} will also be undefined.\n   */\n  code: ErrorCode | string | undefined\n\n  /** HTTP status code that caused the error. */\n  status: number | undefined\n\n  protected __isAuthError = true\n\n  constructor(message: string, status?: number, code?: string) {\n    super(message)\n    this.name = 'AuthError'\n    this.status = status\n    this.code = code\n  }\n}\n\nexport function isAuthError(error: unknown): error is AuthError {\n  return typeof error === 'object' && error !== null && '__isAuthError' in error\n}\n\nexport class AuthApiError extends AuthError {\n  status: number\n\n  constructor(message: string, status: number, code: string | undefined) {\n    super(message, status, code)\n    this.name = 'AuthApiError'\n    this.status = status\n    this.code = code\n  }\n}\n\nexport function isAuthApiError(error: unknown): error is AuthApiError {\n  return isAuthError(error) && error.name === 'AuthApiError'\n}\n\nexport class AuthUnknownError extends AuthError {\n  originalError: unknown\n\n  constructor(message: string, originalError: unknown) {\n    super(message)\n    this.name = 'AuthUnknownError'\n    this.originalError = originalError\n  }\n}\n\nexport class CustomAuthError extends AuthError {\n  name: string\n  status: number\n\n  constructor(message: string, name: string, status: number, code: string | undefined) {\n    super(message, status, code)\n    this.name = name\n    this.status = status\n  }\n}\n\nexport class AuthSessionMissingError extends CustomAuthError {\n  constructor() {\n    super('Auth session missing!', 'AuthSessionMissingError', 400, undefined)\n  }\n}\n\nexport function isAuthSessionMissingError(error: any): error is AuthSessionMissingError {\n  return isAuthError(error) && error.name === 'AuthSessionMissingError'\n}\n\nexport class AuthInvalidTokenResponseError extends CustomAuthError {\n  constructor() {\n    super('Auth session or user missing', 'AuthInvalidTokenResponseError', 500, undefined)\n  }\n}\n\nexport class AuthInvalidCredentialsError extends CustomAuthError {\n  constructor(message: string) {\n    super(message, 'AuthInvalidCredentialsError', 400, undefined)\n  }\n}\n\nexport class AuthImplicitGrantRedirectError extends CustomAuthError {\n  details: { error: string; code: string } | null = null\n  constructor(message: string, details: { error: string; code: string } | null = null) {\n    super(message, 'AuthImplicitGrantRedirectError', 500, undefined)\n    this.details = details\n  }\n\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      status: this.status,\n      details: this.details,\n    }\n  }\n}\n\nexport class AuthPKCEGrantCodeExchangeError extends CustomAuthError {\n  details: { error: string; code: string } | null = null\n\n  constructor(message: string, details: { error: string; code: string } | null = null) {\n    super(message, 'AuthPKCEGrantCodeExchangeError', 500, undefined)\n    this.details = details\n  }\n\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      status: this.status,\n      details: this.details,\n    }\n  }\n}\n\nexport class AuthRetryableFetchError extends CustomAuthError {\n  constructor(message: string, status: number) {\n    super(message, 'AuthRetryableFetchError', status, undefined)\n  }\n}\n\nexport function isAuthRetryableFetchError(error: unknown): error is AuthRetryableFetchError {\n  return isAuthError(error) && error.name === 'AuthRetryableFetchError'\n}\n\n/**\n * This error is thrown on certain methods when the password used is deemed\n * weak. Inspect the reasons to identify what password strength rules are\n * inadequate.\n */\nexport class AuthWeakPasswordError extends CustomAuthError {\n  /**\n   * Reasons why the password is deemed weak.\n   */\n  reasons: WeakPasswordReasons[]\n\n  constructor(message: string, status: number, reasons: string[]) {\n    super(message, 'AuthWeakPasswordError', status, 'weak_password')\n\n    this.reasons = reasons\n  }\n}\n\nexport function isAuthWeakPasswordError(error: unknown): error is AuthWeakPasswordError {\n  return isAuthError(error) && error.name === 'AuthWeakPasswordError'\n}\n", "import { API_VERSIONS, API_VERSION_HEADER_NAME } from './constants'\nimport { expiresAt, looksLikeFetchResponse, parseResponseAPIVersion } from './helpers'\nimport {\n  AuthResponse,\n  AuthResponsePassword,\n  SSOResponse,\n  GenerateLinkProperties,\n  GenerateLinkResponse,\n  User,\n  UserResponse,\n} from './types'\nimport {\n  AuthApiError,\n  AuthRetryableFetchError,\n  AuthWeakPasswordError,\n  AuthUnknownError,\n  AuthSessionMissingError,\n} from './errors'\n\nexport type Fetch = typeof fetch\n\nexport interface FetchOptions {\n  headers?: {\n    [key: string]: string\n  }\n  noResolveJson?: boolean\n}\n\nexport interface FetchParameters {\n  signal?: AbortSignal\n}\n\nexport type RequestMethodType = 'GET' | 'POST' | 'PUT' | 'DELETE'\n\nconst _getErrorMessage = (err: any): string =>\n  err.msg || err.message || err.error_description || err.error || JSON.stringify(err)\n\nconst NETWORK_ERROR_CODES = [502, 503, 504]\n\nexport async function handleError(error: unknown) {\n  if (!looksLikeFetchResponse(error)) {\n    throw new AuthRetryableFetchError(_getErrorMessage(error), 0)\n  }\n\n  if (NETWORK_ERROR_CODES.includes(error.status)) {\n    // status in 500...599 range - server had an error, request might be retryed.\n    throw new AuthRetryableFetchError(_getErrorMessage(error), error.status)\n  }\n\n  let data: any\n  try {\n    data = await error.json()\n  } catch (e: any) {\n    throw new AuthUnknownError(_getErrorMessage(e), e)\n  }\n\n  let errorCode: string | undefined = undefined\n\n  const responseAPIVersion = parseResponseAPIVersion(error)\n  if (\n    responseAPIVersion &&\n    responseAPIVersion.getTime() >= API_VERSIONS['2024-01-01'].timestamp &&\n    typeof data === 'object' &&\n    data &&\n    typeof data.code === 'string'\n  ) {\n    errorCode = data.code\n  } else if (typeof data === 'object' && data && typeof data.error_code === 'string') {\n    errorCode = data.error_code\n  }\n\n  if (!errorCode) {\n    // Legacy support for weak password errors, when there were no error codes\n    if (\n      typeof data === 'object' &&\n      data &&\n      typeof data.weak_password === 'object' &&\n      data.weak_password &&\n      Array.isArray(data.weak_password.reasons) &&\n      data.weak_password.reasons.length &&\n      data.weak_password.reasons.reduce((a: boolean, i: any) => a && typeof i === 'string', true)\n    ) {\n      throw new AuthWeakPasswordError(\n        _getErrorMessage(data),\n        error.status,\n        data.weak_password.reasons\n      )\n    }\n  } else if (errorCode === 'weak_password') {\n    throw new AuthWeakPasswordError(\n      _getErrorMessage(data),\n      error.status,\n      data.weak_password?.reasons || []\n    )\n  } else if (errorCode === 'session_not_found') {\n    // The `session_id` inside the JWT does not correspond to a row in the\n    // `sessions` table. This usually means the user has signed out, has been\n    // deleted, or their session has somehow been terminated.\n    throw new AuthSessionMissingError()\n  }\n\n  throw new AuthApiError(_getErrorMessage(data), error.status || 500, errorCode)\n}\n\nconst _getRequestParams = (\n  method: RequestMethodType,\n  options?: FetchOptions,\n  parameters?: FetchParameters,\n  body?: object\n) => {\n  const params: { [k: string]: any } = { method, headers: options?.headers || {} }\n\n  if (method === 'GET') {\n    return params\n  }\n\n  params.headers = { 'Content-Type': 'application/json;charset=UTF-8', ...options?.headers }\n  params.body = JSON.stringify(body)\n  return { ...params, ...parameters }\n}\n\ninterface GotrueRequestOptions extends FetchOptions {\n  jwt?: string\n  redirectTo?: string\n  body?: object\n  query?: { [key: string]: string }\n  /**\n   * Function that transforms api response from gotrue into a desirable / standardised format\n   */\n  xform?: (data: any) => any\n}\n\nexport async function _request(\n  fetcher: Fetch,\n  method: RequestMethodType,\n  url: string,\n  options?: GotrueRequestOptions\n) {\n  const headers = {\n    ...options?.headers,\n  }\n\n  if (!headers[API_VERSION_HEADER_NAME]) {\n    headers[API_VERSION_HEADER_NAME] = API_VERSIONS['2024-01-01'].name\n  }\n\n  if (options?.jwt) {\n    headers['Authorization'] = `Bearer ${options.jwt}`\n  }\n\n  const qs = options?.query ?? {}\n  if (options?.redirectTo) {\n    qs['redirect_to'] = options.redirectTo\n  }\n\n  const queryString = Object.keys(qs).length ? '?' + new URLSearchParams(qs).toString() : ''\n  const data = await _handleRequest(\n    fetcher,\n    method,\n    url + queryString,\n    {\n      headers,\n      noResolveJson: options?.noResolveJson,\n    },\n    {},\n    options?.body\n  )\n  return options?.xform ? options?.xform(data) : { data: { ...data }, error: null }\n}\n\nasync function _handleRequest(\n  fetcher: Fetch,\n  method: RequestMethodType,\n  url: string,\n  options?: FetchOptions,\n  parameters?: FetchParameters,\n  body?: object\n): Promise<any> {\n  const requestParams = _getRequestParams(method, options, parameters, body)\n\n  let result: any\n\n  try {\n    result = await fetcher(url, {\n      ...requestParams,\n    })\n  } catch (e) {\n    console.error(e)\n\n    // fetch failed, likely due to a network or CORS error\n    throw new AuthRetryableFetchError(_getErrorMessage(e), 0)\n  }\n\n  if (!result.ok) {\n    await handleError(result)\n  }\n\n  if (options?.noResolveJson) {\n    return result\n  }\n\n  try {\n    return await result.json()\n  } catch (e: any) {\n    await handleError(e)\n  }\n}\n\nexport function _sessionResponse(data: any): AuthResponse {\n  let session = null\n  if (hasSession(data)) {\n    session = { ...data }\n\n    if (!data.expires_at) {\n      session.expires_at = expiresAt(data.expires_in)\n    }\n  }\n\n  const user: User = data.user ?? (data as User)\n  return { data: { session, user }, error: null }\n}\n\nexport function _sessionResponsePassword(data: any): AuthResponsePassword {\n  const response = _sessionResponse(data) as AuthResponsePassword\n\n  if (\n    !response.error &&\n    data.weak_password &&\n    typeof data.weak_password === 'object' &&\n    Array.isArray(data.weak_password.reasons) &&\n    data.weak_password.reasons.length &&\n    data.weak_password.message &&\n    typeof data.weak_password.message === 'string' &&\n    data.weak_password.reasons.reduce((a: boolean, i: any) => a && typeof i === 'string', true)\n  ) {\n    response.data.weak_password = data.weak_password\n  }\n\n  return response\n}\n\nexport function _userResponse(data: any): UserResponse {\n  const user: User = data.user ?? (data as User)\n  return { data: { user }, error: null }\n}\n\nexport function _ssoResponse(data: any): SSOResponse {\n  return { data, error: null }\n}\n\nexport function _generateLinkResponse(data: any): GenerateLinkResponse {\n  const { action_link, email_otp, hashed_token, redirect_to, verification_type, ...rest } = data\n\n  const properties: GenerateLinkProperties = {\n    action_link,\n    email_otp,\n    hashed_token,\n    redirect_to,\n    verification_type,\n  }\n\n  const user: User = { ...rest }\n  return {\n    data: {\n      properties,\n      user,\n    },\n    error: null,\n  }\n}\n\nexport function _noResolveJsonResponse(data: any): Response {\n  return data\n}\n\n/**\n * hasSession checks if the response object contains a valid session\n * @param data A response object\n * @returns true if a session is in the response\n */\nfunction hasSession(data: any): boolean {\n  return data.access_token && data.refresh_token && data.expires_in\n}\n", "import {\n  Fetch,\n  _generateLinkResponse,\n  _noResolveJsonResponse,\n  _request,\n  _userResponse,\n} from './lib/fetch'\nimport { resolveFetch } from './lib/helpers'\nimport {\n  AdminUserAttributes,\n  GenerateLinkParams,\n  GenerateLinkResponse,\n  Pagination,\n  User,\n  UserResponse,\n  GoTrueAdminMFAApi,\n  AuthMFAAdminDeleteFactorParams,\n  AuthMFAAdminDeleteFactorResponse,\n  AuthMFAAdminListFactorsParams,\n  AuthMFAAdminListFactorsResponse,\n  PageParams,\n} from './lib/types'\nimport { AuthError, isAuthError } from './lib/errors'\n\nexport default class GoTrueAdminApi {\n  /** Contains all MFA administration methods. */\n  mfa: GoTrueAdminMFAApi\n\n  protected url: string\n  protected headers: {\n    [key: string]: string\n  }\n  protected fetch: Fetch\n\n  constructor({\n    url = '',\n    headers = {},\n    fetch,\n  }: {\n    url: string\n    headers?: {\n      [key: string]: string\n    }\n    fetch?: Fetch\n  }) {\n    this.url = url\n    this.headers = headers\n    this.fetch = resolveFetch(fetch)\n    this.mfa = {\n      listFactors: this._listFactors.bind(this),\n      deleteFactor: this._deleteFactor.bind(this),\n    }\n  }\n\n  /**\n   * Removes a logged-in session.\n   * @param jwt A valid, logged-in JWT.\n   * @param scope The logout sope.\n   */\n  async signOut(\n    jwt: string,\n    scope: 'global' | 'local' | 'others' = 'global'\n  ): Promise<{ data: null; error: AuthError | null }> {\n    try {\n      await _request(this.fetch, 'POST', `${this.url}/logout?scope=${scope}`, {\n        headers: this.headers,\n        jwt,\n        noResolveJson: true,\n      })\n      return { data: null, error: null }\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: null, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Sends an invite link to an email address.\n   * @param email The email address of the user.\n   * @param options Additional options to be included when inviting.\n   */\n  async inviteUserByEmail(\n    email: string,\n    options: {\n      /** A custom data object to store additional metadata about the user. This maps to the `auth.users.user_metadata` column. */\n      data?: object\n\n      /** The URL which will be appended to the email link sent to the user's email address. Once clicked the user will end up on this URL. */\n      redirectTo?: string\n    } = {}\n  ): Promise<UserResponse> {\n    try {\n      return await _request(this.fetch, 'POST', `${this.url}/invite`, {\n        body: { email, data: options.data },\n        headers: this.headers,\n        redirectTo: options.redirectTo,\n        xform: _userResponse,\n      })\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { user: null }, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Generates email links and OTPs to be sent via a custom email provider.\n   * @param email The user's email.\n   * @param options.password User password. For signup only.\n   * @param options.data Optional user metadata. For signup only.\n   * @param options.redirectTo The redirect url which should be appended to the generated link\n   */\n  async generateLink(params: GenerateLinkParams): Promise<GenerateLinkResponse> {\n    try {\n      const { options, ...rest } = params\n      const body: any = { ...rest, ...options }\n      if ('newEmail' in rest) {\n        // replace newEmail with new_email in request body\n        body.new_email = rest?.newEmail\n        delete body['newEmail']\n      }\n      return await _request(this.fetch, 'POST', `${this.url}/admin/generate_link`, {\n        body: body,\n        headers: this.headers,\n        xform: _generateLinkResponse,\n        redirectTo: options?.redirectTo,\n      })\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: {\n            properties: null,\n            user: null,\n          },\n          error,\n        }\n      }\n      throw error\n    }\n  }\n\n  // User Admin API\n  /**\n   * Creates a new user.\n   * This function should only be called on a server. Never expose your `service_role` key in the browser.\n   */\n  async createUser(attributes: AdminUserAttributes): Promise<UserResponse> {\n    try {\n      return await _request(this.fetch, 'POST', `${this.url}/admin/users`, {\n        body: attributes,\n        headers: this.headers,\n        xform: _userResponse,\n      })\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { user: null }, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Get a list of users.\n   *\n   * This function should only be called on a server. Never expose your `service_role` key in the browser.\n   * @param params An object which supports `page` and `perPage` as numbers, to alter the paginated results.\n   */\n  async listUsers(\n    params?: PageParams\n  ): Promise<\n    | { data: { users: User[]; aud: string } & Pagination; error: null }\n    | { data: { users: [] }; error: AuthError }\n  > {\n    try {\n      const pagination: Pagination = { nextPage: null, lastPage: 0, total: 0 }\n      const response = await _request(this.fetch, 'GET', `${this.url}/admin/users`, {\n        headers: this.headers,\n        noResolveJson: true,\n        query: {\n          page: params?.page?.toString() ?? '',\n          per_page: params?.perPage?.toString() ?? '',\n        },\n        xform: _noResolveJsonResponse,\n      })\n      if (response.error) throw response.error\n\n      const users = await response.json()\n      const total = response.headers.get('x-total-count') ?? 0\n      const links = response.headers.get('link')?.split(',') ?? []\n      if (links.length > 0) {\n        links.forEach((link: string) => {\n          const page = parseInt(link.split(';')[0].split('=')[1].substring(0, 1))\n          const rel = JSON.parse(link.split(';')[1].split('=')[1])\n          pagination[`${rel}Page`] = page\n        })\n\n        pagination.total = parseInt(total)\n      }\n      return { data: { ...users, ...pagination }, error: null }\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { users: [] }, error }\n      }\n      throw error\n    }\n  }\n\n  /**\n   * Get user by id.\n   *\n   * @param uid The user's unique identifier\n   *\n   * This function should only be called on a server. Never expose your `service_role` key in the browser.\n   */\n  async getUserById(uid: string): Promise<UserResponse> {\n    try {\n      return await _request(this.fetch, 'GET', `${this.url}/admin/users/${uid}`, {\n        headers: this.headers,\n        xform: _userResponse,\n      })\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { user: null }, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Updates the user data.\n   *\n   * @param attributes The data you want to update.\n   *\n   * This function should only be called on a server. Never expose your `service_role` key in the browser.\n   */\n  async updateUserById(uid: string, attributes: AdminUserAttributes): Promise<UserResponse> {\n    try {\n      return await _request(this.fetch, 'PUT', `${this.url}/admin/users/${uid}`, {\n        body: attributes,\n        headers: this.headers,\n        xform: _userResponse,\n      })\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { user: null }, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Delete a user. Requires a `service_role` key.\n   *\n   * @param id The user id you want to remove.\n   * @param shouldSoftDelete If true, then the user will be soft-deleted (setting `deleted_at` to the current timestamp and disabling their account while preserving their data) from the auth schema.\n   * Defaults to false for backward compatibility.\n   *\n   * This function should only be called on a server. Never expose your `service_role` key in the browser.\n   */\n  async deleteUser(id: string, shouldSoftDelete = false): Promise<UserResponse> {\n    try {\n      return await _request(this.fetch, 'DELETE', `${this.url}/admin/users/${id}`, {\n        headers: this.headers,\n        body: {\n          should_soft_delete: shouldSoftDelete,\n        },\n        xform: _userResponse,\n      })\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { user: null }, error }\n      }\n\n      throw error\n    }\n  }\n\n  private async _listFactors(\n    params: AuthMFAAdminListFactorsParams\n  ): Promise<AuthMFAAdminListFactorsResponse> {\n    try {\n      const { data, error } = await _request(\n        this.fetch,\n        'GET',\n        `${this.url}/admin/users/${params.userId}/factors`,\n        {\n          headers: this.headers,\n          xform: (factors: any) => {\n            return { data: { factors }, error: null }\n          },\n        }\n      )\n      return { data, error }\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: null, error }\n      }\n\n      throw error\n    }\n  }\n\n  private async _deleteFactor(\n    params: AuthMFAAdminDeleteFactorParams\n  ): Promise<AuthMFAAdminDeleteFactorResponse> {\n    try {\n      const data = await _request(\n        this.fetch,\n        'DELETE',\n        `${this.url}/admin/users/${params.userId}/factors/${params.id}`,\n        {\n          headers: this.headers,\n        }\n      )\n\n      return { data, error: null }\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: null, error }\n      }\n\n      throw error\n    }\n  }\n}\n", "import { supportsLocalStorage } from './helpers'\nimport { SupportedStorage } from './types'\n\n/**\n * Provides safe access to the globalThis.localStorage property.\n */\nexport const localStorageAdapter: SupportedStorage = {\n  getItem: (key) => {\n    if (!supportsLocalStorage()) {\n      return null\n    }\n\n    return globalThis.localStorage.getItem(key)\n  },\n  setItem: (key, value) => {\n    if (!supportsLocalStorage()) {\n      return\n    }\n\n    globalThis.localStorage.setItem(key, value)\n  },\n  removeItem: (key) => {\n    if (!supportsLocalStorage()) {\n      return\n    }\n\n    globalThis.localStorage.removeItem(key)\n  },\n}\n\n/**\n * Returns a localStorage-like object that stores the key-value pairs in\n * memory.\n */\nexport function memoryLocalStorageAdapter(store: { [key: string]: string } = {}): SupportedStorage {\n  return {\n    getItem: (key) => {\n      return store[key] || null\n    },\n\n    setItem: (key, value) => {\n      store[key] = value\n    },\n\n    removeItem: (key) => {\n      delete store[key]\n    },\n  }\n}\n", "/**\n * https://mathiasbynens.be/notes/globalthis\n */\nexport function polyfillGlobalThis() {\n  if (typeof globalThis === 'object') return\n  try {\n    Object.defineProperty(Object.prototype, '__magic__', {\n      get: function () {\n        return this\n      },\n      configurable: true,\n    })\n    // @ts-expect-error 'Allow access to magic'\n    __magic__.globalThis = __magic__\n    // @ts-expect-error 'Allow access to magic'\n    delete Object.prototype.__magic__\n  } catch (e) {\n    if (typeof self !== 'undefined') {\n      // @ts-expect-error 'Allow access to globals'\n      self.globalThis = self\n    }\n  }\n}\n", "import { supportsLocalStorage } from './helpers'\n\n/**\n * @experimental\n */\nexport const internals = {\n  /**\n   * @experimental\n   */\n  debug: !!(\n    globalThis &&\n    supportsLocalStorage() &&\n    globalThis.localStorage &&\n    globalThis.localStorage.getItem('supabase.gotrue-js.locks.debug') === 'true'\n  ),\n}\n\n/**\n * An error thrown when a lock cannot be acquired after some amount of time.\n *\n * Use the {@link #isAcquireTimeout} property instead of checking with `instanceof`.\n */\nexport abstract class LockAcquireTimeoutError extends Error {\n  public readonly isAcquireTimeout = true\n\n  constructor(message: string) {\n    super(message)\n  }\n}\n\nexport class NavigatorLockAcquireTimeoutError extends LockAcquireTimeoutError {}\n\n/**\n * Implements a global exclusive lock using the Navigator LockManager API. It\n * is available on all browsers released after 2022-03-15 with <PERSON><PERSON> being the\n * last one to release support. If the API is not available, this function will\n * throw. Make sure you check availablility before configuring {@link\n * GoTrueClient}.\n *\n * You can turn on debugging by setting the `supabase.gotrue-js.locks.debug`\n * local storage item to `true`.\n *\n * Internals:\n *\n * Since the LockManager API does not preserve stack traces for the async\n * function passed in the `request` method, a trick is used where acquiring the\n * lock releases a previously started promise to run the operation in the `fn`\n * function. The lock waits for that promise to finish (with or without error),\n * while the function will finally wait for the result anyway.\n *\n * @param name Name of the lock to be acquired.\n * @param acquireTimeout If negative, no timeout. If 0 an error is thrown if\n *                       the lock can't be acquired without waiting. If positive, the lock acquire\n *                       will time out after so many milliseconds. An error is\n *                       a timeout if it has `isAcquireTimeout` set to true.\n * @param fn The operation to run once the lock is acquired.\n */\nexport async function navigatorLock<R>(\n  name: string,\n  acquireTimeout: number,\n  fn: () => Promise<R>\n): Promise<R> {\n  if (internals.debug) {\n    console.log('@supabase/gotrue-js: navigatorLock: acquire lock', name, acquireTimeout)\n  }\n\n  const abortController = new globalThis.AbortController()\n\n  if (acquireTimeout > 0) {\n    setTimeout(() => {\n      abortController.abort()\n      if (internals.debug) {\n        console.log('@supabase/gotrue-js: navigatorLock acquire timed out', name)\n      }\n    }, acquireTimeout)\n  }\n\n  // MDN article: https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request\n\n  return await globalThis.navigator.locks.request(\n    name,\n    acquireTimeout === 0\n      ? {\n          mode: 'exclusive',\n          ifAvailable: true,\n        }\n      : {\n          mode: 'exclusive',\n          signal: abortController.signal,\n        },\n    async (lock) => {\n      if (lock) {\n        if (internals.debug) {\n          console.log('@supabase/gotrue-js: navigatorLock: acquired', name, lock.name)\n        }\n\n        try {\n          return await fn()\n        } finally {\n          if (internals.debug) {\n            console.log('@supabase/gotrue-js: navigatorLock: released', name, lock.name)\n          }\n        }\n      } else {\n        if (acquireTimeout === 0) {\n          if (internals.debug) {\n            console.log('@supabase/gotrue-js: navigatorLock: not immediately available', name)\n          }\n\n          throw new NavigatorLockAcquireTimeoutError(\n            `Acquiring an exclusive Navigator LockManager lock \"${name}\" immediately failed`\n          )\n        } else {\n          if (internals.debug) {\n            try {\n              const result = await globalThis.navigator.locks.query()\n\n              console.log(\n                '@supabase/gotrue-js: Navigator LockManager state',\n                JSON.stringify(result, null, '  ')\n              )\n            } catch (e: any) {\n              console.warn(\n                '@supabase/gotrue-js: Error when querying Navigator LockManager state',\n                e\n              )\n            }\n          }\n\n          // Browser is not following the Navigator LockManager spec, it\n          // returned a null lock when we didn't use ifAvailable. So we can\n          // pretend the lock is acquired in the name of backward compatibility\n          // and user experience and just run the function.\n          console.warn(\n            '@supabase/gotrue-js: Navigator LockManager returned a null lock when using #request without ifAvailable set to true, it appears this browser is not following the LockManager spec https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request'\n          )\n\n          return await fn()\n        }\n      }\n    }\n  )\n}\n", "import GoTrueAdminApi from './GoTrueAdminApi'\nimport { DEFAULT_HEADERS, EXPIRY_MARGIN, GOTRUE_URL, STORAGE_KEY } from './lib/constants'\nimport {\n  AuthError,\n  AuthImplicitGrantRedirectError,\n  AuthPKCEGrantCodeExchangeError,\n  AuthInvalidCredentialsError,\n  AuthSessionMissingError,\n  AuthInvalidTokenResponseError,\n  AuthUnknownError,\n  isAuthApiError,\n  isAuthError,\n  isAuthRetryableFetchError,\n  isAuthSessionMissingError,\n} from './lib/errors'\nimport {\n  Fetch,\n  _request,\n  _sessionResponse,\n  _sessionResponsePassword,\n  _userResponse,\n  _ssoResponse,\n} from './lib/fetch'\nimport {\n  decodeJWTPayload,\n  Deferred,\n  getItemAsync,\n  isBrowser,\n  removeItemAsync,\n  resolveFetch,\n  setItemAsync,\n  uuid,\n  retryable,\n  sleep,\n  supportsLocalStorage,\n  parseParametersFromURL,\n  getCodeChallengeAndMethod,\n} from './lib/helpers'\nimport { localStorageAdapter, memoryLocalStorageAdapter } from './lib/local-storage'\nimport { polyfillGlobalThis } from './lib/polyfills'\nimport { version } from './lib/version'\nimport { LockAcquireTimeoutError, navigatorLock } from './lib/locks'\n\nimport type {\n  AuthChangeEvent,\n  AuthResponse,\n  AuthResponsePassword,\n  AuthTokenResponse,\n  AuthTokenResponsePassword,\n  AuthOtpResponse,\n  CallRefreshTokenResult,\n  GoTrueClientOptions,\n  InitializeResult,\n  OAuthResponse,\n  SSOResponse,\n  Provider,\n  Session,\n  SignInWithIdTokenCredentials,\n  SignInWithOAuthCredentials,\n  SignInWithPasswordCredentials,\n  SignInWithPasswordlessCredentials,\n  SignUpWithPasswordCredentials,\n  SignInWithSSO,\n  SignOut,\n  Subscription,\n  SupportedStorage,\n  User,\n  UserAttributes,\n  UserResponse,\n  VerifyOtpParams,\n  GoTrueMFAApi,\n  MFAEnrollParams,\n  AuthMFAEnrollResponse,\n  MFAChallengeParams,\n  AuthMFAChallengeResponse,\n  MFAUnenrollParams,\n  AuthMFAUnenrollResponse,\n  MFAVerifyParams,\n  AuthMFAVerifyResponse,\n  AuthMFAListFactorsResponse,\n  AMREntry,\n  AuthMFAGetAuthenticatorAssuranceLevelResponse,\n  AuthenticatorAssuranceLevels,\n  Factor,\n  MFAChallengeAndVerifyParams,\n  ResendParams,\n  AuthFlowType,\n  LockFunc,\n  UserIdentity,\n  SignInAnonymouslyCredentials,\n} from './lib/types'\n\npolyfillGlobalThis() // Make \"globalThis\" available\n\nconst DEFAULT_OPTIONS: Omit<Required<GoTrueClientOptions>, 'fetch' | 'storage' | 'lock'> = {\n  url: GOTRUE_URL,\n  storageKey: STORAGE_KEY,\n  autoRefreshToken: true,\n  persistSession: true,\n  detectSessionInUrl: true,\n  headers: DEFAULT_HEADERS,\n  flowType: 'implicit',\n  debug: false,\n  hasCustomAuthorizationHeader: false,\n}\n\n/** Current session will be checked for refresh at this interval. */\nconst AUTO_REFRESH_TICK_DURATION = 30 * 1000\n\n/**\n * A token refresh will be attempted this many ticks before the current session expires. */\nconst AUTO_REFRESH_TICK_THRESHOLD = 3\n\nasync function lockNoOp<R>(name: string, acquireTimeout: number, fn: () => Promise<R>): Promise<R> {\n  return await fn()\n}\n\nexport default class GoTrueClient {\n  private static nextInstanceID = 0\n\n  private instanceID: number\n\n  /**\n   * Namespace for the GoTrue admin methods.\n   * These methods should only be used in a trusted server-side environment.\n   */\n  admin: GoTrueAdminApi\n  /**\n   * Namespace for the MFA methods.\n   */\n  mfa: GoTrueMFAApi\n  /**\n   * The storage key used to identify the values saved in localStorage\n   */\n  protected storageKey: string\n\n  protected flowType: AuthFlowType\n\n  protected autoRefreshToken: boolean\n  protected persistSession: boolean\n  protected storage: SupportedStorage\n  protected memoryStorage: { [key: string]: string } | null = null\n  protected stateChangeEmitters: Map<string, Subscription> = new Map()\n  protected autoRefreshTicker: ReturnType<typeof setInterval> | null = null\n  protected visibilityChangedCallback: (() => Promise<any>) | null = null\n  protected refreshingDeferred: Deferred<CallRefreshTokenResult> | null = null\n  /**\n   * Keeps track of the async client initialization.\n   * When null or not yet resolved the auth state is `unknown`\n   * Once resolved the the auth state is known and it's save to call any further client methods.\n   * Keep extra care to never reject or throw uncaught errors\n   */\n  protected initializePromise: Promise<InitializeResult> | null = null\n  protected detectSessionInUrl = true\n  protected url: string\n  protected headers: {\n    [key: string]: string\n  }\n  protected hasCustomAuthorizationHeader = false\n  protected suppressGetSessionWarning = false\n  protected fetch: Fetch\n  protected lock: LockFunc\n  protected lockAcquired = false\n  protected pendingInLock: Promise<any>[] = []\n\n  /**\n   * Used to broadcast state change events to other tabs listening.\n   */\n  protected broadcastChannel: BroadcastChannel | null = null\n\n  protected logDebugMessages: boolean\n  protected logger: (message: string, ...args: any[]) => void = console.log\n\n  /**\n   * Create a new client for use in the browser.\n   */\n  constructor(options: GoTrueClientOptions) {\n    this.instanceID = GoTrueClient.nextInstanceID\n    GoTrueClient.nextInstanceID += 1\n\n    if (this.instanceID > 0 && isBrowser()) {\n      console.warn(\n        'Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.'\n      )\n    }\n\n    const settings = { ...DEFAULT_OPTIONS, ...options }\n\n    this.logDebugMessages = !!settings.debug\n    if (typeof settings.debug === 'function') {\n      this.logger = settings.debug\n    }\n\n    this.persistSession = settings.persistSession\n    this.storageKey = settings.storageKey\n    this.autoRefreshToken = settings.autoRefreshToken\n    this.admin = new GoTrueAdminApi({\n      url: settings.url,\n      headers: settings.headers,\n      fetch: settings.fetch,\n    })\n\n    this.url = settings.url\n    this.headers = settings.headers\n    this.fetch = resolveFetch(settings.fetch)\n    this.lock = settings.lock || lockNoOp\n    this.detectSessionInUrl = settings.detectSessionInUrl\n    this.flowType = settings.flowType\n    this.hasCustomAuthorizationHeader = settings.hasCustomAuthorizationHeader\n\n    if (settings.lock) {\n      this.lock = settings.lock\n    } else if (isBrowser() && globalThis?.navigator?.locks) {\n      this.lock = navigatorLock\n    } else {\n      this.lock = lockNoOp\n    }\n\n    this.mfa = {\n      verify: this._verify.bind(this),\n      enroll: this._enroll.bind(this),\n      unenroll: this._unenroll.bind(this),\n      challenge: this._challenge.bind(this),\n      listFactors: this._listFactors.bind(this),\n      challengeAndVerify: this._challengeAndVerify.bind(this),\n      getAuthenticatorAssuranceLevel: this._getAuthenticatorAssuranceLevel.bind(this),\n    }\n\n    if (this.persistSession) {\n      if (settings.storage) {\n        this.storage = settings.storage\n      } else {\n        if (supportsLocalStorage()) {\n          this.storage = localStorageAdapter\n        } else {\n          this.memoryStorage = {}\n          this.storage = memoryLocalStorageAdapter(this.memoryStorage)\n        }\n      }\n    } else {\n      this.memoryStorage = {}\n      this.storage = memoryLocalStorageAdapter(this.memoryStorage)\n    }\n\n    if (isBrowser() && globalThis.BroadcastChannel && this.persistSession && this.storageKey) {\n      try {\n        this.broadcastChannel = new globalThis.BroadcastChannel(this.storageKey)\n      } catch (e: any) {\n        console.error(\n          'Failed to create a new BroadcastChannel, multi-tab state changes will not be available',\n          e\n        )\n      }\n\n      this.broadcastChannel?.addEventListener('message', async (event) => {\n        this._debug('received broadcast notification from other tab or client', event)\n\n        await this._notifyAllSubscribers(event.data.event, event.data.session, false) // broadcast = false so we don't get an endless loop of messages\n      })\n    }\n\n    this.initialize()\n  }\n\n  private _debug(...args: any[]): GoTrueClient {\n    if (this.logDebugMessages) {\n      this.logger(\n        `GoTrueClient@${this.instanceID} (${version}) ${new Date().toISOString()}`,\n        ...args\n      )\n    }\n\n    return this\n  }\n\n  /**\n   * Initializes the client session either from the url or from storage.\n   * This method is automatically called when instantiating the client, but should also be called\n   * manually when checking for an error from an auth redirect (oauth, magiclink, password recovery, etc).\n   */\n  async initialize(): Promise<InitializeResult> {\n    if (this.initializePromise) {\n      return await this.initializePromise\n    }\n\n    this.initializePromise = (async () => {\n      return await this._acquireLock(-1, async () => {\n        return await this._initialize()\n      })\n    })()\n\n    return await this.initializePromise\n  }\n\n  /**\n   * IMPORTANT:\n   * 1. Never throw in this method, as it is called from the constructor\n   * 2. Never return a session from this method as it would be cached over\n   *    the whole lifetime of the client\n   */\n  private async _initialize(): Promise<InitializeResult> {\n    try {\n      const isPKCEFlow = isBrowser() ? await this._isPKCEFlow() : false\n      this._debug('#_initialize()', 'begin', 'is PKCE flow', isPKCEFlow)\n\n      if (isPKCEFlow || (this.detectSessionInUrl && this._isImplicitGrantFlow())) {\n        const { data, error } = await this._getSessionFromURL(isPKCEFlow)\n        if (error) {\n          this._debug('#_initialize()', 'error detecting session from URL', error)\n\n          // hacky workaround to keep the existing session if there's an error returned from identity linking\n          // TODO: once error codes are ready, we should match against it instead of the message\n          if (\n            error?.message === 'Identity is already linked' ||\n            error?.message === 'Identity is already linked to another user'\n          ) {\n            return { error }\n          }\n\n          // failed login attempt via url,\n          // remove old session as in verifyOtp, signUp and signInWith*\n          await this._removeSession()\n\n          return { error }\n        }\n\n        const { session, redirectType } = data\n\n        this._debug(\n          '#_initialize()',\n          'detected session in URL',\n          session,\n          'redirect type',\n          redirectType\n        )\n\n        await this._saveSession(session)\n\n        setTimeout(async () => {\n          if (redirectType === 'recovery') {\n            await this._notifyAllSubscribers('PASSWORD_RECOVERY', session)\n          } else {\n            await this._notifyAllSubscribers('SIGNED_IN', session)\n          }\n        }, 0)\n\n        return { error: null }\n      }\n      // no login attempt via callback url try to recover session from storage\n      await this._recoverAndRefresh()\n      return { error: null }\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { error }\n      }\n\n      return {\n        error: new AuthUnknownError('Unexpected error during initialization', error),\n      }\n    } finally {\n      await this._handleVisibilityChange()\n      this._debug('#_initialize()', 'end')\n    }\n  }\n\n  /**\n   * Creates a new anonymous user.\n   *\n   * @returns A session where the is_anonymous claim in the access token JWT set to true\n   */\n  async signInAnonymously(credentials?: SignInAnonymouslyCredentials): Promise<AuthResponse> {\n    try {\n      const res = await _request(this.fetch, 'POST', `${this.url}/signup`, {\n        headers: this.headers,\n        body: {\n          data: credentials?.options?.data ?? {},\n          gotrue_meta_security: { captcha_token: credentials?.options?.captchaToken },\n        },\n        xform: _sessionResponse,\n      })\n      const { data, error } = res\n\n      if (error || !data) {\n        return { data: { user: null, session: null }, error: error }\n      }\n      const session: Session | null = data.session\n      const user: User | null = data.user\n\n      if (data.session) {\n        await this._saveSession(data.session)\n        await this._notifyAllSubscribers('SIGNED_IN', session)\n      }\n\n      return { data: { user, session }, error: null }\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { user: null, session: null }, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Creates a new user.\n   *\n   * Be aware that if a user account exists in the system you may get back an\n   * error message that attempts to hide this information from the user.\n   * This method has support for PKCE via email signups. The PKCE flow cannot be used when autoconfirm is enabled.\n   *\n   * @returns A logged-in session if the server has \"autoconfirm\" ON\n   * @returns A user if the server has \"autoconfirm\" OFF\n   */\n  async signUp(credentials: SignUpWithPasswordCredentials): Promise<AuthResponse> {\n    try {\n      let res: AuthResponse\n      if ('email' in credentials) {\n        const { email, password, options } = credentials\n        let codeChallenge: string | null = null\n        let codeChallengeMethod: string | null = null\n        if (this.flowType === 'pkce') {\n          ;[codeChallenge, codeChallengeMethod] = await getCodeChallengeAndMethod(\n            this.storage,\n            this.storageKey\n          )\n        }\n        res = await _request(this.fetch, 'POST', `${this.url}/signup`, {\n          headers: this.headers,\n          redirectTo: options?.emailRedirectTo,\n          body: {\n            email,\n            password,\n            data: options?.data ?? {},\n            gotrue_meta_security: { captcha_token: options?.captchaToken },\n            code_challenge: codeChallenge,\n            code_challenge_method: codeChallengeMethod,\n          },\n          xform: _sessionResponse,\n        })\n      } else if ('phone' in credentials) {\n        const { phone, password, options } = credentials\n        res = await _request(this.fetch, 'POST', `${this.url}/signup`, {\n          headers: this.headers,\n          body: {\n            phone,\n            password,\n            data: options?.data ?? {},\n            channel: options?.channel ?? 'sms',\n            gotrue_meta_security: { captcha_token: options?.captchaToken },\n          },\n          xform: _sessionResponse,\n        })\n      } else {\n        throw new AuthInvalidCredentialsError(\n          'You must provide either an email or phone number and a password'\n        )\n      }\n\n      const { data, error } = res\n\n      if (error || !data) {\n        return { data: { user: null, session: null }, error: error }\n      }\n\n      const session: Session | null = data.session\n      const user: User | null = data.user\n\n      if (data.session) {\n        await this._saveSession(data.session)\n        await this._notifyAllSubscribers('SIGNED_IN', session)\n      }\n\n      return { data: { user, session }, error: null }\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { user: null, session: null }, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Log in an existing user with an email and password or phone and password.\n   *\n   * Be aware that you may get back an error message that will not distinguish\n   * between the cases where the account does not exist or that the\n   * email/phone and password combination is wrong or that the account can only\n   * be accessed via social login.\n   */\n  async signInWithPassword(\n    credentials: SignInWithPasswordCredentials\n  ): Promise<AuthTokenResponsePassword> {\n    try {\n      let res: AuthResponsePassword\n      if ('email' in credentials) {\n        const { email, password, options } = credentials\n        res = await _request(this.fetch, 'POST', `${this.url}/token?grant_type=password`, {\n          headers: this.headers,\n          body: {\n            email,\n            password,\n            gotrue_meta_security: { captcha_token: options?.captchaToken },\n          },\n          xform: _sessionResponsePassword,\n        })\n      } else if ('phone' in credentials) {\n        const { phone, password, options } = credentials\n        res = await _request(this.fetch, 'POST', `${this.url}/token?grant_type=password`, {\n          headers: this.headers,\n          body: {\n            phone,\n            password,\n            gotrue_meta_security: { captcha_token: options?.captchaToken },\n          },\n          xform: _sessionResponsePassword,\n        })\n      } else {\n        throw new AuthInvalidCredentialsError(\n          'You must provide either an email or phone number and a password'\n        )\n      }\n      const { data, error } = res\n\n      if (error) {\n        return { data: { user: null, session: null }, error }\n      } else if (!data || !data.session || !data.user) {\n        return { data: { user: null, session: null }, error: new AuthInvalidTokenResponseError() }\n      }\n      if (data.session) {\n        await this._saveSession(data.session)\n        await this._notifyAllSubscribers('SIGNED_IN', data.session)\n      }\n      return {\n        data: {\n          user: data.user,\n          session: data.session,\n          ...(data.weak_password ? { weakPassword: data.weak_password } : null),\n        },\n        error,\n      }\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { user: null, session: null }, error }\n      }\n      throw error\n    }\n  }\n\n  /**\n   * Log in an existing user via a third-party provider.\n   * This method supports the PKCE flow.\n   */\n  async signInWithOAuth(credentials: SignInWithOAuthCredentials): Promise<OAuthResponse> {\n    return await this._handleProviderSignIn(credentials.provider, {\n      redirectTo: credentials.options?.redirectTo,\n      scopes: credentials.options?.scopes,\n      queryParams: credentials.options?.queryParams,\n      skipBrowserRedirect: credentials.options?.skipBrowserRedirect,\n    })\n  }\n\n  /**\n   * Log in an existing user by exchanging an Auth Code issued during the PKCE flow.\n   */\n  async exchangeCodeForSession(authCode: string): Promise<AuthTokenResponse> {\n    await this.initializePromise\n\n    return this._acquireLock(-1, async () => {\n      return this._exchangeCodeForSession(authCode)\n    })\n  }\n\n  private async _exchangeCodeForSession(authCode: string): Promise<\n    | {\n        data: { session: Session; user: User; redirectType: string | null }\n        error: null\n      }\n    | { data: { session: null; user: null; redirectType: null }; error: AuthError }\n  > {\n    const storageItem = await getItemAsync(this.storage, `${this.storageKey}-code-verifier`)\n    const [codeVerifier, redirectType] = ((storageItem ?? '') as string).split('/')\n\n    try {\n      const { data, error } = await _request(\n        this.fetch,\n        'POST',\n        `${this.url}/token?grant_type=pkce`,\n        {\n          headers: this.headers,\n          body: {\n            auth_code: authCode,\n            code_verifier: codeVerifier,\n          },\n          xform: _sessionResponse,\n        }\n      )\n      await removeItemAsync(this.storage, `${this.storageKey}-code-verifier`)\n      if (error) {\n        throw error\n      }\n      if (!data || !data.session || !data.user) {\n        return {\n          data: { user: null, session: null, redirectType: null },\n          error: new AuthInvalidTokenResponseError(),\n        }\n      }\n      if (data.session) {\n        await this._saveSession(data.session)\n        await this._notifyAllSubscribers('SIGNED_IN', data.session)\n      }\n      return { data: { ...data, redirectType: redirectType ?? null }, error }\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { user: null, session: null, redirectType: null }, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Allows signing in with an OIDC ID token. The authentication provider used\n   * should be enabled and configured.\n   */\n  async signInWithIdToken(credentials: SignInWithIdTokenCredentials): Promise<AuthTokenResponse> {\n    try {\n      const { options, provider, token, access_token, nonce } = credentials\n\n      const res = await _request(this.fetch, 'POST', `${this.url}/token?grant_type=id_token`, {\n        headers: this.headers,\n        body: {\n          provider,\n          id_token: token,\n          access_token,\n          nonce,\n          gotrue_meta_security: { captcha_token: options?.captchaToken },\n        },\n        xform: _sessionResponse,\n      })\n\n      const { data, error } = res\n      if (error) {\n        return { data: { user: null, session: null }, error }\n      } else if (!data || !data.session || !data.user) {\n        return {\n          data: { user: null, session: null },\n          error: new AuthInvalidTokenResponseError(),\n        }\n      }\n      if (data.session) {\n        await this._saveSession(data.session)\n        await this._notifyAllSubscribers('SIGNED_IN', data.session)\n      }\n      return { data, error }\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { user: null, session: null }, error }\n      }\n      throw error\n    }\n  }\n\n  /**\n   * Log in a user using magiclink or a one-time password (OTP).\n   *\n   * If the `{{ .ConfirmationURL }}` variable is specified in the email template, a magiclink will be sent.\n   * If the `{{ .Token }}` variable is specified in the email template, an OTP will be sent.\n   * If you're using phone sign-ins, only an OTP will be sent. You won't be able to send a magiclink for phone sign-ins.\n   *\n   * Be aware that you may get back an error message that will not distinguish\n   * between the cases where the account does not exist or, that the account\n   * can only be accessed via social login.\n   *\n   * Do note that you will need to configure a Whatsapp sender on Twilio\n   * if you are using phone sign in with the 'whatsapp' channel. The whatsapp\n   * channel is not supported on other providers\n   * at this time.\n   * This method supports PKCE when an email is passed.\n   */\n  async signInWithOtp(credentials: SignInWithPasswordlessCredentials): Promise<AuthOtpResponse> {\n    try {\n      if ('email' in credentials) {\n        const { email, options } = credentials\n        let codeChallenge: string | null = null\n        let codeChallengeMethod: string | null = null\n        if (this.flowType === 'pkce') {\n          ;[codeChallenge, codeChallengeMethod] = await getCodeChallengeAndMethod(\n            this.storage,\n            this.storageKey\n          )\n        }\n        const { error } = await _request(this.fetch, 'POST', `${this.url}/otp`, {\n          headers: this.headers,\n          body: {\n            email,\n            data: options?.data ?? {},\n            create_user: options?.shouldCreateUser ?? true,\n            gotrue_meta_security: { captcha_token: options?.captchaToken },\n            code_challenge: codeChallenge,\n            code_challenge_method: codeChallengeMethod,\n          },\n          redirectTo: options?.emailRedirectTo,\n        })\n        return { data: { user: null, session: null }, error }\n      }\n      if ('phone' in credentials) {\n        const { phone, options } = credentials\n        const { data, error } = await _request(this.fetch, 'POST', `${this.url}/otp`, {\n          headers: this.headers,\n          body: {\n            phone,\n            data: options?.data ?? {},\n            create_user: options?.shouldCreateUser ?? true,\n            gotrue_meta_security: { captcha_token: options?.captchaToken },\n            channel: options?.channel ?? 'sms',\n          },\n        })\n        return { data: { user: null, session: null, messageId: data?.message_id }, error }\n      }\n      throw new AuthInvalidCredentialsError('You must provide either an email or phone number.')\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { user: null, session: null }, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Log in a user given a User supplied OTP or TokenHash received through mobile or email.\n   */\n  async verifyOtp(params: VerifyOtpParams): Promise<AuthResponse> {\n    try {\n      let redirectTo: string | undefined = undefined\n      let captchaToken: string | undefined = undefined\n      if ('options' in params) {\n        redirectTo = params.options?.redirectTo\n        captchaToken = params.options?.captchaToken\n      }\n      const { data, error } = await _request(this.fetch, 'POST', `${this.url}/verify`, {\n        headers: this.headers,\n        body: {\n          ...params,\n          gotrue_meta_security: { captcha_token: captchaToken },\n        },\n        redirectTo,\n        xform: _sessionResponse,\n      })\n\n      if (error) {\n        throw error\n      }\n\n      if (!data) {\n        throw new Error('An error occurred on token verification.')\n      }\n\n      const session: Session | null = data.session\n      const user: User = data.user\n\n      if (session?.access_token) {\n        await this._saveSession(session as Session)\n        await this._notifyAllSubscribers(\n          params.type == 'recovery' ? 'PASSWORD_RECOVERY' : 'SIGNED_IN',\n          session\n        )\n      }\n\n      return { data: { user, session }, error: null }\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { user: null, session: null }, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Attempts a single-sign on using an enterprise Identity Provider. A\n   * successful SSO attempt will redirect the current page to the identity\n   * provider authorization page. The redirect URL is implementation and SSO\n   * protocol specific.\n   *\n   * You can use it by providing a SSO domain. Typically you can extract this\n   * domain by asking users for their email address. If this domain is\n   * registered on the Auth instance the redirect will use that organization's\n   * currently active SSO Identity Provider for the login.\n   *\n   * If you have built an organization-specific login page, you can use the\n   * organization's SSO Identity Provider UUID directly instead.\n   */\n  async signInWithSSO(params: SignInWithSSO): Promise<SSOResponse> {\n    try {\n      let codeChallenge: string | null = null\n      let codeChallengeMethod: string | null = null\n      if (this.flowType === 'pkce') {\n        ;[codeChallenge, codeChallengeMethod] = await getCodeChallengeAndMethod(\n          this.storage,\n          this.storageKey\n        )\n      }\n\n      return await _request(this.fetch, 'POST', `${this.url}/sso`, {\n        body: {\n          ...('providerId' in params ? { provider_id: params.providerId } : null),\n          ...('domain' in params ? { domain: params.domain } : null),\n          redirect_to: params.options?.redirectTo ?? undefined,\n          ...(params?.options?.captchaToken\n            ? { gotrue_meta_security: { captcha_token: params.options.captchaToken } }\n            : null),\n          skip_http_redirect: true, // fetch does not handle redirects\n          code_challenge: codeChallenge,\n          code_challenge_method: codeChallengeMethod,\n        },\n        headers: this.headers,\n        xform: _ssoResponse,\n      })\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: null, error }\n      }\n      throw error\n    }\n  }\n\n  /**\n   * Sends a reauthentication OTP to the user's email or phone number.\n   * Requires the user to be signed-in.\n   */\n  async reauthenticate(): Promise<AuthResponse> {\n    await this.initializePromise\n\n    return await this._acquireLock(-1, async () => {\n      return await this._reauthenticate()\n    })\n  }\n\n  private async _reauthenticate(): Promise<AuthResponse> {\n    try {\n      return await this._useSession(async (result) => {\n        const {\n          data: { session },\n          error: sessionError,\n        } = result\n        if (sessionError) throw sessionError\n        if (!session) throw new AuthSessionMissingError()\n\n        const { error } = await _request(this.fetch, 'GET', `${this.url}/reauthenticate`, {\n          headers: this.headers,\n          jwt: session.access_token,\n        })\n        return { data: { user: null, session: null }, error }\n      })\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { user: null, session: null }, error }\n      }\n      throw error\n    }\n  }\n\n  /**\n   * Resends an existing signup confirmation email, email change email, SMS OTP or phone change OTP.\n   */\n  async resend(credentials: ResendParams): Promise<AuthOtpResponse> {\n    try {\n      const endpoint = `${this.url}/resend`\n      if ('email' in credentials) {\n        const { email, type, options } = credentials\n        const { error } = await _request(this.fetch, 'POST', endpoint, {\n          headers: this.headers,\n          body: {\n            email,\n            type,\n            gotrue_meta_security: { captcha_token: options?.captchaToken },\n          },\n          redirectTo: options?.emailRedirectTo,\n        })\n        return { data: { user: null, session: null }, error }\n      } else if ('phone' in credentials) {\n        const { phone, type, options } = credentials\n        const { data, error } = await _request(this.fetch, 'POST', endpoint, {\n          headers: this.headers,\n          body: {\n            phone,\n            type,\n            gotrue_meta_security: { captcha_token: options?.captchaToken },\n          },\n        })\n        return { data: { user: null, session: null, messageId: data?.message_id }, error }\n      }\n      throw new AuthInvalidCredentialsError(\n        'You must provide either an email or phone number and a type'\n      )\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { user: null, session: null }, error }\n      }\n      throw error\n    }\n  }\n\n  /**\n   * Returns the session, refreshing it if necessary.\n   *\n   * The session returned can be null if the session is not detected which can happen in the event a user is not signed-in or has logged out.\n   *\n   * **IMPORTANT:** This method loads values directly from the storage attached\n   * to the client. If that storage is based on request cookies for example,\n   * the values in it may not be authentic and therefore it's strongly advised\n   * against using this method and its results in such circumstances. A warning\n   * will be emitted if this is detected. Use {@link #getUser()} instead.\n   */\n  async getSession() {\n    await this.initializePromise\n\n    const result = await this._acquireLock(-1, async () => {\n      return this._useSession(async (result) => {\n        return result\n      })\n    })\n\n    return result\n  }\n\n  /**\n   * Acquires a global lock based on the storage key.\n   */\n  private async _acquireLock<R>(acquireTimeout: number, fn: () => Promise<R>): Promise<R> {\n    this._debug('#_acquireLock', 'begin', acquireTimeout)\n\n    try {\n      if (this.lockAcquired) {\n        const last = this.pendingInLock.length\n          ? this.pendingInLock[this.pendingInLock.length - 1]\n          : Promise.resolve()\n\n        const result = (async () => {\n          await last\n          return await fn()\n        })()\n\n        this.pendingInLock.push(\n          (async () => {\n            try {\n              await result\n            } catch (e: any) {\n              // we just care if it finished\n            }\n          })()\n        )\n\n        return result\n      }\n\n      return await this.lock(`lock:${this.storageKey}`, acquireTimeout, async () => {\n        this._debug('#_acquireLock', 'lock acquired for storage key', this.storageKey)\n\n        try {\n          this.lockAcquired = true\n\n          const result = fn()\n\n          this.pendingInLock.push(\n            (async () => {\n              try {\n                await result\n              } catch (e: any) {\n                // we just care if it finished\n              }\n            })()\n          )\n\n          await result\n\n          // keep draining the queue until there's nothing to wait on\n          while (this.pendingInLock.length) {\n            const waitOn = [...this.pendingInLock]\n\n            await Promise.all(waitOn)\n\n            this.pendingInLock.splice(0, waitOn.length)\n          }\n\n          return await result\n        } finally {\n          this._debug('#_acquireLock', 'lock released for storage key', this.storageKey)\n\n          this.lockAcquired = false\n        }\n      })\n    } finally {\n      this._debug('#_acquireLock', 'end')\n    }\n  }\n\n  /**\n   * Use instead of {@link #getSession} inside the library. It is\n   * semantically usually what you want, as getting a session involves some\n   * processing afterwards that requires only one client operating on the\n   * session at once across multiple tabs or processes.\n   */\n  private async _useSession<R>(\n    fn: (\n      result:\n        | {\n            data: {\n              session: Session\n            }\n            error: null\n          }\n        | {\n            data: {\n              session: null\n            }\n            error: AuthError\n          }\n        | {\n            data: {\n              session: null\n            }\n            error: null\n          }\n    ) => Promise<R>\n  ): Promise<R> {\n    this._debug('#_useSession', 'begin')\n\n    try {\n      // the use of __loadSession here is the only correct use of the function!\n      const result = await this.__loadSession()\n\n      return await fn(result)\n    } finally {\n      this._debug('#_useSession', 'end')\n    }\n  }\n\n  /**\n   * NEVER USE DIRECTLY!\n   *\n   * Always use {@link #_useSession}.\n   */\n  private async __loadSession(): Promise<\n    | {\n        data: {\n          session: Session\n        }\n        error: null\n      }\n    | {\n        data: {\n          session: null\n        }\n        error: AuthError\n      }\n    | {\n        data: {\n          session: null\n        }\n        error: null\n      }\n  > {\n    this._debug('#__loadSession()', 'begin')\n\n    if (!this.lockAcquired) {\n      this._debug('#__loadSession()', 'used outside of an acquired lock!', new Error().stack)\n    }\n\n    try {\n      let currentSession: Session | null = null\n\n      const maybeSession = await getItemAsync(this.storage, this.storageKey)\n\n      this._debug('#getSession()', 'session from storage', maybeSession)\n\n      if (maybeSession !== null) {\n        if (this._isValidSession(maybeSession)) {\n          currentSession = maybeSession\n        } else {\n          this._debug('#getSession()', 'session from storage is not valid')\n          await this._removeSession()\n        }\n      }\n\n      if (!currentSession) {\n        return { data: { session: null }, error: null }\n      }\n\n      const hasExpired = currentSession.expires_at\n        ? currentSession.expires_at <= Date.now() / 1000\n        : false\n\n      this._debug(\n        '#__loadSession()',\n        `session has${hasExpired ? '' : ' not'} expired`,\n        'expires_at',\n        currentSession.expires_at\n      )\n\n      if (!hasExpired) {\n        if (this.storage.isServer) {\n          let suppressWarning = this.suppressGetSessionWarning\n          const proxySession: Session = new Proxy(currentSession, {\n            get: (target: any, prop: string, receiver: any) => {\n              if (!suppressWarning && prop === 'user') {\n                // only show warning when the user object is being accessed from the server\n                console.warn(\n                  'Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and many not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server.'\n                )\n                suppressWarning = true // keeps this proxy instance from logging additional warnings\n                this.suppressGetSessionWarning = true // keeps this client's future proxy instances from warning\n              }\n              return Reflect.get(target, prop, receiver)\n            },\n          })\n          currentSession = proxySession\n        }\n\n        return { data: { session: currentSession }, error: null }\n      }\n\n      const { session, error } = await this._callRefreshToken(currentSession.refresh_token)\n      if (error) {\n        return { data: { session: null }, error }\n      }\n\n      return { data: { session }, error: null }\n    } finally {\n      this._debug('#__loadSession()', 'end')\n    }\n  }\n\n  /**\n   * Gets the current user details if there is an existing session. This method\n   * performs a network request to the Supabase Auth server, so the returned\n   * value is authentic and can be used to base authorization rules on.\n   *\n   * @param jwt Takes in an optional access token JWT. If no JWT is provided, the JWT from the current session is used.\n   */\n  async getUser(jwt?: string): Promise<UserResponse> {\n    if (jwt) {\n      return await this._getUser(jwt)\n    }\n\n    await this.initializePromise\n\n    const result = await this._acquireLock(-1, async () => {\n      return await this._getUser()\n    })\n\n    return result\n  }\n\n  private async _getUser(jwt?: string): Promise<UserResponse> {\n    try {\n      if (jwt) {\n        return await _request(this.fetch, 'GET', `${this.url}/user`, {\n          headers: this.headers,\n          jwt: jwt,\n          xform: _userResponse,\n        })\n      }\n\n      return await this._useSession(async (result) => {\n        const { data, error } = result\n        if (error) {\n          throw error\n        }\n\n        // returns an error if there is no access_token or custom authorization header\n        if (!data.session?.access_token && !this.hasCustomAuthorizationHeader) {\n          return { data: { user: null }, error: new AuthSessionMissingError() }\n        }\n\n        return await _request(this.fetch, 'GET', `${this.url}/user`, {\n          headers: this.headers,\n          jwt: data.session?.access_token ?? undefined,\n          xform: _userResponse,\n        })\n      })\n    } catch (error) {\n      if (isAuthError(error)) {\n        if (isAuthSessionMissingError(error)) {\n          // JWT contains a `session_id` which does not correspond to an active\n          // session in the database, indicating the user is signed out.\n\n          await this._removeSession()\n          await removeItemAsync(this.storage, `${this.storageKey}-code-verifier`)\n          await this._notifyAllSubscribers('SIGNED_OUT', null)\n        }\n\n        return { data: { user: null }, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Updates user data for a logged in user.\n   */\n  async updateUser(\n    attributes: UserAttributes,\n    options: {\n      emailRedirectTo?: string | undefined\n    } = {}\n  ): Promise<UserResponse> {\n    await this.initializePromise\n\n    return await this._acquireLock(-1, async () => {\n      return await this._updateUser(attributes, options)\n    })\n  }\n\n  protected async _updateUser(\n    attributes: UserAttributes,\n    options: {\n      emailRedirectTo?: string | undefined\n    } = {}\n  ): Promise<UserResponse> {\n    try {\n      return await this._useSession(async (result) => {\n        const { data: sessionData, error: sessionError } = result\n        if (sessionError) {\n          throw sessionError\n        }\n        if (!sessionData.session) {\n          throw new AuthSessionMissingError()\n        }\n        const session: Session = sessionData.session\n        let codeChallenge: string | null = null\n        let codeChallengeMethod: string | null = null\n        if (this.flowType === 'pkce' && attributes.email != null) {\n          ;[codeChallenge, codeChallengeMethod] = await getCodeChallengeAndMethod(\n            this.storage,\n            this.storageKey\n          )\n        }\n\n        const { data, error: userError } = await _request(this.fetch, 'PUT', `${this.url}/user`, {\n          headers: this.headers,\n          redirectTo: options?.emailRedirectTo,\n          body: {\n            ...attributes,\n            code_challenge: codeChallenge,\n            code_challenge_method: codeChallengeMethod,\n          },\n          jwt: session.access_token,\n          xform: _userResponse,\n        })\n        if (userError) throw userError\n        session.user = data.user as User\n        await this._saveSession(session)\n        await this._notifyAllSubscribers('USER_UPDATED', session)\n        return { data: { user: session.user }, error: null }\n      })\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { user: null }, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Decodes a JWT (without performing any validation).\n   */\n  private _decodeJWT(jwt: string): {\n    exp?: number\n    aal?: AuthenticatorAssuranceLevels | null\n    amr?: AMREntry[] | null\n  } {\n    return decodeJWTPayload(jwt)\n  }\n\n  /**\n   * Sets the session data from the current session. If the current session is expired, setSession will take care of refreshing it to obtain a new session.\n   * If the refresh token or access token in the current session is invalid, an error will be thrown.\n   * @param currentSession The current session that minimally contains an access token and refresh token.\n   */\n  async setSession(currentSession: {\n    access_token: string\n    refresh_token: string\n  }): Promise<AuthResponse> {\n    await this.initializePromise\n\n    return await this._acquireLock(-1, async () => {\n      return await this._setSession(currentSession)\n    })\n  }\n\n  protected async _setSession(currentSession: {\n    access_token: string\n    refresh_token: string\n  }): Promise<AuthResponse> {\n    try {\n      if (!currentSession.access_token || !currentSession.refresh_token) {\n        throw new AuthSessionMissingError()\n      }\n\n      const timeNow = Date.now() / 1000\n      let expiresAt = timeNow\n      let hasExpired = true\n      let session: Session | null = null\n      const payload = decodeJWTPayload(currentSession.access_token)\n      if (payload.exp) {\n        expiresAt = payload.exp\n        hasExpired = expiresAt <= timeNow\n      }\n\n      if (hasExpired) {\n        const { session: refreshedSession, error } = await this._callRefreshToken(\n          currentSession.refresh_token\n        )\n        if (error) {\n          return { data: { user: null, session: null }, error: error }\n        }\n\n        if (!refreshedSession) {\n          return { data: { user: null, session: null }, error: null }\n        }\n        session = refreshedSession\n      } else {\n        const { data, error } = await this._getUser(currentSession.access_token)\n        if (error) {\n          throw error\n        }\n        session = {\n          access_token: currentSession.access_token,\n          refresh_token: currentSession.refresh_token,\n          user: data.user,\n          token_type: 'bearer',\n          expires_in: expiresAt - timeNow,\n          expires_at: expiresAt,\n        }\n        await this._saveSession(session)\n        await this._notifyAllSubscribers('SIGNED_IN', session)\n      }\n\n      return { data: { user: session.user, session }, error: null }\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { session: null, user: null }, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Returns a new session, regardless of expiry status.\n   * Takes in an optional current session. If not passed in, then refreshSession() will attempt to retrieve it from getSession().\n   * If the current session's refresh token is invalid, an error will be thrown.\n   * @param currentSession The current session. If passed in, it must contain a refresh token.\n   */\n  async refreshSession(currentSession?: { refresh_token: string }): Promise<AuthResponse> {\n    await this.initializePromise\n\n    return await this._acquireLock(-1, async () => {\n      return await this._refreshSession(currentSession)\n    })\n  }\n\n  protected async _refreshSession(currentSession?: {\n    refresh_token: string\n  }): Promise<AuthResponse> {\n    try {\n      return await this._useSession(async (result) => {\n        if (!currentSession) {\n          const { data, error } = result\n          if (error) {\n            throw error\n          }\n\n          currentSession = data.session ?? undefined\n        }\n\n        if (!currentSession?.refresh_token) {\n          throw new AuthSessionMissingError()\n        }\n\n        const { session, error } = await this._callRefreshToken(currentSession.refresh_token)\n        if (error) {\n          return { data: { user: null, session: null }, error: error }\n        }\n\n        if (!session) {\n          return { data: { user: null, session: null }, error: null }\n        }\n\n        return { data: { user: session.user, session }, error: null }\n      })\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { user: null, session: null }, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Gets the session data from a URL string\n   */\n  private async _getSessionFromURL(isPKCEFlow: boolean): Promise<\n    | {\n        data: { session: Session; redirectType: string | null }\n        error: null\n      }\n    | { data: { session: null; redirectType: null }; error: AuthError }\n  > {\n    try {\n      if (!isBrowser()) throw new AuthImplicitGrantRedirectError('No browser detected.')\n      if (this.flowType === 'implicit' && !this._isImplicitGrantFlow()) {\n        throw new AuthImplicitGrantRedirectError('Not a valid implicit grant flow url.')\n      } else if (this.flowType == 'pkce' && !isPKCEFlow) {\n        throw new AuthPKCEGrantCodeExchangeError('Not a valid PKCE flow url.')\n      }\n\n      const params = parseParametersFromURL(window.location.href)\n\n      if (isPKCEFlow) {\n        if (!params.code) throw new AuthPKCEGrantCodeExchangeError('No code detected.')\n        const { data, error } = await this._exchangeCodeForSession(params.code)\n        if (error) throw error\n\n        const url = new URL(window.location.href)\n        url.searchParams.delete('code')\n\n        window.history.replaceState(window.history.state, '', url.toString())\n\n        return { data: { session: data.session, redirectType: null }, error: null }\n      }\n\n      if (params.error || params.error_description || params.error_code) {\n        throw new AuthImplicitGrantRedirectError(\n          params.error_description || 'Error in URL with unspecified error_description',\n          {\n            error: params.error || 'unspecified_error',\n            code: params.error_code || 'unspecified_code',\n          }\n        )\n      }\n\n      const {\n        provider_token,\n        provider_refresh_token,\n        access_token,\n        refresh_token,\n        expires_in,\n        expires_at,\n        token_type,\n      } = params\n\n      if (!access_token || !expires_in || !refresh_token || !token_type) {\n        throw new AuthImplicitGrantRedirectError('No session defined in URL')\n      }\n\n      const timeNow = Math.round(Date.now() / 1000)\n      const expiresIn = parseInt(expires_in)\n      let expiresAt = timeNow + expiresIn\n\n      if (expires_at) {\n        expiresAt = parseInt(expires_at)\n      }\n\n      const actuallyExpiresIn = expiresAt - timeNow\n      if (actuallyExpiresIn * 1000 <= AUTO_REFRESH_TICK_DURATION) {\n        console.warn(\n          `@supabase/gotrue-js: Session as retrieved from URL expires in ${actuallyExpiresIn}s, should have been closer to ${expiresIn}s`\n        )\n      }\n\n      const issuedAt = expiresAt - expiresIn\n      if (timeNow - issuedAt >= 120) {\n        console.warn(\n          '@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale',\n          issuedAt,\n          expiresAt,\n          timeNow\n        )\n      } else if (timeNow - issuedAt < 0) {\n        console.warn(\n          '@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clock for skew',\n          issuedAt,\n          expiresAt,\n          timeNow\n        )\n      }\n\n      const { data, error } = await this._getUser(access_token)\n      if (error) throw error\n\n      const session: Session = {\n        provider_token,\n        provider_refresh_token,\n        access_token,\n        expires_in: expiresIn,\n        expires_at: expiresAt,\n        refresh_token,\n        token_type,\n        user: data.user,\n      }\n\n      // Remove tokens from URL\n      window.location.hash = ''\n      this._debug('#_getSessionFromURL()', 'clearing window.location.hash')\n\n      return { data: { session, redirectType: params.type }, error: null }\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { session: null, redirectType: null }, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Checks if the current URL contains parameters given by an implicit oauth grant flow (https://www.rfc-editor.org/rfc/rfc6749.html#section-4.2)\n   */\n  private _isImplicitGrantFlow(): boolean {\n    const params = parseParametersFromURL(window.location.href)\n\n    return !!(isBrowser() && (params.access_token || params.error_description))\n  }\n\n  /**\n   * Checks if the current URL and backing storage contain parameters given by a PKCE flow\n   */\n  private async _isPKCEFlow(): Promise<boolean> {\n    const params = parseParametersFromURL(window.location.href)\n\n    const currentStorageContent = await getItemAsync(\n      this.storage,\n      `${this.storageKey}-code-verifier`\n    )\n\n    return !!(params.code && currentStorageContent)\n  }\n\n  /**\n   * Inside a browser context, `signOut()` will remove the logged in user from the browser session and log them out - removing all items from localstorage and then trigger a `\"SIGNED_OUT\"` event.\n   *\n   * For server-side management, you can revoke all refresh tokens for a user by passing a user's JWT through to `auth.api.signOut(JWT: string)`.\n   * There is no way to revoke a user's access token jwt until it expires. It is recommended to set a shorter expiry on the jwt for this reason.\n   *\n   * If using `others` scope, no `SIGNED_OUT` event is fired!\n   */\n  async signOut(options: SignOut = { scope: 'global' }): Promise<{ error: AuthError | null }> {\n    await this.initializePromise\n\n    return await this._acquireLock(-1, async () => {\n      return await this._signOut(options)\n    })\n  }\n\n  protected async _signOut(\n    { scope }: SignOut = { scope: 'global' }\n  ): Promise<{ error: AuthError | null }> {\n    return await this._useSession(async (result) => {\n      const { data, error: sessionError } = result\n      if (sessionError) {\n        return { error: sessionError }\n      }\n      const accessToken = data.session?.access_token\n      if (accessToken) {\n        const { error } = await this.admin.signOut(accessToken, scope)\n        if (error) {\n          // ignore 404s since user might not exist anymore\n          // ignore 401s since an invalid or expired JWT should sign out the current session\n          if (\n            !(\n              isAuthApiError(error) &&\n              (error.status === 404 || error.status === 401 || error.status === 403)\n            )\n          ) {\n            return { error }\n          }\n        }\n      }\n      if (scope !== 'others') {\n        await this._removeSession()\n        await removeItemAsync(this.storage, `${this.storageKey}-code-verifier`)\n        await this._notifyAllSubscribers('SIGNED_OUT', null)\n      }\n      return { error: null }\n    })\n  }\n\n  /**\n   * Receive a notification every time an auth event happens.\n   * @param callback A callback function to be invoked when an auth event happens.\n   */\n  onAuthStateChange(\n    callback: (event: AuthChangeEvent, session: Session | null) => void | Promise<void>\n  ): {\n    data: { subscription: Subscription }\n  } {\n    const id: string = uuid()\n    const subscription: Subscription = {\n      id,\n      callback,\n      unsubscribe: () => {\n        this._debug('#unsubscribe()', 'state change callback with id removed', id)\n\n        this.stateChangeEmitters.delete(id)\n      },\n    }\n\n    this._debug('#onAuthStateChange()', 'registered callback with id', id)\n\n    this.stateChangeEmitters.set(id, subscription)\n    ;(async () => {\n      await this.initializePromise\n\n      await this._acquireLock(-1, async () => {\n        this._emitInitialSession(id)\n      })\n    })()\n\n    return { data: { subscription } }\n  }\n\n  private async _emitInitialSession(id: string): Promise<void> {\n    return await this._useSession(async (result) => {\n      try {\n        const {\n          data: { session },\n          error,\n        } = result\n        if (error) throw error\n\n        await this.stateChangeEmitters.get(id)?.callback('INITIAL_SESSION', session)\n        this._debug('INITIAL_SESSION', 'callback id', id, 'session', session)\n      } catch (err) {\n        await this.stateChangeEmitters.get(id)?.callback('INITIAL_SESSION', null)\n        this._debug('INITIAL_SESSION', 'callback id', id, 'error', err)\n        console.error(err)\n      }\n    })\n  }\n\n  /**\n   * Sends a password reset request to an email address. This method supports the PKCE flow.\n   *\n   * @param email The email address of the user.\n   * @param options.redirectTo The URL to send the user to after they click the password reset link.\n   * @param options.captchaToken Verification token received when the user completes the captcha on the site.\n   */\n  async resetPasswordForEmail(\n    email: string,\n    options: {\n      redirectTo?: string\n      captchaToken?: string\n    } = {}\n  ): Promise<\n    | {\n        data: {}\n        error: null\n      }\n    | { data: null; error: AuthError }\n  > {\n    let codeChallenge: string | null = null\n    let codeChallengeMethod: string | null = null\n\n    if (this.flowType === 'pkce') {\n      ;[codeChallenge, codeChallengeMethod] = await getCodeChallengeAndMethod(\n        this.storage,\n        this.storageKey,\n        true // isPasswordRecovery\n      )\n    }\n    try {\n      return await _request(this.fetch, 'POST', `${this.url}/recover`, {\n        body: {\n          email,\n          code_challenge: codeChallenge,\n          code_challenge_method: codeChallengeMethod,\n          gotrue_meta_security: { captcha_token: options.captchaToken },\n        },\n        headers: this.headers,\n        redirectTo: options.redirectTo,\n      })\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: null, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Gets all the identities linked to a user.\n   */\n  async getUserIdentities(): Promise<\n    | {\n        data: {\n          identities: UserIdentity[]\n        }\n        error: null\n      }\n    | { data: null; error: AuthError }\n  > {\n    try {\n      const { data, error } = await this.getUser()\n      if (error) throw error\n      return { data: { identities: data.user.identities ?? [] }, error: null }\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: null, error }\n      }\n      throw error\n    }\n  }\n  /**\n   * Links an oauth identity to an existing user.\n   * This method supports the PKCE flow.\n   */\n  async linkIdentity(credentials: SignInWithOAuthCredentials): Promise<OAuthResponse> {\n    try {\n      const { data, error } = await this._useSession(async (result) => {\n        const { data, error } = result\n        if (error) throw error\n        const url: string = await this._getUrlForProvider(\n          `${this.url}/user/identities/authorize`,\n          credentials.provider,\n          {\n            redirectTo: credentials.options?.redirectTo,\n            scopes: credentials.options?.scopes,\n            queryParams: credentials.options?.queryParams,\n            skipBrowserRedirect: true,\n          }\n        )\n        return await _request(this.fetch, 'GET', url, {\n          headers: this.headers,\n          jwt: data.session?.access_token ?? undefined,\n        })\n      })\n      if (error) throw error\n      if (isBrowser() && !credentials.options?.skipBrowserRedirect) {\n        window.location.assign(data?.url)\n      }\n      return { data: { provider: credentials.provider, url: data?.url }, error: null }\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: { provider: credentials.provider, url: null }, error }\n      }\n      throw error\n    }\n  }\n\n  /**\n   * Unlinks an identity from a user by deleting it. The user will no longer be able to sign in with that identity once it's unlinked.\n   */\n  async unlinkIdentity(identity: UserIdentity): Promise<\n    | {\n        data: {}\n        error: null\n      }\n    | { data: null; error: AuthError }\n  > {\n    try {\n      return await this._useSession(async (result) => {\n        const { data, error } = result\n        if (error) {\n          throw error\n        }\n        return await _request(\n          this.fetch,\n          'DELETE',\n          `${this.url}/user/identities/${identity.identity_id}`,\n          {\n            headers: this.headers,\n            jwt: data.session?.access_token ?? undefined,\n          }\n        )\n      })\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: null, error }\n      }\n      throw error\n    }\n  }\n\n  /**\n   * Generates a new JWT.\n   * @param refreshToken A valid refresh token that was returned on login.\n   */\n  private async _refreshAccessToken(refreshToken: string): Promise<AuthResponse> {\n    const debugName = `#_refreshAccessToken(${refreshToken.substring(0, 5)}...)`\n    this._debug(debugName, 'begin')\n\n    try {\n      const startedAt = Date.now()\n\n      // will attempt to refresh the token with exponential backoff\n      return await retryable(\n        async (attempt) => {\n          if (attempt > 0) {\n            await sleep(200 * Math.pow(2, attempt - 1)) // 200, 400, 800, ...\n          }\n\n          this._debug(debugName, 'refreshing attempt', attempt)\n\n          return await _request(this.fetch, 'POST', `${this.url}/token?grant_type=refresh_token`, {\n            body: { refresh_token: refreshToken },\n            headers: this.headers,\n            xform: _sessionResponse,\n          })\n        },\n        (attempt, error) => {\n          const nextBackOffInterval = 200 * Math.pow(2, attempt)\n          return (\n            error &&\n            isAuthRetryableFetchError(error) &&\n            // retryable only if the request can be sent before the backoff overflows the tick duration\n            Date.now() + nextBackOffInterval - startedAt < AUTO_REFRESH_TICK_DURATION\n          )\n        }\n      )\n    } catch (error) {\n      this._debug(debugName, 'error', error)\n\n      if (isAuthError(error)) {\n        return { data: { session: null, user: null }, error }\n      }\n      throw error\n    } finally {\n      this._debug(debugName, 'end')\n    }\n  }\n\n  private _isValidSession(maybeSession: unknown): maybeSession is Session {\n    const isValidSession =\n      typeof maybeSession === 'object' &&\n      maybeSession !== null &&\n      'access_token' in maybeSession &&\n      'refresh_token' in maybeSession &&\n      'expires_at' in maybeSession\n\n    return isValidSession\n  }\n\n  private async _handleProviderSignIn(\n    provider: Provider,\n    options: {\n      redirectTo?: string\n      scopes?: string\n      queryParams?: { [key: string]: string }\n      skipBrowserRedirect?: boolean\n    }\n  ) {\n    const url: string = await this._getUrlForProvider(`${this.url}/authorize`, provider, {\n      redirectTo: options.redirectTo,\n      scopes: options.scopes,\n      queryParams: options.queryParams,\n    })\n\n    this._debug('#_handleProviderSignIn()', 'provider', provider, 'options', options, 'url', url)\n\n    // try to open on the browser\n    if (isBrowser() && !options.skipBrowserRedirect) {\n      window.location.assign(url)\n    }\n\n    return { data: { provider, url }, error: null }\n  }\n\n  /**\n   * Recovers the session from LocalStorage and refreshes\n   * Note: this method is async to accommodate for AsyncStorage e.g. in React native.\n   */\n  private async _recoverAndRefresh() {\n    const debugName = '#_recoverAndRefresh()'\n    this._debug(debugName, 'begin')\n\n    try {\n      const currentSession = await getItemAsync(this.storage, this.storageKey)\n      this._debug(debugName, 'session from storage', currentSession)\n\n      if (!this._isValidSession(currentSession)) {\n        this._debug(debugName, 'session is not valid')\n        if (currentSession !== null) {\n          await this._removeSession()\n        }\n\n        return\n      }\n\n      const timeNow = Math.round(Date.now() / 1000)\n      const expiresWithMargin = (currentSession.expires_at ?? Infinity) < timeNow + EXPIRY_MARGIN\n\n      this._debug(\n        debugName,\n        `session has${expiresWithMargin ? '' : ' not'} expired with margin of ${EXPIRY_MARGIN}s`\n      )\n\n      if (expiresWithMargin) {\n        if (this.autoRefreshToken && currentSession.refresh_token) {\n          const { error } = await this._callRefreshToken(currentSession.refresh_token)\n\n          if (error) {\n            console.error(error)\n\n            if (!isAuthRetryableFetchError(error)) {\n              this._debug(\n                debugName,\n                'refresh failed with a non-retryable error, removing the session',\n                error\n              )\n              await this._removeSession()\n            }\n          }\n        }\n      } else {\n        // no need to persist currentSession again, as we just loaded it from\n        // local storage; persisting it again may overwrite a value saved by\n        // another client with access to the same local storage\n        await this._notifyAllSubscribers('SIGNED_IN', currentSession)\n      }\n    } catch (err) {\n      this._debug(debugName, 'error', err)\n\n      console.error(err)\n      return\n    } finally {\n      this._debug(debugName, 'end')\n    }\n  }\n\n  private async _callRefreshToken(refreshToken: string): Promise<CallRefreshTokenResult> {\n    if (!refreshToken) {\n      throw new AuthSessionMissingError()\n    }\n\n    // refreshing is already in progress\n    if (this.refreshingDeferred) {\n      return this.refreshingDeferred.promise\n    }\n\n    const debugName = `#_callRefreshToken(${refreshToken.substring(0, 5)}...)`\n\n    this._debug(debugName, 'begin')\n\n    try {\n      this.refreshingDeferred = new Deferred<CallRefreshTokenResult>()\n\n      const { data, error } = await this._refreshAccessToken(refreshToken)\n      if (error) throw error\n      if (!data.session) throw new AuthSessionMissingError()\n\n      await this._saveSession(data.session)\n      await this._notifyAllSubscribers('TOKEN_REFRESHED', data.session)\n\n      const result = { session: data.session, error: null }\n\n      this.refreshingDeferred.resolve(result)\n\n      return result\n    } catch (error) {\n      this._debug(debugName, 'error', error)\n\n      if (isAuthError(error)) {\n        const result = { session: null, error }\n\n        if (!isAuthRetryableFetchError(error)) {\n          await this._removeSession()\n          await this._notifyAllSubscribers('SIGNED_OUT', null)\n        }\n\n        this.refreshingDeferred?.resolve(result)\n\n        return result\n      }\n\n      this.refreshingDeferred?.reject(error)\n      throw error\n    } finally {\n      this.refreshingDeferred = null\n      this._debug(debugName, 'end')\n    }\n  }\n\n  private async _notifyAllSubscribers(\n    event: AuthChangeEvent,\n    session: Session | null,\n    broadcast = true\n  ) {\n    const debugName = `#_notifyAllSubscribers(${event})`\n    this._debug(debugName, 'begin', session, `broadcast = ${broadcast}`)\n\n    try {\n      if (this.broadcastChannel && broadcast) {\n        this.broadcastChannel.postMessage({ event, session })\n      }\n\n      const errors: any[] = []\n      const promises = Array.from(this.stateChangeEmitters.values()).map(async (x) => {\n        try {\n          await x.callback(event, session)\n        } catch (e: any) {\n          errors.push(e)\n        }\n      })\n\n      await Promise.all(promises)\n\n      if (errors.length > 0) {\n        for (let i = 0; i < errors.length; i += 1) {\n          console.error(errors[i])\n        }\n\n        throw errors[0]\n      }\n    } finally {\n      this._debug(debugName, 'end')\n    }\n  }\n\n  /**\n   * set currentSession and currentUser\n   * process to _startAutoRefreshToken if possible\n   */\n  private async _saveSession(session: Session) {\n    this._debug('#_saveSession()', session)\n    // _saveSession is always called whenever a new session has been acquired\n    // so we can safely suppress the warning returned by future getSession calls\n    this.suppressGetSessionWarning = true\n    await setItemAsync(this.storage, this.storageKey, session)\n  }\n\n  private async _removeSession() {\n    this._debug('#_removeSession()')\n\n    await removeItemAsync(this.storage, this.storageKey)\n  }\n\n  /**\n   * Removes any registered visibilitychange callback.\n   *\n   * {@see #startAutoRefresh}\n   * {@see #stopAutoRefresh}\n   */\n  private _removeVisibilityChangedCallback() {\n    this._debug('#_removeVisibilityChangedCallback()')\n\n    const callback = this.visibilityChangedCallback\n    this.visibilityChangedCallback = null\n\n    try {\n      if (callback && isBrowser() && window?.removeEventListener) {\n        window.removeEventListener('visibilitychange', callback)\n      }\n    } catch (e) {\n      console.error('removing visibilitychange callback failed', e)\n    }\n  }\n\n  /**\n   * This is the private implementation of {@link #startAutoRefresh}. Use this\n   * within the library.\n   */\n  private async _startAutoRefresh() {\n    await this._stopAutoRefresh()\n\n    this._debug('#_startAutoRefresh()')\n\n    const ticker = setInterval(() => this._autoRefreshTokenTick(), AUTO_REFRESH_TICK_DURATION)\n    this.autoRefreshTicker = ticker\n\n    if (ticker && typeof ticker === 'object' && typeof ticker.unref === 'function') {\n      // ticker is a NodeJS Timeout object that has an `unref` method\n      // https://nodejs.org/api/timers.html#timeoutunref\n      // When auto refresh is used in NodeJS (like for testing) the\n      // `setInterval` is preventing the process from being marked as\n      // finished and tests run endlessly. This can be prevented by calling\n      // `unref()` on the returned object.\n      ticker.unref()\n      // @ts-ignore\n    } else if (typeof Deno !== 'undefined' && typeof Deno.unrefTimer === 'function') {\n      // similar like for NodeJS, but with the Deno API\n      // https://deno.land/api@latest?unstable&s=Deno.unrefTimer\n      // @ts-ignore\n      Deno.unrefTimer(ticker)\n    }\n\n    // run the tick immediately, but in the next pass of the event loop so that\n    // #_initialize can be allowed to complete without recursively waiting on\n    // itself\n    setTimeout(async () => {\n      await this.initializePromise\n      await this._autoRefreshTokenTick()\n    }, 0)\n  }\n\n  /**\n   * This is the private implementation of {@link #stopAutoRefresh}. Use this\n   * within the library.\n   */\n  private async _stopAutoRefresh() {\n    this._debug('#_stopAutoRefresh()')\n\n    const ticker = this.autoRefreshTicker\n    this.autoRefreshTicker = null\n\n    if (ticker) {\n      clearInterval(ticker)\n    }\n  }\n\n  /**\n   * Starts an auto-refresh process in the background. The session is checked\n   * every few seconds. Close to the time of expiration a process is started to\n   * refresh the session. If refreshing fails it will be retried for as long as\n   * necessary.\n   *\n   * If you set the {@link GoTrueClientOptions#autoRefreshToken} you don't need\n   * to call this function, it will be called for you.\n   *\n   * On browsers the refresh process works only when the tab/window is in the\n   * foreground to conserve resources as well as prevent race conditions and\n   * flooding auth with requests. If you call this method any managed\n   * visibility change callback will be removed and you must manage visibility\n   * changes on your own.\n   *\n   * On non-browser platforms the refresh process works *continuously* in the\n   * background, which may not be desirable. You should hook into your\n   * platform's foreground indication mechanism and call these methods\n   * appropriately to conserve resources.\n   *\n   * {@see #stopAutoRefresh}\n   */\n  async startAutoRefresh() {\n    this._removeVisibilityChangedCallback()\n    await this._startAutoRefresh()\n  }\n\n  /**\n   * Stops an active auto refresh process running in the background (if any).\n   *\n   * If you call this method any managed visibility change callback will be\n   * removed and you must manage visibility changes on your own.\n   *\n   * See {@link #startAutoRefresh} for more details.\n   */\n  async stopAutoRefresh() {\n    this._removeVisibilityChangedCallback()\n    await this._stopAutoRefresh()\n  }\n\n  /**\n   * Runs the auto refresh token tick.\n   */\n  private async _autoRefreshTokenTick() {\n    this._debug('#_autoRefreshTokenTick()', 'begin')\n\n    try {\n      await this._acquireLock(0, async () => {\n        try {\n          const now = Date.now()\n\n          try {\n            return await this._useSession(async (result) => {\n              const {\n                data: { session },\n              } = result\n\n              if (!session || !session.refresh_token || !session.expires_at) {\n                this._debug('#_autoRefreshTokenTick()', 'no session')\n                return\n              }\n\n              // session will expire in this many ticks (or has already expired if <= 0)\n              const expiresInTicks = Math.floor(\n                (session.expires_at * 1000 - now) / AUTO_REFRESH_TICK_DURATION\n              )\n\n              this._debug(\n                '#_autoRefreshTokenTick()',\n                `access token expires in ${expiresInTicks} ticks, a tick lasts ${AUTO_REFRESH_TICK_DURATION}ms, refresh threshold is ${AUTO_REFRESH_TICK_THRESHOLD} ticks`\n              )\n\n              if (expiresInTicks <= AUTO_REFRESH_TICK_THRESHOLD) {\n                await this._callRefreshToken(session.refresh_token)\n              }\n            })\n          } catch (e: any) {\n            console.error(\n              'Auto refresh tick failed with error. This is likely a transient error.',\n              e\n            )\n          }\n        } finally {\n          this._debug('#_autoRefreshTokenTick()', 'end')\n        }\n      })\n    } catch (e: any) {\n      if (e.isAcquireTimeout || e instanceof LockAcquireTimeoutError) {\n        this._debug('auto refresh token tick lock not available')\n      } else {\n        throw e\n      }\n    }\n  }\n\n  /**\n   * Registers callbacks on the browser / platform, which in-turn run\n   * algorithms when the browser window/tab are in foreground. On non-browser\n   * platforms it assumes always foreground.\n   */\n  private async _handleVisibilityChange() {\n    this._debug('#_handleVisibilityChange()')\n\n    if (!isBrowser() || !window?.addEventListener) {\n      if (this.autoRefreshToken) {\n        // in non-browser environments the refresh token ticker runs always\n        this.startAutoRefresh()\n      }\n\n      return false\n    }\n\n    try {\n      this.visibilityChangedCallback = async () => await this._onVisibilityChanged(false)\n\n      window?.addEventListener('visibilitychange', this.visibilityChangedCallback)\n\n      // now immediately call the visbility changed callback to setup with the\n      // current visbility state\n      await this._onVisibilityChanged(true) // initial call\n    } catch (error) {\n      console.error('_handleVisibilityChange', error)\n    }\n  }\n\n  /**\n   * Callback registered with `window.addEventListener('visibilitychange')`.\n   */\n  private async _onVisibilityChanged(calledFromInitialize: boolean) {\n    const methodName = `#_onVisibilityChanged(${calledFromInitialize})`\n    this._debug(methodName, 'visibilityState', document.visibilityState)\n\n    if (document.visibilityState === 'visible') {\n      if (this.autoRefreshToken) {\n        // in browser environments the refresh token ticker runs only on focused tabs\n        // which prevents race conditions\n        this._startAutoRefresh()\n      }\n\n      if (!calledFromInitialize) {\n        // called when the visibility has changed, i.e. the browser\n        // transitioned from hidden -> visible so we need to see if the session\n        // should be recovered immediately... but to do that we need to acquire\n        // the lock first asynchronously\n        await this.initializePromise\n\n        await this._acquireLock(-1, async () => {\n          if (document.visibilityState !== 'visible') {\n            this._debug(\n              methodName,\n              'acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting'\n            )\n\n            // visibility has changed while waiting for the lock, abort\n            return\n          }\n\n          // recover the session\n          await this._recoverAndRefresh()\n        })\n      }\n    } else if (document.visibilityState === 'hidden') {\n      if (this.autoRefreshToken) {\n        this._stopAutoRefresh()\n      }\n    }\n  }\n\n  /**\n   * Generates the relevant login URL for a third-party provider.\n   * @param options.redirectTo A URL or mobile address to send the user to after they are confirmed.\n   * @param options.scopes A space-separated list of scopes granted to the OAuth application.\n   * @param options.queryParams An object of key-value pairs containing query parameters granted to the OAuth application.\n   */\n  private async _getUrlForProvider(\n    url: string,\n    provider: Provider,\n    options: {\n      redirectTo?: string\n      scopes?: string\n      queryParams?: { [key: string]: string }\n      skipBrowserRedirect?: boolean\n    }\n  ) {\n    const urlParams: string[] = [`provider=${encodeURIComponent(provider)}`]\n    if (options?.redirectTo) {\n      urlParams.push(`redirect_to=${encodeURIComponent(options.redirectTo)}`)\n    }\n    if (options?.scopes) {\n      urlParams.push(`scopes=${encodeURIComponent(options.scopes)}`)\n    }\n    if (this.flowType === 'pkce') {\n      const [codeChallenge, codeChallengeMethod] = await getCodeChallengeAndMethod(\n        this.storage,\n        this.storageKey\n      )\n\n      const flowParams = new URLSearchParams({\n        code_challenge: `${encodeURIComponent(codeChallenge)}`,\n        code_challenge_method: `${encodeURIComponent(codeChallengeMethod)}`,\n      })\n      urlParams.push(flowParams.toString())\n    }\n    if (options?.queryParams) {\n      const query = new URLSearchParams(options.queryParams)\n      urlParams.push(query.toString())\n    }\n    if (options?.skipBrowserRedirect) {\n      urlParams.push(`skip_http_redirect=${options.skipBrowserRedirect}`)\n    }\n\n    return `${url}?${urlParams.join('&')}`\n  }\n\n  private async _unenroll(params: MFAUnenrollParams): Promise<AuthMFAUnenrollResponse> {\n    try {\n      return await this._useSession(async (result) => {\n        const { data: sessionData, error: sessionError } = result\n        if (sessionError) {\n          return { data: null, error: sessionError }\n        }\n\n        return await _request(this.fetch, 'DELETE', `${this.url}/factors/${params.factorId}`, {\n          headers: this.headers,\n          jwt: sessionData?.session?.access_token,\n        })\n      })\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: null, error }\n      }\n      throw error\n    }\n  }\n\n  /**\n   * {@see GoTrueMFAApi#enroll}\n   */\n  private async _enroll(params: MFAEnrollParams): Promise<AuthMFAEnrollResponse> {\n    try {\n      return await this._useSession(async (result) => {\n        const { data: sessionData, error: sessionError } = result\n        if (sessionError) {\n          return { data: null, error: sessionError }\n        }\n\n        const body = {\n          friendly_name: params.friendlyName,\n          factor_type: params.factorType,\n          ...(params.factorType === 'phone' ? { phone: params.phone } : { issuer: params.issuer }),\n        }\n\n        const { data, error } = await _request(this.fetch, 'POST', `${this.url}/factors`, {\n          body,\n          headers: this.headers,\n          jwt: sessionData?.session?.access_token,\n        })\n\n        if (error) {\n          return { data: null, error }\n        }\n\n        // TODO: Remove once: https://github.com/supabase/auth/pull/1717 is deployed\n        if (params.factorType === 'phone') {\n          delete data.totp\n        }\n\n        if (params.factorType === 'totp' && data?.totp?.qr_code) {\n          data.totp.qr_code = `data:image/svg+xml;utf-8,${data.totp.qr_code}`\n        }\n\n        return { data, error: null }\n      })\n    } catch (error) {\n      if (isAuthError(error)) {\n        return { data: null, error }\n      }\n      throw error\n    }\n  }\n\n  /**\n   * {@see GoTrueMFAApi#verify}\n   */\n  private async _verify(params: MFAVerifyParams): Promise<AuthMFAVerifyResponse> {\n    return this._acquireLock(-1, async () => {\n      try {\n        return await this._useSession(async (result) => {\n          const { data: sessionData, error: sessionError } = result\n          if (sessionError) {\n            return { data: null, error: sessionError }\n          }\n\n          const { data, error } = await _request(\n            this.fetch,\n            'POST',\n            `${this.url}/factors/${params.factorId}/verify`,\n            {\n              body: { code: params.code, challenge_id: params.challengeId },\n              headers: this.headers,\n              jwt: sessionData?.session?.access_token,\n            }\n          )\n          if (error) {\n            return { data: null, error }\n          }\n\n          await this._saveSession({\n            expires_at: Math.round(Date.now() / 1000) + data.expires_in,\n            ...data,\n          })\n          await this._notifyAllSubscribers('MFA_CHALLENGE_VERIFIED', data)\n\n          return { data, error }\n        })\n      } catch (error) {\n        if (isAuthError(error)) {\n          return { data: null, error }\n        }\n        throw error\n      }\n    })\n  }\n\n  /**\n   * {@see GoTrueMFAApi#challenge}\n   */\n  private async _challenge(params: MFAChallengeParams): Promise<AuthMFAChallengeResponse> {\n    return this._acquireLock(-1, async () => {\n      try {\n        return await this._useSession(async (result) => {\n          const { data: sessionData, error: sessionError } = result\n          if (sessionError) {\n            return { data: null, error: sessionError }\n          }\n\n          return await _request(\n            this.fetch,\n            'POST',\n            `${this.url}/factors/${params.factorId}/challenge`,\n            {\n              body: { channel: params.channel },\n              headers: this.headers,\n              jwt: sessionData?.session?.access_token,\n            }\n          )\n        })\n      } catch (error) {\n        if (isAuthError(error)) {\n          return { data: null, error }\n        }\n        throw error\n      }\n    })\n  }\n\n  /**\n   * {@see GoTrueMFAApi#challengeAndVerify}\n   */\n  private async _challengeAndVerify(\n    params: MFAChallengeAndVerifyParams\n  ): Promise<AuthMFAVerifyResponse> {\n    // both _challenge and _verify independently acquire the lock, so no need\n    // to acquire it here\n\n    const { data: challengeData, error: challengeError } = await this._challenge({\n      factorId: params.factorId,\n    })\n    if (challengeError) {\n      return { data: null, error: challengeError }\n    }\n\n    return await this._verify({\n      factorId: params.factorId,\n      challengeId: challengeData.id,\n      code: params.code,\n    })\n  }\n\n  /**\n   * {@see GoTrueMFAApi#listFactors}\n   */\n  private async _listFactors(): Promise<AuthMFAListFactorsResponse> {\n    // use #getUser instead of #_getUser as the former acquires a lock\n    const {\n      data: { user },\n      error: userError,\n    } = await this.getUser()\n    if (userError) {\n      return { data: null, error: userError }\n    }\n\n    const factors = user?.factors || []\n    const totp = factors.filter(\n      (factor) => factor.factor_type === 'totp' && factor.status === 'verified'\n    )\n    const phone = factors.filter(\n      (factor) => factor.factor_type === 'phone' && factor.status === 'verified'\n    )\n\n    return {\n      data: {\n        all: factors,\n        totp,\n        phone,\n      },\n      error: null,\n    }\n  }\n\n  /**\n   * {@see GoTrueMFAApi#getAuthenticatorAssuranceLevel}\n   */\n  private async _getAuthenticatorAssuranceLevel(): Promise<AuthMFAGetAuthenticatorAssuranceLevelResponse> {\n    return this._acquireLock(-1, async () => {\n      return await this._useSession(async (result) => {\n        const {\n          data: { session },\n          error: sessionError,\n        } = result\n        if (sessionError) {\n          return { data: null, error: sessionError }\n        }\n        if (!session) {\n          return {\n            data: { currentLevel: null, nextLevel: null, currentAuthenticationMethods: [] },\n            error: null,\n          }\n        }\n\n        const payload = this._decodeJWT(session.access_token)\n\n        let currentLevel: AuthenticatorAssuranceLevels | null = null\n\n        if (payload.aal) {\n          currentLevel = payload.aal\n        }\n\n        let nextLevel: AuthenticatorAssuranceLevels | null = currentLevel\n\n        const verifiedFactors =\n          session.user.factors?.filter((factor: Factor) => factor.status === 'verified') ?? []\n\n        if (verifiedFactors.length > 0) {\n          nextLevel = 'aal2'\n        }\n\n        const currentAuthenticationMethods = payload.amr || []\n\n        return { data: { currentLevel, nextLevel, currentAuthenticationMethods }, error: null }\n      })\n    })\n  }\n}\n", "import GoTrueAdminApi from './GoTrueAdminApi'\n\nconst AuthAdminApi = GoTrueAdminApi\n\nexport default AuthAdminApi\n", "import GoTrueClient from './GoTrueClient'\n\nconst AuthClient = GoTrueClient\n\nexport default AuthClient\n", "import { AuthClient } from '@supabase/auth-js'\nimport { SupabaseAuthClientOptions } from './types'\n\nexport class SupabaseAuthClient extends AuthClient {\n  constructor(options: SupabaseAuthClientOptions) {\n    super(options)\n  }\n}\n", "import { FunctionsClient } from '@supabase/functions-js'\nimport { AuthChangeEvent } from '@supabase/auth-js'\nimport {\n  PostgrestClient,\n  PostgrestFilterBuilder,\n  PostgrestQueryBuilder,\n} from '@supabase/postgrest-js'\nimport {\n  RealtimeChannel,\n  RealtimeChannelOptions,\n  RealtimeClient,\n  RealtimeClientOptions,\n} from '@supabase/realtime-js'\nimport { StorageClient as SupabaseStorageClient } from '@supabase/storage-js'\nimport {\n  DEFAULT_GLOBAL_OPTIONS,\n  DEFAULT_DB_OPTIONS,\n  DEFAULT_AUTH_OPTIONS,\n  DEFAULT_REALTIME_OPTIONS,\n} from './lib/constants'\nimport { fetchWithAuth } from './lib/fetch'\nimport { stripTrailingSlash, applySettingDefaults } from './lib/helpers'\nimport { SupabaseAuthClient } from './lib/SupabaseAuthClient'\nimport { Fetch, GenericSchema, SupabaseClientOptions, SupabaseAuthClientOptions } from './lib/types'\n\n/**\n * Supabase Client.\n *\n * An isomorphic Javascript client for interacting with Postgres.\n */\nexport default class SupabaseClient<\n  Database = any,\n  SchemaName extends string & keyof Database = 'public' extends keyof Database\n    ? 'public'\n    : string & keyof Database,\n  Schema extends GenericSchema = Database[SchemaName] extends GenericSchema\n    ? Database[SchemaName]\n    : any\n> {\n  /**\n   * Supabase Auth allows you to create and manage user sessions for access to data that is secured by access policies.\n   */\n  auth: SupabaseAuthClient\n  realtime: RealtimeClient\n\n  protected realtimeUrl: string\n  protected authUrl: string\n  protected storageUrl: string\n  protected functionsUrl: string\n  protected rest: PostgrestClient<Database, SchemaName>\n  protected storageKey: string\n  protected fetch?: Fetch\n  protected changedAccessToken?: string\n  protected accessToken?: () => Promise<string>\n\n  protected headers: Record<string, string>\n\n  /**\n   * Create a new client for use in the browser.\n   * @param supabaseUrl The unique Supabase URL which is supplied when you create a new project in your project dashboard.\n   * @param supabaseKey The unique Supabase Key which is supplied when you create a new project in your project dashboard.\n   * @param options.db.schema You can switch in between schemas. The schema needs to be on the list of exposed schemas inside Supabase.\n   * @param options.auth.autoRefreshToken Set to \"true\" if you want to automatically refresh the token before expiring.\n   * @param options.auth.persistSession Set to \"true\" if you want to automatically save the user session into local storage.\n   * @param options.auth.detectSessionInUrl Set to \"true\" if you want to automatically detects OAuth grants in the URL and signs in the user.\n   * @param options.realtime Options passed along to realtime-js constructor.\n   * @param options.global.fetch A custom fetch implementation.\n   * @param options.global.headers Any additional headers to send with each network request.\n   */\n  constructor(\n    protected supabaseUrl: string,\n    protected supabaseKey: string,\n    options?: SupabaseClientOptions<SchemaName>\n  ) {\n    if (!supabaseUrl) throw new Error('supabaseUrl is required.')\n    if (!supabaseKey) throw new Error('supabaseKey is required.')\n\n    const _supabaseUrl = stripTrailingSlash(supabaseUrl)\n\n    this.realtimeUrl = `${_supabaseUrl}/realtime/v1`.replace(/^http/i, 'ws')\n    this.authUrl = `${_supabaseUrl}/auth/v1`\n    this.storageUrl = `${_supabaseUrl}/storage/v1`\n    this.functionsUrl = `${_supabaseUrl}/functions/v1`\n\n    // default storage key uses the supabase project ref as a namespace\n    const defaultStorageKey = `sb-${new URL(this.authUrl).hostname.split('.')[0]}-auth-token`\n    const DEFAULTS = {\n      db: DEFAULT_DB_OPTIONS,\n      realtime: DEFAULT_REALTIME_OPTIONS,\n      auth: { ...DEFAULT_AUTH_OPTIONS, storageKey: defaultStorageKey },\n      global: DEFAULT_GLOBAL_OPTIONS,\n    }\n\n    const settings = applySettingDefaults(options ?? {}, DEFAULTS)\n\n    this.storageKey = settings.auth.storageKey ?? ''\n    this.headers = settings.global.headers ?? {}\n\n    if (!settings.accessToken) {\n      this.auth = this._initSupabaseAuthClient(\n        settings.auth ?? {},\n        this.headers,\n        settings.global.fetch\n      )\n    } else {\n      this.accessToken = settings.accessToken\n\n      this.auth = new Proxy<SupabaseAuthClient>({} as any, {\n        get: (_, prop) => {\n          throw new Error(\n            `@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(\n              prop\n            )} is not possible`\n          )\n        },\n      })\n    }\n\n    this.fetch = fetchWithAuth(supabaseKey, this._getAccessToken.bind(this), settings.global.fetch)\n\n    this.realtime = this._initRealtimeClient({ headers: this.headers, ...settings.realtime })\n    this.rest = new PostgrestClient(`${_supabaseUrl}/rest/v1`, {\n      headers: this.headers,\n      schema: settings.db.schema,\n      fetch: this.fetch,\n    })\n\n    if (!settings.accessToken) {\n      this._listenForAuthEvents()\n    }\n  }\n\n  /**\n   * Supabase Functions allows you to deploy and invoke edge functions.\n   */\n  get functions(): FunctionsClient {\n    return new FunctionsClient(this.functionsUrl, {\n      headers: this.headers,\n      customFetch: this.fetch,\n    })\n  }\n\n  /**\n   * Supabase Storage allows you to manage user-generated content, such as photos or videos.\n   */\n  get storage(): SupabaseStorageClient {\n    return new SupabaseStorageClient(this.storageUrl, this.headers, this.fetch)\n  }\n\n  // NOTE: signatures must be kept in sync with PostgrestClient.from\n  from<\n    TableName extends string & keyof Schema['Tables'],\n    Table extends Schema['Tables'][TableName]\n  >(relation: TableName): PostgrestQueryBuilder<Schema, Table, TableName>\n  from<ViewName extends string & keyof Schema['Views'], View extends Schema['Views'][ViewName]>(\n    relation: ViewName\n  ): PostgrestQueryBuilder<Schema, View, ViewName>\n  /**\n   * Perform a query on a table or a view.\n   *\n   * @param relation - The table or view name to query\n   */\n  from(relation: string): PostgrestQueryBuilder<Schema, any, any> {\n    return this.rest.from(relation)\n  }\n\n  // NOTE: signatures must be kept in sync with PostgrestClient.schema\n  /**\n   * Select a schema to query or perform an function (rpc) call.\n   *\n   * The schema needs to be on the list of exposed schemas inside Supabase.\n   *\n   * @param schema - The schema to query\n   */\n  schema<DynamicSchema extends string & keyof Database>(\n    schema: DynamicSchema\n  ): PostgrestClient<\n    Database,\n    DynamicSchema,\n    Database[DynamicSchema] extends GenericSchema ? Database[DynamicSchema] : any\n  > {\n    return this.rest.schema<DynamicSchema>(schema)\n  }\n\n  // NOTE: signatures must be kept in sync with PostgrestClient.rpc\n  /**\n   * Perform a function call.\n   *\n   * @param fn - The function name to call\n   * @param args - The arguments to pass to the function call\n   * @param options - Named parameters\n   * @param options.head - When set to `true`, `data` will not be returned.\n   * Useful if you only need the count.\n   * @param options.get - When set to `true`, the function will be called with\n   * read-only access mode.\n   * @param options.count - Count algorithm to use to count rows returned by the\n   * function. Only applicable for [set-returning\n   * functions](https://www.postgresql.org/docs/current/functions-srf.html).\n   *\n   * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n   * hood.\n   *\n   * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n   * statistics under the hood.\n   *\n   * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n   * numbers.\n   */\n  rpc<FnName extends string & keyof Schema['Functions'], Fn extends Schema['Functions'][FnName]>(\n    fn: FnName,\n    args: Fn['Args'] = {},\n    options: {\n      head?: boolean\n      get?: boolean\n      count?: 'exact' | 'planned' | 'estimated'\n    } = {}\n  ): PostgrestFilterBuilder<\n    Schema,\n    Fn['Returns'] extends any[]\n      ? Fn['Returns'][number] extends Record<string, unknown>\n        ? Fn['Returns'][number]\n        : never\n      : never,\n    Fn['Returns']\n  > {\n    return this.rest.rpc(fn, args, options)\n  }\n\n  /**\n   * Creates a Realtime channel with Broadcast, Presence, and Postgres Changes.\n   *\n   * @param {string} name - The name of the Realtime channel.\n   * @param {Object} opts - The options to pass to the Realtime channel.\n   *\n   */\n  channel(name: string, opts: RealtimeChannelOptions = { config: {} }): RealtimeChannel {\n    return this.realtime.channel(name, opts)\n  }\n\n  /**\n   * Returns all Realtime channels.\n   */\n  getChannels(): RealtimeChannel[] {\n    return this.realtime.getChannels()\n  }\n\n  /**\n   * Unsubscribes and removes Realtime channel from Realtime client.\n   *\n   * @param {RealtimeChannel} channel - The name of the Realtime channel.\n   *\n   */\n  removeChannel(channel: RealtimeChannel): Promise<'ok' | 'timed out' | 'error'> {\n    return this.realtime.removeChannel(channel)\n  }\n\n  /**\n   * Unsubscribes and removes all Realtime channels from Realtime client.\n   */\n  removeAllChannels(): Promise<('ok' | 'timed out' | 'error')[]> {\n    return this.realtime.removeAllChannels()\n  }\n\n  private async _getAccessToken() {\n    if (this.accessToken) {\n      return await this.accessToken()\n    }\n\n    const { data } = await this.auth.getSession()\n\n    return data.session?.access_token ?? null\n  }\n\n  private _initSupabaseAuthClient(\n    {\n      autoRefreshToken,\n      persistSession,\n      detectSessionInUrl,\n      storage,\n      storageKey,\n      flowType,\n      lock,\n      debug,\n    }: SupabaseAuthClientOptions,\n    headers?: Record<string, string>,\n    fetch?: Fetch\n  ) {\n    const authHeaders = {\n      Authorization: `Bearer ${this.supabaseKey}`,\n      apikey: `${this.supabaseKey}`,\n    }\n    return new SupabaseAuthClient({\n      url: this.authUrl,\n      headers: { ...authHeaders, ...headers },\n      storageKey: storageKey,\n      autoRefreshToken,\n      persistSession,\n      detectSessionInUrl,\n      storage,\n      flowType,\n      lock,\n      debug,\n      fetch,\n      // auth checks if there is a custom authorizaiton header using this flag\n      // so it knows whether to return an error when getUser is called with no session\n      hasCustomAuthorizationHeader: 'Authorization' in this.headers ?? false,\n    })\n  }\n\n  private _initRealtimeClient(options: RealtimeClientOptions) {\n    return new RealtimeClient(this.realtimeUrl, {\n      ...options,\n      params: { ...{ apikey: this.supabaseKey }, ...options?.params },\n    })\n  }\n\n  private _listenForAuthEvents() {\n    let data = this.auth.onAuthStateChange((event, session) => {\n      this._handleTokenChanged(event, 'CLIENT', session?.access_token)\n    })\n    return data\n  }\n\n  private _handleTokenChanged(\n    event: AuthChangeEvent,\n    source: 'CLIENT' | 'STORAGE',\n    token?: string\n  ) {\n    if (\n      (event === 'TOKEN_REFRESHED' || event === 'SIGNED_IN') &&\n      this.changedAccessToken !== token\n    ) {\n      // Token has changed\n      this.realtime.setAuth(token ?? null)\n\n      this.changedAccessToken = token\n    } else if (event === 'SIGNED_OUT') {\n      // Token is removed\n      this.realtime.setAuth(this.supabaseKey)\n      if (source == 'STORAGE') this.auth.signOut()\n      this.changedAccessToken = undefined\n    }\n  }\n}\n", "import SupabaseClient from './SupabaseClient'\nimport type { GenericSchema, SupabaseClientOptions } from './lib/types'\n\nexport * from '@supabase/auth-js'\nexport type { User as AuthUser, Session as AuthSession } from '@supabase/auth-js'\nexport type {\n  PostgrestResponse,\n  PostgrestSingleResponse,\n  PostgrestMaybeSingleResponse,\n  PostgrestError,\n} from '@supabase/postgrest-js'\nexport {\n  FunctionsHttpError,\n  FunctionsFetchError,\n  FunctionsRelayError,\n  FunctionsError,\n  type FunctionInvokeOptions,\n  FunctionRegion,\n} from '@supabase/functions-js'\nexport * from '@supabase/realtime-js'\nexport { default as SupabaseClient } from './SupabaseClient'\nexport type { SupabaseClientOptions, QueryResult, QueryData, QueryError } from './lib/types'\n\n/**\n * Creates a new Supabase Client.\n */\nexport const createClient = <\n  Database = any,\n  SchemaName extends string & keyof Database = 'public' extends keyof Database\n    ? 'public'\n    : string & keyof Database,\n  Schema extends GenericSchema = Database[SchemaName] extends GenericSchema\n    ? Database[SchemaName]\n    : any\n>(\n  supabaseUrl: string,\n  supabaseKey: string,\n  options?: SupabaseClientOptions<SchemaName>\n): SupabaseClient<Database, SchemaName, Schema> => {\n  return new SupabaseClient<Database, SchemaName, Schema>(supabaseUrl, supabaseKey, options)\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;AAEA,QAAqB,iBAArB,cAA4C,MAAK;MAK/C,YAAY,SAAwB;AAClC,cAAM,QAAQ,OAAO;AACrB,aAAK,OAAO;AACZ,aAAK,UAAU,QAAQ;AACvB,aAAK,OAAO,QAAQ;AACpB,aAAK,OAAO,QAAQ;MACtB;;AAXF,YAAA,UAAA;;;;;;;;;;;;ACDA,QAAA,eAAA,gBAAA,+CAAA;AAGA,QAAA,mBAAA,gBAAA,wBAAA;AAEA,QAA8BA,oBAA9B,MAA8C;MAa5C,YAAY,SAAiC;AALnC,aAAA,qBAAqB;AAM7B,aAAK,SAAS,QAAQ;AACtB,aAAK,MAAM,QAAQ;AACnB,aAAK,UAAU,QAAQ;AACvB,aAAK,SAAS,QAAQ;AACtB,aAAK,OAAO,QAAQ;AACpB,aAAK,qBAAqB,QAAQ;AAClC,aAAK,SAAS,QAAQ;AACtB,aAAK,gBAAgB,QAAQ;AAE7B,YAAI,QAAQ,OAAO;AACjB,eAAK,QAAQ,QAAQ;mBACZ,OAAO,UAAU,aAAa;AACvC,eAAK,QAAQ,aAAA;eACR;AACL,eAAK,QAAQ;;MAEjB;;;;;;;MAQA,eAAY;AACV,aAAK,qBAAqB;AAC1B,eAAO;MACT;;;;MAKA,UAAU,MAAc,OAAa;AACnC,aAAK,UAAO,OAAA,OAAA,CAAA,GAAQ,KAAK,OAAO;AAChC,aAAK,QAAQ,IAAI,IAAI;AACrB,eAAO;MACT;MAEA,KACE,aAIA,YAAmF;AAGnF,YAAI,KAAK,WAAW,QAAW;mBAEpB,CAAC,OAAO,MAAM,EAAE,SAAS,KAAK,MAAM,GAAG;AAChD,eAAK,QAAQ,gBAAgB,IAAI,KAAK;eACjC;AACL,eAAK,QAAQ,iBAAiB,IAAI,KAAK;;AAEzC,YAAI,KAAK,WAAW,SAAS,KAAK,WAAW,QAAQ;AACnD,eAAK,QAAQ,cAAc,IAAI;;AAKjC,cAAM,SAAS,KAAK;AACpB,YAAI,MAAM,OAAO,KAAK,IAAI,SAAQ,GAAI;UACpC,QAAQ,KAAK;UACb,SAAS,KAAK;UACd,MAAM,KAAK,UAAU,KAAK,IAAI;UAC9B,QAAQ,KAAK;SACd,EAAE,KAAK,OAAOC,SAAO;;AACpB,cAAI,QAAQ;AACZ,cAAI,OAAO;AACX,cAAI,QAAuB;AAC3B,cAAI,SAASA,KAAI;AACjB,cAAI,aAAaA,KAAI;AAErB,cAAIA,KAAI,IAAI;AACV,gBAAI,KAAK,WAAW,QAAQ;AAC1B,oBAAM,OAAO,MAAMA,KAAI,KAAI;AAC3B,kBAAI,SAAS,IAAI;yBAEN,KAAK,QAAQ,QAAQ,MAAM,YAAY;AAChD,uBAAO;yBAEP,KAAK,QAAQ,QAAQ,KACrB,KAAK,QAAQ,QAAQ,EAAE,SAAS,iCAAiC,GACjE;AACA,uBAAO;qBACF;AACL,uBAAO,KAAK,MAAM,IAAI;;;AAI1B,kBAAM,eAAc,KAAA,KAAK,QAAQ,QAAQ,OAAC,QAAA,OAAA,SAAA,SAAA,GAAE,MAAM,iCAAiC;AACnF,kBAAM,gBAAe,KAAAA,KAAI,QAAQ,IAAI,eAAe,OAAC,QAAA,OAAA,SAAA,SAAA,GAAE,MAAM,GAAG;AAChE,gBAAI,eAAe,gBAAgB,aAAa,SAAS,GAAG;AAC1D,sBAAQ,SAAS,aAAa,CAAC,CAAC;;AAKlC,gBAAI,KAAK,iBAAiB,KAAK,WAAW,SAAS,MAAM,QAAQ,IAAI,GAAG;AACtE,kBAAI,KAAK,SAAS,GAAG;AACnB,wBAAQ;;kBAEN,MAAM;kBACN,SAAS,mBAAmB,KAAK,MAAM;kBACvC,MAAM;kBACN,SAAS;;AAEX,uBAAO;AACP,wBAAQ;AACR,yBAAS;AACT,6BAAa;yBACJ,KAAK,WAAW,GAAG;AAC5B,uBAAO,KAAK,CAAC;qBACR;AACL,uBAAO;;;iBAGN;AACL,kBAAM,OAAO,MAAMA,KAAI,KAAI;AAE3B,gBAAI;AACF,sBAAQ,KAAK,MAAM,IAAI;AAGvB,kBAAI,MAAM,QAAQ,KAAK,KAAKA,KAAI,WAAW,KAAK;AAC9C,uBAAO,CAAA;AACP,wBAAQ;AACR,yBAAS;AACT,6BAAa;;qBAEf,IAAM;AAEN,kBAAIA,KAAI,WAAW,OAAO,SAAS,IAAI;AACrC,yBAAS;AACT,6BAAa;qBACR;AACL,wBAAQ;kBACN,SAAS;;;;AAKf,gBAAI,SAAS,KAAK,mBAAiB,KAAA,UAAK,QAAL,UAAK,SAAA,SAAL,MAAO,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE,SAAS,QAAQ,IAAG;AACrE,sBAAQ;AACR,uBAAS;AACT,2BAAa;;AAGf,gBAAI,SAAS,KAAK,oBAAoB;AACpC,oBAAM,IAAI,iBAAA,QAAe,KAAK;;;AAIlC,gBAAM,oBAAoB;YACxB;YACA;YACA;YACA;YACA;;AAGF,iBAAO;QACT,CAAC;AACD,YAAI,CAAC,KAAK,oBAAoB;AAC5B,gBAAM,IAAI,MAAM,CAAC,eAAc;;AAAC,mBAAC;cAC/B,OAAO;gBACL,SAAS,IAAG,KAAA,eAAU,QAAV,eAAU,SAAA,SAAV,WAAY,UAAI,QAAA,OAAA,SAAA,KAAI,YAAY,KAAK,eAAU,QAAV,eAAU,SAAA,SAAV,WAAY,OAAO;gBACpE,SAAS,IAAG,KAAA,eAAU,QAAV,eAAU,SAAA,SAAV,WAAY,WAAK,QAAA,OAAA,SAAA,KAAI,EAAE;gBACnC,MAAM;gBACN,MAAM,IAAG,KAAA,eAAU,QAAV,eAAU,SAAA,SAAV,WAAY,UAAI,QAAA,OAAA,SAAA,KAAI,EAAE;;cAEjC,MAAM;cACN,OAAO;cACP,QAAQ;cACR,YAAY;;WACZ;;AAGJ,eAAO,IAAI,KAAK,aAAa,UAAU;MACzC;;AAhMF,YAAA,UAAAD;;;;;;;;;;;;ACNA,QAAA,qBAAA,gBAAA,0BAAA;AAIA,QAAqBE,6BAArB,cAMU,mBAAA,QAAwB;;;;;;;;;;MAUhC,OAIE,SAAe;AAGf,YAAI,SAAS;AACb,cAAM,kBAAkB,YAAO,QAAP,YAAO,SAAP,UAAW,KAChC,MAAM,EAAE,EACR,IAAI,CAAC,MAAK;AACT,cAAI,KAAK,KAAK,CAAC,KAAK,CAAC,QAAQ;AAC3B,mBAAO;;AAET,cAAI,MAAM,KAAK;AACb,qBAAS,CAAC;;AAEZ,iBAAO;QACT,CAAC,EACA,KAAK,EAAE;AACV,aAAK,IAAI,aAAa,IAAI,UAAU,cAAc;AAClD,YAAI,KAAK,QAAQ,QAAQ,GAAG;AAC1B,eAAK,QAAQ,QAAQ,KAAK;;AAE5B,aAAK,QAAQ,QAAQ,KAAK;AAC1B,eAAO;MAOT;;;;;;;;;;;;;;;;;;;MA0CA,MACE,QACA,EACE,YAAY,MACZ,YACA,cACA,kBAAkB,aAAY,IAM5B,CAAA,GAAE;AAEN,cAAM,MAAM,kBAAkB,GAAG,eAAe,WAAW;AAC3D,cAAM,gBAAgB,KAAK,IAAI,aAAa,IAAI,GAAG;AAEnD,aAAK,IAAI,aAAa,IACpB,KACA,GAAG,gBAAgB,GAAG,aAAa,MAAM,EAAE,GAAG,MAAM,IAAI,YAAY,QAAQ,MAAM,GAChF,eAAe,SAAY,KAAK,aAAa,gBAAgB,YAC/D,EAAE;AAEJ,eAAO;MACT;;;;;;;;;;;MAYA,MACE,OACA,EACE,cACA,kBAAkB,aAAY,IACyB,CAAA,GAAE;AAE3D,cAAM,MAAM,OAAO,oBAAoB,cAAc,UAAU,GAAG,eAAe;AACjF,aAAK,IAAI,aAAa,IAAI,KAAK,GAAG,KAAK,EAAE;AACzC,eAAO;MACT;;;;;;;;;;;;;;;;MAiBA,MACE,MACA,IACA,EACE,cACA,kBAAkB,aAAY,IACyB,CAAA,GAAE;AAE3D,cAAM,YACJ,OAAO,oBAAoB,cAAc,WAAW,GAAG,eAAe;AACxE,cAAM,WAAW,OAAO,oBAAoB,cAAc,UAAU,GAAG,eAAe;AACtF,aAAK,IAAI,aAAa,IAAI,WAAW,GAAG,IAAI,EAAE;AAE9C,aAAK,IAAI,aAAa,IAAI,UAAU,GAAG,KAAK,OAAO,CAAC,EAAE;AACtD,eAAO;MACT;;;;;;MAOA,YAAY,QAAmB;AAC7B,aAAK,SAAS;AACd,eAAO;MACT;;;;;;;MAQA,SAAM;AAGJ,aAAK,QAAQ,QAAQ,IAAI;AACzB,eAAO;MACT;;;;;;;MAQA,cAAW;AAKT,YAAI,KAAK,WAAW,OAAO;AACzB,eAAK,QAAQ,QAAQ,IAAI;eACpB;AACL,eAAK,QAAQ,QAAQ,IAAI;;AAE3B,aAAK,gBAAgB;AACrB,eAAO;MACT;;;;MAKA,MAAG;AACD,aAAK,QAAQ,QAAQ,IAAI;AACzB,eAAO;MACT;;;;MAKA,UAAO;AACL,aAAK,QAAQ,QAAQ,IAAI;AACzB,eAAO;MACT;;;;;;;;;;;;;;;;;;;;;;;;;;MA2BA,QAAQ,EACN,UAAU,OACV,UAAU,OACV,WAAW,OACX,UAAU,OACV,MAAM,OACN,SAAS,OAAM,IAQb,CAAA,GAAE;;AACJ,cAAM,UAAU;UACd,UAAU,YAAY;UACtB,UAAU,YAAY;UACtB,WAAW,aAAa;UACxB,UAAU,YAAY;UACtB,MAAM,QAAQ;UAEb,OAAO,OAAO,EACd,KAAK,GAAG;AAEX,cAAM,gBAAe,KAAA,KAAK,QAAQ,QAAQ,OAAC,QAAA,OAAA,SAAA,KAAI;AAC/C,aAAK,QACH,QAAQ,IACN,8BAA8B,MAAM,UAAU,YAAY,cAAc,OAAO;AACnF,YAAI,WAAW;AAAQ,iBAAO;;AACzB,iBAAO;MACd;;;;;;MAOA,WAAQ;;AACN,cAAK,KAAA,KAAK,QAAQ,QAAQ,OAAC,QAAA,OAAA,SAAA,KAAI,IAAI,KAAI,EAAG,SAAS,GAAG;AACpD,eAAK,QAAQ,QAAQ,KAAK;eACrB;AACL,eAAK,QAAQ,QAAQ,IAAI;;AAE3B,eAAO;MACT;;;;;;MAOA,UAAO;AAOL,eAAO;MAOT;;AAhUF,YAAA,UAAAA;;;;;;;;;;;;ACJA,QAAA,8BAAA,gBAAA,mCAAA;AA2BA,QAAqBC,0BAArB,cAMU,4BAAA,QAA2E;;;;;;;;;MAcnF,GAAG,QAAgB,OAAc;AAC/B,aAAK,IAAI,aAAa,OAAO,QAAQ,MAAM,KAAK,EAAE;AAClD,eAAO;MACT;;;;;;;MAUA,IAAI,QAAgB,OAAc;AAChC,aAAK,IAAI,aAAa,OAAO,QAAQ,OAAO,KAAK,EAAE;AACnD,eAAO;MACT;;;;;;;MAUA,GAAG,QAAgB,OAAc;AAC/B,aAAK,IAAI,aAAa,OAAO,QAAQ,MAAM,KAAK,EAAE;AAClD,eAAO;MACT;;;;;;;MAUA,IAAI,QAAgB,OAAc;AAChC,aAAK,IAAI,aAAa,OAAO,QAAQ,OAAO,KAAK,EAAE;AACnD,eAAO;MACT;;;;;;;MAUA,GAAG,QAAgB,OAAc;AAC/B,aAAK,IAAI,aAAa,OAAO,QAAQ,MAAM,KAAK,EAAE;AAClD,eAAO;MACT;;;;;;;MAUA,IAAI,QAAgB,OAAc;AAChC,aAAK,IAAI,aAAa,OAAO,QAAQ,OAAO,KAAK,EAAE;AACnD,eAAO;MACT;;;;;;;MAUA,KAAK,QAAgB,SAAe;AAClC,aAAK,IAAI,aAAa,OAAO,QAAQ,QAAQ,OAAO,EAAE;AACtD,eAAO;MACT;;;;;;;MAaA,UAAU,QAAgB,UAA2B;AACnD,aAAK,IAAI,aAAa,OAAO,QAAQ,cAAc,SAAS,KAAK,GAAG,CAAC,GAAG;AACxE,eAAO;MACT;;;;;;;MAaA,UAAU,QAAgB,UAA2B;AACnD,aAAK,IAAI,aAAa,OAAO,QAAQ,cAAc,SAAS,KAAK,GAAG,CAAC,GAAG;AACxE,eAAO;MACT;;;;;;;MAUA,MAAM,QAAgB,SAAe;AACnC,aAAK,IAAI,aAAa,OAAO,QAAQ,SAAS,OAAO,EAAE;AACvD,eAAO;MACT;;;;;;;MAaA,WAAW,QAAgB,UAA2B;AACpD,aAAK,IAAI,aAAa,OAAO,QAAQ,eAAe,SAAS,KAAK,GAAG,CAAC,GAAG;AACzE,eAAO;MACT;;;;;;;MAaA,WAAW,QAAgB,UAA2B;AACpD,aAAK,IAAI,aAAa,OAAO,QAAQ,eAAe,SAAS,KAAK,GAAG,CAAC,GAAG;AACzE,eAAO;MACT;;;;;;;;;;;;;MAmBA,GAAG,QAAgB,OAAqB;AACtC,aAAK,IAAI,aAAa,OAAO,QAAQ,MAAM,KAAK,EAAE;AAClD,eAAO;MACT;;;;;;;MAaA,GAAG,QAAgB,QAA0B;AAC3C,cAAM,gBAAgB,MAAM,KAAK,IAAI,IAAI,MAAM,CAAC,EAC7C,IAAI,CAAC,MAAK;AAGT,cAAI,OAAO,MAAM,YAAY,IAAI,OAAO,OAAO,EAAE,KAAK,CAAC;AAAG,mBAAO,IAAI,CAAC;;AACjE,mBAAO,GAAG,CAAC;QAClB,CAAC,EACA,KAAK,GAAG;AACX,aAAK,IAAI,aAAa,OAAO,QAAQ,OAAO,aAAa,GAAG;AAC5D,eAAO;MACT;;;;;;;;MAcA,SAAS,QAAgB,OAA4D;AACnF,YAAI,OAAO,UAAU,UAAU;AAG7B,eAAK,IAAI,aAAa,OAAO,QAAQ,MAAM,KAAK,EAAE;mBACzC,MAAM,QAAQ,KAAK,GAAG;AAE/B,eAAK,IAAI,aAAa,OAAO,QAAQ,OAAO,MAAM,KAAK,GAAG,CAAC,GAAG;eACzD;AAEL,eAAK,IAAI,aAAa,OAAO,QAAQ,MAAM,KAAK,UAAU,KAAK,CAAC,EAAE;;AAEpE,eAAO;MACT;;;;;;;;MAcA,YAAY,QAAgB,OAA4D;AACtF,YAAI,OAAO,UAAU,UAAU;AAE7B,eAAK,IAAI,aAAa,OAAO,QAAQ,MAAM,KAAK,EAAE;mBACzC,MAAM,QAAQ,KAAK,GAAG;AAE/B,eAAK,IAAI,aAAa,OAAO,QAAQ,OAAO,MAAM,KAAK,GAAG,CAAC,GAAG;eACzD;AAEL,eAAK,IAAI,aAAa,OAAO,QAAQ,MAAM,KAAK,UAAU,KAAK,CAAC,EAAE;;AAEpE,eAAO;MACT;;;;;;;;MAWA,QAAQ,QAAgB,OAAa;AACnC,aAAK,IAAI,aAAa,OAAO,QAAQ,MAAM,KAAK,EAAE;AAClD,eAAO;MACT;;;;;;;;;MAYA,SAAS,QAAgB,OAAa;AACpC,aAAK,IAAI,aAAa,OAAO,QAAQ,OAAO,KAAK,EAAE;AACnD,eAAO;MACT;;;;;;;;MAWA,QAAQ,QAAgB,OAAa;AACnC,aAAK,IAAI,aAAa,OAAO,QAAQ,MAAM,KAAK,EAAE;AAClD,eAAO;MACT;;;;;;;;;MAYA,SAAS,QAAgB,OAAa;AACpC,aAAK,IAAI,aAAa,OAAO,QAAQ,OAAO,KAAK,EAAE;AACnD,eAAO;MACT;;;;;;;;;MAYA,cAAc,QAAgB,OAAa;AACzC,aAAK,IAAI,aAAa,OAAO,QAAQ,OAAO,KAAK,EAAE;AACnD,eAAO;MACT;;;;;;;;MAcA,SAAS,QAAgB,OAAkC;AACzD,YAAI,OAAO,UAAU,UAAU;AAE7B,eAAK,IAAI,aAAa,OAAO,QAAQ,MAAM,KAAK,EAAE;eAC7C;AAEL,eAAK,IAAI,aAAa,OAAO,QAAQ,OAAO,MAAM,KAAK,GAAG,CAAC,GAAG;;AAEhE,eAAO;MACT;;;;;;;;;;;MAsBA,WACE,QACA,OACA,EAAE,QAAQ,KAAI,IAAmE,CAAA,GAAE;AAEnF,YAAI,WAAW;AACf,YAAI,SAAS,SAAS;AACpB,qBAAW;mBACF,SAAS,UAAU;AAC5B,qBAAW;mBACF,SAAS,aAAa;AAC/B,qBAAW;;AAEb,cAAM,aAAa,WAAW,SAAY,KAAK,IAAI,MAAM;AACzD,aAAK,IAAI,aAAa,OAAO,QAAQ,GAAG,QAAQ,MAAM,UAAU,IAAI,KAAK,EAAE;AAC3E,eAAO;MACT;;;;;;;;MAWA,MAAM,OAA8B;AAClC,eAAO,QAAQ,KAAK,EAAE,QAAQ,CAAC,CAAC,QAAQ,KAAK,MAAK;AAChD,eAAK,IAAI,aAAa,OAAO,QAAQ,MAAM,KAAK,EAAE;QACpD,CAAC;AACD,eAAO;MACT;;;;;;;;;;;;;;MAqBA,IAAI,QAAgB,UAAkB,OAAc;AAClD,aAAK,IAAI,aAAa,OAAO,QAAQ,OAAO,QAAQ,IAAI,KAAK,EAAE;AAC/D,eAAO;MACT;;;;;;;;;;;;;;;;MAiBA,GACE,SACA,EACE,cACA,kBAAkB,aAAY,IACyB,CAAA,GAAE;AAE3D,cAAM,MAAM,kBAAkB,GAAG,eAAe,QAAQ;AACxD,aAAK,IAAI,aAAa,OAAO,KAAK,IAAI,OAAO,GAAG;AAChD,eAAO;MACT;;;;;;;;;;;;;;MAqBA,OAAO,QAAgB,UAAkB,OAAc;AACrD,aAAK,IAAI,aAAa,OAAO,QAAQ,GAAG,QAAQ,IAAI,KAAK,EAAE;AAC3D,eAAO;MACT;;AAtfF,YAAA,UAAAA;;;;;;;;;;;;AC1BA,QAAA,2BAAA,gBAAA,gCAAA;AAIA,QAAqBC,yBAArB,MAA0C;MAYxC,YACE,KACA,EACE,UAAU,CAAA,GACV,QACA,OAAAC,OAAK,GAKN;AAED,aAAK,MAAM;AACX,aAAK,UAAU;AACf,aAAK,SAAS;AACd,aAAK,QAAQA;MACf;;;;;;;;;;;;;;;;;;;;;;MAuBA,OAIE,SACA,EACE,MAAAC,QAAO,OACP,MAAK,IAIH,CAAA,GAAE;AAEN,cAAM,SAASA,QAAO,SAAS;AAE/B,YAAI,SAAS;AACb,cAAM,kBAAkB,YAAO,QAAP,YAAO,SAAP,UAAW,KAChC,MAAM,EAAE,EACR,IAAI,CAAC,MAAK;AACT,cAAI,KAAK,KAAK,CAAC,KAAK,CAAC,QAAQ;AAC3B,mBAAO;;AAET,cAAI,MAAM,KAAK;AACb,qBAAS,CAAC;;AAEZ,iBAAO;QACT,CAAC,EACA,KAAK,EAAE;AACV,aAAK,IAAI,aAAa,IAAI,UAAU,cAAc;AAClD,YAAI,OAAO;AACT,eAAK,QAAQ,QAAQ,IAAI,SAAS,KAAK;;AAGzC,eAAO,IAAI,yBAAA,QAAuB;UAChC;UACA,KAAK,KAAK;UACV,SAAS,KAAK;UACd,QAAQ,KAAK;UACb,OAAO,KAAK;UACZ,YAAY;SAC+B;MAC/C;;;;;;;;;;;;;;;;;;;;;;;;;;;MA0CA,OACE,QACA,EACE,OACA,gBAAgB,KAAI,IAIlB,CAAA,GAAE;AAEN,cAAM,SAAS;AAEf,cAAM,iBAAiB,CAAA;AACvB,YAAI,KAAK,QAAQ,QAAQ,GAAG;AAC1B,yBAAe,KAAK,KAAK,QAAQ,QAAQ,CAAC;;AAE5C,YAAI,OAAO;AACT,yBAAe,KAAK,SAAS,KAAK,EAAE;;AAEtC,YAAI,CAAC,eAAe;AAClB,yBAAe,KAAK,iBAAiB;;AAEvC,aAAK,QAAQ,QAAQ,IAAI,eAAe,KAAK,GAAG;AAEhD,YAAI,MAAM,QAAQ,MAAM,GAAG;AACzB,gBAAM,UAAU,OAAO,OAAO,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,KAAK,CAAC,CAAC,GAAG,CAAA,CAAc;AACpF,cAAI,QAAQ,SAAS,GAAG;AACtB,kBAAM,gBAAgB,CAAC,GAAG,IAAI,IAAI,OAAO,CAAC,EAAE,IAAI,CAAC,WAAW,IAAI,MAAM,GAAG;AACzE,iBAAK,IAAI,aAAa,IAAI,WAAW,cAAc,KAAK,GAAG,CAAC;;;AAIhE,eAAO,IAAI,yBAAA,QAAuB;UAChC;UACA,KAAK,KAAK;UACV,SAAS,KAAK;UACd,QAAQ,KAAK;UACb,MAAM;UACN,OAAO,KAAK;UACZ,YAAY;SACwB;MACxC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MA0DA,OACE,QACA,EACE,YACA,mBAAmB,OACnB,OACA,gBAAgB,KAAI,IAMlB,CAAA,GAAE;AAEN,cAAM,SAAS;AAEf,cAAM,iBAAiB,CAAC,cAAc,mBAAmB,WAAW,OAAO,aAAa;AAExF,YAAI,eAAe;AAAW,eAAK,IAAI,aAAa,IAAI,eAAe,UAAU;AACjF,YAAI,KAAK,QAAQ,QAAQ,GAAG;AAC1B,yBAAe,KAAK,KAAK,QAAQ,QAAQ,CAAC;;AAE5C,YAAI,OAAO;AACT,yBAAe,KAAK,SAAS,KAAK,EAAE;;AAEtC,YAAI,CAAC,eAAe;AAClB,yBAAe,KAAK,iBAAiB;;AAEvC,aAAK,QAAQ,QAAQ,IAAI,eAAe,KAAK,GAAG;AAEhD,YAAI,MAAM,QAAQ,MAAM,GAAG;AACzB,gBAAM,UAAU,OAAO,OAAO,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,KAAK,CAAC,CAAC,GAAG,CAAA,CAAc;AACpF,cAAI,QAAQ,SAAS,GAAG;AACtB,kBAAM,gBAAgB,CAAC,GAAG,IAAI,IAAI,OAAO,CAAC,EAAE,IAAI,CAAC,WAAW,IAAI,MAAM,GAAG;AACzE,iBAAK,IAAI,aAAa,IAAI,WAAW,cAAc,KAAK,GAAG,CAAC;;;AAIhE,eAAO,IAAI,yBAAA,QAAuB;UAChC;UACA,KAAK,KAAK;UACV,SAAS,KAAK;UACd,QAAQ,KAAK;UACb,MAAM;UACN,OAAO,KAAK;UACZ,YAAY;SACwB;MACxC;;;;;;;;;;;;;;;;;;;;;;MAuBA,OACE,QACA,EACE,MAAK,IAGH,CAAA,GAAE;AAEN,cAAM,SAAS;AACf,cAAM,iBAAiB,CAAA;AACvB,YAAI,KAAK,QAAQ,QAAQ,GAAG;AAC1B,yBAAe,KAAK,KAAK,QAAQ,QAAQ,CAAC;;AAE5C,YAAI,OAAO;AACT,yBAAe,KAAK,SAAS,KAAK,EAAE;;AAEtC,aAAK,QAAQ,QAAQ,IAAI,eAAe,KAAK,GAAG;AAEhD,eAAO,IAAI,yBAAA,QAAuB;UAChC;UACA,KAAK,KAAK;UACV,SAAS,KAAK;UACd,QAAQ,KAAK;UACb,MAAM;UACN,OAAO,KAAK;UACZ,YAAY;SACwB;MACxC;;;;;;;;;;;;;;;;;;;;MAqBA,OAAO,EACL,MAAK,IAGH,CAAA,GAAE;AACJ,cAAM,SAAS;AACf,cAAM,iBAAiB,CAAA;AACvB,YAAI,OAAO;AACT,yBAAe,KAAK,SAAS,KAAK,EAAE;;AAEtC,YAAI,KAAK,QAAQ,QAAQ,GAAG;AAC1B,yBAAe,QAAQ,KAAK,QAAQ,QAAQ,CAAC;;AAE/C,aAAK,QAAQ,QAAQ,IAAI,eAAe,KAAK,GAAG;AAEhD,eAAO,IAAI,yBAAA,QAAuB;UAChC;UACA,KAAK,KAAK;UACV,SAAS,KAAK;UACd,QAAQ,KAAK;UACb,OAAO,KAAK;UACZ,YAAY;SACwB;MACxC;;AAtXF,YAAA,UAAAF;;;;;;;;;;ACLa,YAAA,UAAU;;;;;;;;;;ACAvB,QAAA,YAAA;AACa,YAAA,kBAAkB,EAAE,iBAAiB,gBAAgB,UAAA,OAAO,GAAE;;;;;;;;;;;;ACD3E,QAAA,0BAAA,gBAAA,+BAAA;AACA,QAAA,2BAAA,gBAAA,gCAAA;AAEA,QAAA,cAAA;AAaA,QAAqBG,mBAArB,MAAqB,iBAAe;;;;;;;;;;;MAwBlC,YACE,KACA,EACE,UAAU,CAAA,GACV,QACA,OAAAC,OAAK,IAKH,CAAA,GAAE;AAEN,aAAK,MAAM;AACX,aAAK,UAAO,OAAA,OAAA,OAAA,OAAA,CAAA,GAAQ,YAAA,eAAe,GAAK,OAAO;AAC/C,aAAK,aAAa;AAClB,aAAK,QAAQA;MACf;;;;;;MAcA,KAAK,UAAgB;AACnB,cAAM,MAAM,IAAI,IAAI,GAAG,KAAK,GAAG,IAAI,QAAQ,EAAE;AAC7C,eAAO,IAAI,wBAAA,QAAsB,KAAK;UACpC,SAAO,OAAA,OAAA,CAAA,GAAO,KAAK,OAAO;UAC1B,QAAQ,KAAK;UACb,OAAO,KAAK;SACb;MACH;;;;;;;;MASA,OACE,QAAqB;AAMrB,eAAO,IAAI,iBAAgB,KAAK,KAAK;UACnC,SAAS,KAAK;UACd;UACA,OAAO,KAAK;SACb;MACH;;;;;;;;;;;;;;;;;;;;;;;;MAyBA,IACE,IACA,OAAmB,CAAA,GACnB,EACE,MAAAC,QAAO,OACP,KAAAC,OAAM,OACN,MAAK,IAKH,CAAA,GAAE;AAUN,YAAI;AACJ,cAAM,MAAM,IAAI,IAAI,GAAG,KAAK,GAAG,QAAQ,EAAE,EAAE;AAC3C,YAAI;AACJ,YAAID,SAAQC,MAAK;AACf,mBAASD,QAAO,SAAS;AACzB,iBAAO,QAAQ,IAAI,EAGhB,OAAO,CAAC,CAAC,GAAG,KAAK,MAAM,UAAU,MAAS,EAE1C,IAAI,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,MAAM,QAAQ,KAAK,IAAI,IAAI,MAAM,KAAK,GAAG,CAAC,MAAM,GAAG,KAAK,EAAE,CAAC,EACzF,QAAQ,CAAC,CAAC,MAAM,KAAK,MAAK;AACzB,gBAAI,aAAa,OAAO,MAAM,KAAK;UACrC,CAAC;eACE;AACL,mBAAS;AACT,iBAAO;;AAGT,cAAM,UAAO,OAAA,OAAA,CAAA,GAAQ,KAAK,OAAO;AACjC,YAAI,OAAO;AACT,kBAAQ,QAAQ,IAAI,SAAS,KAAK;;AAGpC,eAAO,IAAI,yBAAA,QAAuB;UAChC;UACA;UACA;UACA,QAAQ,KAAK;UACb;UACA,OAAO,KAAK;UACZ,YAAY;SACiC;MACjD;;AAjKF,YAAA,UAAAF;;;;;;;;;;;;;ACfA,QAAA,oBAAA,gBAAA,yBAAA;AAOE,YAAA,kBAPK,kBAAA;AACP,QAAA,0BAAA,gBAAA,+BAAA;AAOE,YAAA,wBAPK,wBAAA;AACP,QAAA,2BAAA,gBAAA,gCAAA;AAOE,YAAA,yBAPK,yBAAA;AACP,QAAA,8BAAA,gBAAA,mCAAA;AAOE,YAAA,4BAPK,4BAAA;AACP,QAAA,qBAAA,gBAAA,0BAAA;AAOE,YAAA,mBAPK,mBAAA;AASP,YAAA,UAAe;MACb,iBAAA,kBAAA;MACA,uBAAA,wBAAA;MACA,wBAAA,yBAAA;MACA,2BAAA,4BAAA;MACA,kBAAA,mBAAA;;;;;;ACjBK,IAAM,eAAe,CAAC,gBAA8B;AACzD,MAAI;AACJ,MAAI,aAAa;AACf,aAAS;aACA,OAAO,UAAU,aAAa;AACvC,aAAS,IAAI,SACX,OAAO,uBAA6B,EAAE,KAAK,CAAC,EAAE,SAASI,OAAK,MAAOA,OAAM,GAAG,IAAI,CAAC;SAC9E;AACL,aAAS;;AAEX,SAAO,IAAI,SAAS,OAAO,GAAG,IAAI;AACpC;;;ACGM,IAAO,iBAAP,cAA8B,MAAK;EAEvC,YAAY,SAAiB,OAAO,kBAAkB,SAAa;AACjE,UAAM,OAAO;AACb,SAAK,OAAO;AACZ,SAAK,UAAU;EACjB;;AAGI,IAAO,sBAAP,cAAmC,eAAc;EACrD,YAAY,SAAY;AACtB,UAAM,iDAAiD,uBAAuB,OAAO;EACvF;;AAGI,IAAO,sBAAP,cAAmC,eAAc;EACrD,YAAY,SAAY;AACtB,UAAM,0CAA0C,uBAAuB,OAAO;EAChF;;AAGI,IAAO,qBAAP,cAAkC,eAAc;EACpD,YAAY,SAAY;AACtB,UAAM,gDAAgD,sBAAsB,OAAO;EACrF;;AAGF,IAAY;CAAZ,SAAYC,iBAAc;AACxB,EAAAA,gBAAA,KAAA,IAAA;AACA,EAAAA,gBAAA,cAAA,IAAA;AACA,EAAAA,gBAAA,cAAA,IAAA;AACA,EAAAA,gBAAA,UAAA,IAAA;AACA,EAAAA,gBAAA,cAAA,IAAA;AACA,EAAAA,gBAAA,cAAA,IAAA;AACA,EAAAA,gBAAA,YAAA,IAAA;AACA,EAAAA,gBAAA,YAAA,IAAA;AACA,EAAAA,gBAAA,SAAA,IAAA;AACA,EAAAA,gBAAA,SAAA,IAAA;AACA,EAAAA,gBAAA,SAAA,IAAA;AACA,EAAAA,gBAAA,SAAA,IAAA;AACA,EAAAA,gBAAA,SAAA,IAAA;AACA,EAAAA,gBAAA,SAAA,IAAA;AACA,EAAAA,gBAAA,SAAA,IAAA;AACF,GAhBY,mBAAA,iBAAc,CAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChCpB,IAAO,kBAAP,MAAsB;EAM1B,YACE,KACA,EACE,UAAU,CAAA,GACV,aACA,SAAS,eAAe,IAAG,IAKzB,CAAA,GAAE;AAEN,SAAK,MAAM;AACX,SAAK,UAAU;AACf,SAAK,SAAS;AACd,SAAK,QAAQ,aAAa,WAAW;EACvC;;;;;EAMA,QAAQ,OAAa;AACnB,SAAK,QAAQ,gBAAgB,UAAU,KAAK;EAC9C;;;;;;EAOM,OACJ,cACA,UAAiC,CAAA,GAAE;;;AAEnC,UAAI;AACF,cAAM,EAAE,SAAS,QAAQ,MAAM,aAAY,IAAK;AAChD,YAAI,WAAmC,CAAA;AACvC,YAAI,EAAE,OAAM,IAAK;AACjB,YAAI,CAAC,QAAQ;AACX,mBAAS,KAAK;;AAEhB,YAAI,UAAU,WAAW,OAAO;AAC9B,mBAAS,UAAU,IAAI;;AAEzB,YAAI;AACJ,YACE,iBACE,WAAW,CAAC,OAAO,UAAU,eAAe,KAAK,SAAS,cAAc,KAAM,CAAC,UACjF;AACA,cACG,OAAO,SAAS,eAAe,wBAAwB,QACxD,wBAAwB,aACxB;AAGA,qBAAS,cAAc,IAAI;AAC3B,mBAAO;qBACE,OAAO,iBAAiB,UAAU;AAE3C,qBAAS,cAAc,IAAI;AAC3B,mBAAO;qBACE,OAAO,aAAa,eAAe,wBAAwB,UAAU;AAG9E,mBAAO;iBACF;AAEL,qBAAS,cAAc,IAAI;AAC3B,mBAAO,KAAK,UAAU,YAAY;;;AAItC,cAAM,WAAW,MAAM,KAAK,MAAM,GAAG,KAAK,GAAG,IAAI,YAAY,IAAI;UAC/D,QAAQ,UAAU;;;;;UAKlB,SAAO,OAAA,OAAA,OAAA,OAAA,OAAA,OAAA,CAAA,GAAO,QAAQ,GAAK,KAAK,OAAO,GAAK,OAAO;UACnD;SACD,EAAE,MAAM,CAAC,eAAc;AACtB,gBAAM,IAAI,oBAAoB,UAAU;QAC1C,CAAC;AAED,cAAM,eAAe,SAAS,QAAQ,IAAI,eAAe;AACzD,YAAI,gBAAgB,iBAAiB,QAAQ;AAC3C,gBAAM,IAAI,oBAAoB,QAAQ;;AAGxC,YAAI,CAAC,SAAS,IAAI;AAChB,gBAAM,IAAI,mBAAmB,QAAQ;;AAGvC,YAAI,iBAAgB,KAAA,SAAS,QAAQ,IAAI,cAAc,OAAC,QAAA,OAAA,SAAA,KAAI,cAAc,MAAM,GAAG,EAAE,CAAC,EAAE,KAAI;AAC5F,YAAI;AACJ,YAAI,iBAAiB,oBAAoB;AACvC,iBAAO,MAAM,SAAS,KAAI;mBACjB,iBAAiB,4BAA4B;AACtD,iBAAO,MAAM,SAAS,KAAI;mBACjB,iBAAiB,qBAAqB;AAC/C,iBAAO;mBACE,iBAAiB,uBAAuB;AACjD,iBAAO,MAAM,SAAS,SAAQ;eACzB;AAEL,iBAAO,MAAM,SAAS,KAAI;;AAG5B,eAAO,EAAE,MAAM,OAAO,KAAI;eACnB,OAAO;AACd,eAAO,EAAE,MAAM,MAAM,MAAK;;;;;;;AChIhC,iBAAkB;AAClB,IAAM;AAAA,EACJ;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,IAAI,WAAAC;;;ACPG,IAAM,UAAU;;;ACEhB,IAAM,kBAAkB,EAAE,iBAAiB,eAAe,OAAO,GAAE;AAEnE,IAAM,MAAc;AAEpB,IAAM,kBAAkB;AAExB,IAAM,kBAAkB;AAE/B,IAAY;CAAZ,SAAYC,gBAAa;AACvB,EAAAA,eAAAA,eAAA,YAAA,IAAA,CAAA,IAAA;AACA,EAAAA,eAAAA,eAAA,MAAA,IAAA,CAAA,IAAA;AACA,EAAAA,eAAAA,eAAA,SAAA,IAAA,CAAA,IAAA;AACA,EAAAA,eAAAA,eAAA,QAAA,IAAA,CAAA,IAAA;AACF,GALY,kBAAA,gBAAa,CAAA,EAAA;AAOzB,IAAY;CAAZ,SAAYC,iBAAc;AACxB,EAAAA,gBAAA,QAAA,IAAA;AACA,EAAAA,gBAAA,SAAA,IAAA;AACA,EAAAA,gBAAA,QAAA,IAAA;AACA,EAAAA,gBAAA,SAAA,IAAA;AACA,EAAAA,gBAAA,SAAA,IAAA;AACF,GANY,mBAAA,iBAAc,CAAA,EAAA;AAQ1B,IAAY;CAAZ,SAAYC,iBAAc;AACxB,EAAAA,gBAAA,OAAA,IAAA;AACA,EAAAA,gBAAA,OAAA,IAAA;AACA,EAAAA,gBAAA,MAAA,IAAA;AACA,EAAAA,gBAAA,OAAA,IAAA;AACA,EAAAA,gBAAA,OAAA,IAAA;AACA,EAAAA,gBAAA,cAAA,IAAA;AACF,GAPY,mBAAA,iBAAc,CAAA,EAAA;AAS1B,IAAY;CAAZ,SAAYC,aAAU;AACpB,EAAAA,YAAA,WAAA,IAAA;AACF,GAFY,eAAA,aAAU,CAAA,EAAA;AAItB,IAAY;CAAZ,SAAYC,mBAAgB;AAC1B,EAAAA,kBAAA,YAAA,IAAA;AACA,EAAAA,kBAAA,MAAA,IAAA;AACA,EAAAA,kBAAA,SAAA,IAAA;AACA,EAAAA,kBAAA,QAAA,IAAA;AACF,GALY,qBAAA,mBAAgB,CAAA,EAAA;;;ACnC5B,IAAqB,aAArB,MAA+B;EAA/B,cAAA;AACE,SAAA,gBAAgB;EA4ClB;EA1CE,OAAO,YAAkC,UAAkB;AACzD,QAAI,WAAW,gBAAgB,aAAa;AAC1C,aAAO,SAAS,KAAK,cAAc,UAAU,CAAC;;AAGhD,QAAI,OAAO,eAAe,UAAU;AAClC,aAAO,SAAS,KAAK,MAAM,UAAU,CAAC;;AAGxC,WAAO,SAAS,CAAA,CAAE;EACpB;EAEQ,cAAc,QAAmB;AACvC,UAAM,OAAO,IAAI,SAAS,MAAM;AAChC,UAAM,UAAU,IAAI,YAAW;AAE/B,WAAO,KAAK,iBAAiB,QAAQ,MAAM,OAAO;EACpD;EAEQ,iBACN,QACA,MACA,SAAoB;AAOpB,UAAM,YAAY,KAAK,SAAS,CAAC;AACjC,UAAM,YAAY,KAAK,SAAS,CAAC;AACjC,QAAI,SAAS,KAAK,gBAAgB;AAClC,UAAM,QAAQ,QAAQ,OAAO,OAAO,MAAM,QAAQ,SAAS,SAAS,CAAC;AACrE,aAAS,SAAS;AAClB,UAAM,QAAQ,QAAQ,OAAO,OAAO,MAAM,QAAQ,SAAS,SAAS,CAAC;AACrE,aAAS,SAAS;AAClB,UAAM,OAAO,KAAK,MAChB,QAAQ,OAAO,OAAO,MAAM,QAAQ,OAAO,UAAU,CAAC,CAAC;AAGzD,WAAO,EAAE,KAAK,MAAM,OAAc,OAAc,SAAS,KAAI;EAC/D;;;;ACnCF,IAAqB,QAArB,MAA0B;EAIxB,YAAmB,UAA2B,WAAmB;AAA9C,SAAA,WAAA;AAA2B,SAAA,YAAA;AAH9C,SAAA,QAA4B;AAC5B,SAAA,QAAgB;AAGd,SAAK,WAAW;AAChB,SAAK,YAAY;EACnB;EAEA,QAAK;AACH,SAAK,QAAQ;AACb,iBAAa,KAAK,KAAK;EACzB;;EAGA,kBAAe;AACb,iBAAa,KAAK,KAAK;AAEvB,SAAK,QAAa,WAAW,MAAK;AAChC,WAAK,QAAQ,KAAK,QAAQ;AAC1B,WAAK,SAAQ;IACf,GAAG,KAAK,UAAU,KAAK,QAAQ,CAAC,CAAC;EACnC;;;;AC3BF,IAAY;CAAZ,SAAYC,gBAAa;AACvB,EAAAA,eAAA,SAAA,IAAA;AACA,EAAAA,eAAA,MAAA,IAAA;AACA,EAAAA,eAAA,MAAA,IAAA;AACA,EAAAA,eAAA,WAAA,IAAA;AACA,EAAAA,eAAA,QAAA,IAAA;AACA,EAAAA,eAAA,QAAA,IAAA;AACA,EAAAA,eAAA,MAAA,IAAA;AACA,EAAAA,eAAA,MAAA,IAAA;AACA,EAAAA,eAAA,WAAA,IAAA;AACA,EAAAA,eAAA,MAAA,IAAA;AACA,EAAAA,eAAA,WAAA,IAAA;AACA,EAAAA,eAAA,MAAA,IAAA;AACA,EAAAA,eAAA,OAAA,IAAA;AACA,EAAAA,eAAA,OAAA,IAAA;AACA,EAAAA,eAAA,SAAA,IAAA;AACA,EAAAA,eAAA,KAAA,IAAA;AACA,EAAAA,eAAA,SAAA,IAAA;AACA,EAAAA,eAAA,MAAA,IAAA;AACA,EAAAA,eAAA,MAAA,IAAA;AACA,EAAAA,eAAA,WAAA,IAAA;AACA,EAAAA,eAAA,aAAA,IAAA;AACA,EAAAA,eAAA,QAAA,IAAA;AACA,EAAAA,eAAA,SAAA,IAAA;AACA,EAAAA,eAAA,WAAA,IAAA;AACF,GAzBY,kBAAA,gBAAa,CAAA,EAAA;AAqDlB,IAAM,oBAAoB,CAC/B,SACA,QACA,UAAoC,CAAA,MAC1B;;AACV,QAAM,aAAY,KAAA,QAAQ,eAAS,QAAA,OAAA,SAAA,KAAI,CAAA;AAEvC,SAAO,OAAO,KAAK,MAAM,EAAE,OAAO,CAAC,KAAK,YAAW;AACjD,QAAI,OAAO,IAAI,cAAc,SAAS,SAAS,QAAQ,SAAS;AAChE,WAAO;EACT,GAAG,CAAA,CAAY;AACjB;AAgBO,IAAM,gBAAgB,CAC3B,YACA,SACA,QACA,cACe;AACf,QAAM,SAAS,QAAQ,KAAK,CAAC,MAAM,EAAE,SAAS,UAAU;AACxD,QAAM,UAAU,WAAM,QAAN,WAAM,SAAA,SAAN,OAAQ;AACxB,QAAM,QAAQ,OAAO,UAAU;AAE/B,MAAI,WAAW,CAAC,UAAU,SAAS,OAAO,GAAG;AAC3C,WAAO,YAAY,SAAS,KAAK;;AAGnC,SAAO,KAAK,KAAK;AACnB;AAeO,IAAM,cAAc,CAAC,MAAc,UAAmC;AAE3E,MAAI,KAAK,OAAO,CAAC,MAAM,KAAK;AAC1B,UAAM,WAAW,KAAK,MAAM,GAAG,KAAK,MAAM;AAC1C,WAAO,QAAQ,OAAO,QAAQ;;AAIhC,UAAQ,MAAM;IACZ,KAAK,cAAc;AACjB,aAAO,UAAU,KAAK;IACxB,KAAK,cAAc;IACnB,KAAK,cAAc;IACnB,KAAK,cAAc;IACnB,KAAK,cAAc;IACnB,KAAK,cAAc;IACnB,KAAK,cAAc;IACnB,KAAK,cAAc;AACjB,aAAO,SAAS,KAAK;IACvB,KAAK,cAAc;IACnB,KAAK,cAAc;AACjB,aAAO,OAAO,KAAK;IACrB,KAAK,cAAc;AACjB,aAAO,kBAAkB,KAAK;IAChC,KAAK,cAAc;IACnB,KAAK,cAAc;IACnB,KAAK,cAAc;IACnB,KAAK,cAAc;IACnB,KAAK,cAAc;IACnB,KAAK,cAAc;IACnB,KAAK,cAAc;IACnB,KAAK,cAAc;IACnB,KAAK,cAAc;IACnB,KAAK,cAAc;IACnB,KAAK,cAAc;IACnB,KAAK,cAAc;IACnB,KAAK,cAAc;AACjB,aAAO,KAAK,KAAK;IACnB;AAEE,aAAO,KAAK,KAAK;;AAEvB;AAEA,IAAM,OAAO,CAAC,UAAmC;AAC/C,SAAO;AACT;AACO,IAAM,YAAY,CAAC,UAAmC;AAC3D,UAAQ,OAAO;IACb,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT;AACE,aAAO;;AAEb;AACO,IAAM,WAAW,CAAC,UAAmC;AAC1D,MAAI,OAAO,UAAU,UAAU;AAC7B,UAAM,cAAc,WAAW,KAAK;AACpC,QAAI,CAAC,OAAO,MAAM,WAAW,GAAG;AAC9B,aAAO;;;AAGX,SAAO;AACT;AACO,IAAM,SAAS,CAAC,UAAmC;AACxD,MAAI,OAAO,UAAU,UAAU;AAC7B,QAAI;AACF,aAAO,KAAK,MAAM,KAAK;aAChB,OAAO;AACd,cAAQ,IAAI,qBAAqB,KAAK,EAAE;AACxC,aAAO;;;AAGX,SAAO;AACT;AAYO,IAAM,UAAU,CAAC,OAAoB,SAA6B;AACvE,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO;;AAGT,QAAM,UAAU,MAAM,SAAS;AAC/B,QAAM,aAAa,MAAM,OAAO;AAChC,QAAM,YAAY,MAAM,CAAC;AAGzB,MAAI,cAAc,OAAO,eAAe,KAAK;AAC3C,QAAI;AACJ,UAAM,UAAU,MAAM,MAAM,GAAG,OAAO;AAGtC,QAAI;AACF,YAAM,KAAK,MAAM,MAAM,UAAU,GAAG;aAC7B,GAAG;AAEV,YAAM,UAAU,QAAQ,MAAM,GAAG,IAAI,CAAA;;AAGvC,WAAO,IAAI,IAAI,CAAC,QAAmB,YAAY,MAAM,GAAG,CAAC;;AAG3D,SAAO;AACT;AASO,IAAM,oBAAoB,CAAC,UAAmC;AACnE,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO,MAAM,QAAQ,KAAK,GAAG;;AAG/B,SAAO;AACT;AAEO,IAAM,kBAAkB,CAAC,cAA6B;AAC3D,MAAI,MAAM;AACV,QAAM,IAAI,QAAQ,QAAQ,MAAM;AAChC,QAAM,IAAI,QAAQ,mDAAmD,EAAE;AACvE,SAAO,IAAI,QAAQ,QAAQ,EAAE;AAC/B;;;AC1PA,IAAqB,OAArB,MAAyB;;;;;;;;;EAsBvB,YACS,SACA,OACA,UAAkC,CAAA,GAClC,UAAkB,iBAAe;AAHjC,SAAA,UAAA;AACA,SAAA,QAAA;AACA,SAAA,UAAA;AACA,SAAA,UAAA;AAzBT,SAAA,OAAgB;AAChB,SAAA,eAAmC;AACnC,SAAA,MAAc;AACd,SAAA,eAGW;AACX,SAAA,WAGM,CAAA;AACN,SAAA,WAA0B;EAevB;EAEH,OAAO,SAAe;AACpB,SAAK,UAAU;AACf,SAAK,gBAAe;AACpB,SAAK,MAAM;AACX,SAAK,WAAW;AAChB,SAAK,eAAe;AACpB,SAAK,OAAO;AACZ,SAAK,KAAI;EACX;EAEA,OAAI;AACF,QAAI,KAAK,aAAa,SAAS,GAAG;AAChC;;AAEF,SAAK,aAAY;AACjB,SAAK,OAAO;AACZ,SAAK,QAAQ,OAAO,KAAK;MACvB,OAAO,KAAK,QAAQ;MACpB,OAAO,KAAK;MACZ,SAAS,KAAK;MACd,KAAK,KAAK;MACV,UAAU,KAAK,QAAQ,SAAQ;KAChC;EACH;EAEA,cAAc,SAA+B;AAC3C,SAAK,UAAO,OAAA,OAAA,OAAA,OAAA,CAAA,GAAQ,KAAK,OAAO,GAAK,OAAO;EAC9C;EAEA,QAAQ,QAAgB,UAAkB;;AACxC,QAAI,KAAK,aAAa,MAAM,GAAG;AAC7B,gBAAS,KAAA,KAAK,kBAAY,QAAA,OAAA,SAAA,SAAA,GAAE,QAAQ;;AAGtC,SAAK,SAAS,KAAK,EAAE,QAAQ,SAAQ,CAAE;AACvC,WAAO;EACT;EAEA,eAAY;AACV,QAAI,KAAK,cAAc;AACrB;;AAEF,SAAK,MAAM,KAAK,QAAQ,OAAO,SAAQ;AACvC,SAAK,WAAW,KAAK,QAAQ,gBAAgB,KAAK,GAAG;AAErD,UAAM,WAAW,CAAC,YAAgB;AAChC,WAAK,gBAAe;AACpB,WAAK,eAAc;AACnB,WAAK,eAAe;AACpB,WAAK,cAAc,OAAO;IAC5B;AAEA,SAAK,QAAQ,IAAI,KAAK,UAAU,CAAA,GAAI,QAAQ;AAE5C,SAAK,eAAoB,WAAW,MAAK;AACvC,WAAK,QAAQ,WAAW,CAAA,CAAE;IAC5B,GAAG,KAAK,OAAO;EACjB;EAEA,QAAQ,QAAgB,UAAa;AACnC,QAAI,KAAK;AACP,WAAK,QAAQ,SAAS,KAAK,UAAU,EAAE,QAAQ,SAAQ,CAAE;EAC7D;EAEA,UAAO;AACL,SAAK,gBAAe;AACpB,SAAK,eAAc;EACrB;EAEQ,kBAAe;AACrB,QAAI,CAAC,KAAK,UAAU;AAClB;;AAGF,SAAK,QAAQ,KAAK,KAAK,UAAU,CAAA,CAAE;EACrC;EAEQ,iBAAc;AACpB,iBAAa,KAAK,YAAY;AAC9B,SAAK,eAAe;EACtB;EAEQ,cAAc,EACpB,QACA,SAAQ,GAIT;AACC,SAAK,SACF,OAAO,CAAC,MAAM,EAAE,WAAW,MAAM,EACjC,QAAQ,CAAC,MAAM,EAAE,SAAS,QAAQ,CAAC;EACxC;EAEQ,aAAa,QAAc;AACjC,WAAO,KAAK,gBAAgB,KAAK,aAAa,WAAW;EAC3D;;;;AC9FF,IAAY;CAAZ,SAAYC,kCAA+B;AACzC,EAAAA,iCAAA,MAAA,IAAA;AACA,EAAAA,iCAAA,MAAA,IAAA;AACA,EAAAA,iCAAA,OAAA,IAAA;AACF,GAJY,oCAAA,kCAA+B,CAAA,EAAA;AA4B3C,IAAqB,mBAArB,MAAqB,kBAAgB;;;;;;;;EAqBnC,YAAmB,SAA0B,MAAmB;AAA7C,SAAA,UAAA;AApBnB,SAAA,QAA+B,CAAA;AAC/B,SAAA,eAAkC,CAAA;AAClC,SAAA,UAAyB;AACzB,SAAA,SAII;MACF,QAAQ,MAAK;MAAE;MACf,SAAS,MAAK;MAAE;MAChB,QAAQ,MAAK;MAAE;;AAWf,UAAM,UAAS,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM,WAAU;MAC7B,OAAO;MACP,MAAM;;AAGR,SAAK,QAAQ,IAAI,OAAO,OAAO,CAAA,GAAI,CAAC,aAA8B;AAChE,YAAM,EAAE,QAAQ,SAAS,OAAM,IAAK,KAAK;AAEzC,WAAK,UAAU,KAAK,QAAQ,SAAQ;AAEpC,WAAK,QAAQ,kBAAiB,UAC5B,KAAK,OACL,UACA,QACA,OAAO;AAGT,WAAK,aAAa,QAAQ,CAAC,SAAQ;AACjC,aAAK,QAAQ,kBAAiB,SAC5B,KAAK,OACL,MACA,QACA,OAAO;MAEX,CAAC;AAED,WAAK,eAAe,CAAA;AAEpB,aAAM;IACR,CAAC;AAED,SAAK,QAAQ,IAAI,OAAO,MAAM,CAAA,GAAI,CAAC,SAAyB;AAC1D,YAAM,EAAE,QAAQ,SAAS,OAAM,IAAK,KAAK;AAEzC,UAAI,KAAK,mBAAkB,GAAI;AAC7B,aAAK,aAAa,KAAK,IAAI;aACtB;AACL,aAAK,QAAQ,kBAAiB,SAC5B,KAAK,OACL,MACA,QACA,OAAO;AAGT,eAAM;;IAEV,CAAC;AAED,SAAK,OAAO,CAAC,KAAK,kBAAkB,iBAAgB;AAClD,WAAK,QAAQ,SAAS,YAAY;QAChC,OAAO;QACP;QACA;QACA;OACD;IACH,CAAC;AAED,SAAK,QAAQ,CAAC,KAAK,kBAAkB,kBAAiB;AACpD,WAAK,QAAQ,SAAS,YAAY;QAChC,OAAO;QACP;QACA;QACA;OACD;IACH,CAAC;AAED,SAAK,OAAO,MAAK;AACf,WAAK,QAAQ,SAAS,YAAY,EAAE,OAAO,OAAM,CAAE;IACrD,CAAC;EACH;;;;;;;;;;;EAYQ,OAAO,UACb,cACA,UACA,QACA,SAAgC;AAEhC,UAAM,QAAQ,KAAK,UAAU,YAAY;AACzC,UAAM,mBAAmB,KAAK,eAAe,QAAQ;AACrD,UAAM,QAA+B,CAAA;AACrC,UAAM,SAAgC,CAAA;AAEtC,SAAK,IAAI,OAAO,CAAC,KAAa,cAAyB;AACrD,UAAI,CAAC,iBAAiB,GAAG,GAAG;AAC1B,eAAO,GAAG,IAAI;;IAElB,CAAC;AAED,SAAK,IAAI,kBAAkB,CAAC,KAAK,iBAA4B;AAC3D,YAAM,mBAA+B,MAAM,GAAG;AAE9C,UAAI,kBAAkB;AACpB,cAAM,kBAAkB,aAAa,IACnC,CAAC,MAAgB,EAAE,YAAY;AAEjC,cAAM,kBAAkB,iBAAiB,IACvC,CAAC,MAAgB,EAAE,YAAY;AAEjC,cAAM,kBAA8B,aAAa,OAC/C,CAAC,MAAgB,gBAAgB,QAAQ,EAAE,YAAY,IAAI,CAAC;AAE9D,cAAM,gBAA4B,iBAAiB,OACjD,CAAC,MAAgB,gBAAgB,QAAQ,EAAE,YAAY,IAAI,CAAC;AAG9D,YAAI,gBAAgB,SAAS,GAAG;AAC9B,gBAAM,GAAG,IAAI;;AAGf,YAAI,cAAc,SAAS,GAAG;AAC5B,iBAAO,GAAG,IAAI;;aAEX;AACL,cAAM,GAAG,IAAI;;IAEjB,CAAC;AAED,WAAO,KAAK,SAAS,OAAO,EAAE,OAAO,OAAM,GAAI,QAAQ,OAAO;EAChE;;;;;;;;;;;EAYQ,OAAO,SACb,OACA,MACA,QACA,SAAgC;AAEhC,UAAM,EAAE,OAAO,OAAM,IAAK;MACxB,OAAO,KAAK,eAAe,KAAK,KAAK;MACrC,QAAQ,KAAK,eAAe,KAAK,MAAM;;AAGzC,QAAI,CAAC,QAAQ;AACX,eAAS,MAAK;MAAE;;AAGlB,QAAI,CAAC,SAAS;AACZ,gBAAU,MAAK;MAAE;;AAGnB,SAAK,IAAI,OAAO,CAAC,KAAK,iBAA4B;;AAChD,YAAM,oBAA+B,KAAA,MAAM,GAAG,OAAC,QAAA,OAAA,SAAA,KAAI,CAAA;AACnD,YAAM,GAAG,IAAI,KAAK,UAAU,YAAY;AAExC,UAAI,iBAAiB,SAAS,GAAG;AAC/B,cAAM,qBAAqB,MAAM,GAAG,EAAE,IACpC,CAAC,MAAgB,EAAE,YAAY;AAEjC,cAAM,eAA2B,iBAAiB,OAChD,CAAC,MAAgB,mBAAmB,QAAQ,EAAE,YAAY,IAAI,CAAC;AAGjE,cAAM,GAAG,EAAE,QAAQ,GAAG,YAAY;;AAGpC,aAAO,KAAK,kBAAkB,YAAY;IAC5C,CAAC;AAED,SAAK,IAAI,QAAQ,CAAC,KAAK,kBAA6B;AAClD,UAAI,mBAA+B,MAAM,GAAG;AAE5C,UAAI,CAAC;AAAkB;AAEvB,YAAM,uBAAuB,cAAc,IACzC,CAAC,MAAgB,EAAE,YAAY;AAEjC,yBAAmB,iBAAiB,OAClC,CAAC,MAAgB,qBAAqB,QAAQ,EAAE,YAAY,IAAI,CAAC;AAGnE,YAAM,GAAG,IAAI;AAEb,cAAQ,KAAK,kBAAkB,aAAa;AAE5C,UAAI,iBAAiB,WAAW;AAAG,eAAO,MAAM,GAAG;IACrD,CAAC;AAED,WAAO;EACT;;EAGQ,OAAO,IACb,KACA,MAAwB;AAExB,WAAO,OAAO,oBAAoB,GAAG,EAAE,IAAI,CAAC,QAAQ,KAAK,KAAK,IAAI,GAAG,CAAC,CAAC;EACzE;;;;;;;;;;;;;;;;;;;;;;;;EAyBQ,OAAO,eACb,OAA+C;AAE/C,YAAQ,KAAK,UAAU,KAAK;AAE5B,WAAO,OAAO,oBAAoB,KAAK,EAAE,OAAO,CAAC,UAAU,QAAO;AAChE,YAAM,YAAY,MAAM,GAAG;AAE3B,UAAI,WAAW,WAAW;AACxB,iBAAS,GAAG,IAAI,UAAU,MAAM,IAAI,CAAC,aAAY;AAC/C,mBAAS,cAAc,IAAI,SAAS,SAAS;AAE7C,iBAAO,SAAS,SAAS;AACzB,iBAAO,SAAS,cAAc;AAE9B,iBAAO;QACT,CAAC;aACI;AACL,iBAAS,GAAG,IAAI;;AAGlB,aAAO;IACT,GAAG,CAAA,CAA2B;EAChC;;EAGQ,OAAO,UAAU,KAA2B;AAClD,WAAO,KAAK,MAAM,KAAK,UAAU,GAAG,CAAC;EACvC;;EAGQ,OAAO,UAAgC;AAC7C,SAAK,OAAO,SAAS;EACvB;;EAGQ,QAAQ,UAAiC;AAC/C,SAAK,OAAO,UAAU;EACxB;;EAGQ,OAAO,UAAoB;AACjC,SAAK,OAAO,SAAS;EACvB;;EAGQ,qBAAkB;AACxB,WAAO,CAAC,KAAK,WAAW,KAAK,YAAY,KAAK,QAAQ,SAAQ;EAChE;;;;ACjRF,IAAY;CAAZ,SAAYC,yCAAsC;AAChD,EAAAA,wCAAA,KAAA,IAAA;AACA,EAAAA,wCAAA,QAAA,IAAA;AACA,EAAAA,wCAAA,QAAA,IAAA;AACA,EAAAA,wCAAA,QAAA,IAAA;AACF,GALY,2CAAA,yCAAsC,CAAA,EAAA;AAOlD,IAAY;CAAZ,SAAYC,wBAAqB;AAC/B,EAAAA,uBAAA,WAAA,IAAA;AACA,EAAAA,uBAAA,UAAA,IAAA;AAIA,EAAAA,uBAAA,kBAAA,IAAA;AACF,GAPY,0BAAA,wBAAqB,CAAA,EAAA;AASjC,IAAY;CAAZ,SAAYC,4BAAyB;AACnC,EAAAA,2BAAA,YAAA,IAAA;AACA,EAAAA,2BAAA,WAAA,IAAA;AACA,EAAAA,2BAAA,QAAA,IAAA;AACA,EAAAA,2BAAA,eAAA,IAAA;AACF,GALY,8BAAA,4BAAyB,CAAA,EAAA;AAO9B,IAAM,0BAA0B;AAOvC,IAAqB,kBAArB,MAAqB,iBAAe;EAmBlC,YAES,OACA,SAAiC,EAAE,QAAQ,CAAA,EAAE,GAC7C,QAAsB;AAFtB,SAAA,QAAA;AACA,SAAA,SAAA;AACA,SAAA,SAAA;AAtBT,SAAA,WAOI,CAAA;AAEJ,SAAA,QAAQ,eAAe;AACvB,SAAA,aAAa;AAGb,SAAA,aAAqB,CAAA;AAWnB,SAAK,WAAW,MAAM,QAAQ,eAAe,EAAE;AAC/C,SAAK,OAAO,SAAM,OAAA,OACb;MACD,WAAW,EAAE,KAAK,OAAO,MAAM,MAAK;MACpC,UAAU,EAAE,KAAK,GAAE;MACnB,SAAS;OAER,OAAO,MAAM;AAElB,SAAK,UAAU,KAAK,OAAO;AAC3B,SAAK,WAAW,IAAI,KAClB,MACA,eAAe,MACf,KAAK,QACL,KAAK,OAAO;AAEd,SAAK,cAAc,IAAI,MACrB,MAAM,KAAK,sBAAqB,GAChC,KAAK,OAAO,gBAAgB;AAE9B,SAAK,SAAS,QAAQ,MAAM,MAAK;AAC/B,WAAK,QAAQ,eAAe;AAC5B,WAAK,YAAY,MAAK;AACtB,WAAK,WAAW,QAAQ,CAAC,cAAoB,UAAU,KAAI,CAAE;AAC7D,WAAK,aAAa,CAAA;IACpB,CAAC;AACD,SAAK,SAAS,MAAK;AACjB,WAAK,YAAY,MAAK;AACtB,WAAK,OAAO,IAAI,WAAW,SAAS,KAAK,KAAK,IAAI,KAAK,SAAQ,CAAE,EAAE;AACnE,WAAK,QAAQ,eAAe;AAC5B,WAAK,OAAO,QAAQ,IAAI;IAC1B,CAAC;AACD,SAAK,SAAS,CAAC,WAAkB;AAC/B,UAAI,KAAK,WAAU,KAAM,KAAK,UAAS,GAAI;AACzC;;AAEF,WAAK,OAAO,IAAI,WAAW,SAAS,KAAK,KAAK,IAAI,MAAM;AACxD,WAAK,QAAQ,eAAe;AAC5B,WAAK,YAAY,gBAAe;IAClC,CAAC;AACD,SAAK,SAAS,QAAQ,WAAW,MAAK;AACpC,UAAI,CAAC,KAAK,WAAU,GAAI;AACtB;;AAEF,WAAK,OAAO,IAAI,WAAW,WAAW,KAAK,KAAK,IAAI,KAAK,SAAS,OAAO;AACzE,WAAK,QAAQ,eAAe;AAC5B,WAAK,YAAY,gBAAe;IAClC,CAAC;AACD,SAAK,IAAI,eAAe,OAAO,CAAA,GAAI,CAAC,SAAc,QAAe;AAC/D,WAAK,SAAS,KAAK,gBAAgB,GAAG,GAAG,OAAO;IAClD,CAAC;AAED,SAAK,WAAW,IAAI,iBAAiB,IAAI;AAEzC,SAAK,uBACH,gBAAgB,KAAK,OAAO,QAAQ,IAAI;EAC5C;;EAGA,UACE,UACA,UAAU,KAAK,SAAO;;AAEtB,QAAI,CAAC,KAAK,OAAO,YAAW,GAAI;AAC9B,WAAK,OAAO,QAAO;;AAGrB,QAAI,KAAK,YAAY;AACnB,YAAM;WACD;AACL,YAAM,EACJ,QAAQ,EAAE,WAAW,UAAU,SAAS,UAAS,EAAE,IACjD,KAAK;AACT,WAAK,SAAS,CAAC,MAAa,YAAY,SAAS,iBAAiB,CAAC,CAAC;AACpE,WAAK,SAAS,MAAM,YAAY,SAAS,QAAQ,CAAC;AAElD,YAAM,qBAAgD,CAAA;AACtD,YAAM,SAAS;QACb;QACA;QACA,mBACE,MAAA,KAAA,KAAK,SAAS,sBAAgB,QAAA,OAAA,SAAA,SAAA,GAAE,IAAI,CAAC,MAAM,EAAE,MAAM,OAAC,QAAA,OAAA,SAAA,KAAI,CAAA;QAC1D,SAAS;;AAGX,UAAI,KAAK,OAAO,aAAa;AAC3B,2BAAmB,eAAe,KAAK,OAAO;;AAGhD,WAAK,kBAAiB,OAAA,OAAM,EAAE,OAAM,GAAO,kBAAkB,CAAA;AAE7D,WAAK,aAAa;AAClB,WAAK,QAAQ,OAAO;AAEpB,WAAK,SACF,QACC,MACA,CAAC,EACC,kBAAkB,sBAAqB,MASpC;;AACH,aAAK,OAAO,eACV,KAAK,OAAO,QAAQ,KAAK,OAAO,WAAW;AAE7C,YAAI,0BAA0B,QAAW;AACvC,sBAAY,SAAS,YAAY;AACjC;eACK;AACL,gBAAM,yBAAyB,KAAK,SAAS;AAC7C,gBAAM,eAAcC,MAAA,2BAAsB,QAAtB,2BAAsB,SAAA,SAAtB,uBAAwB,YAAM,QAAAA,QAAA,SAAAA,MAAI;AACtD,gBAAM,sBAAsB,CAAA;AAE5B,mBAAS,IAAI,GAAG,IAAI,aAAa,KAAK;AACpC,kBAAM,wBAAwB,uBAAuB,CAAC;AACtD,kBAAM,EACJ,QAAQ,EAAE,OAAO,QAAQ,OAAO,OAAM,EAAE,IACtC;AACJ,kBAAM,uBACJ,yBAAyB,sBAAsB,CAAC;AAElD,gBACE,wBACA,qBAAqB,UAAU,SAC/B,qBAAqB,WAAW,UAChC,qBAAqB,UAAU,SAC/B,qBAAqB,WAAW,QAChC;AACA,kCAAoB,KAAI,OAAA,OAAA,OAAA,OAAA,CAAA,GACnB,qBAAqB,GAAA,EACxB,IAAI,qBAAqB,GAAE,CAAA,CAAA;mBAExB;AACL,mBAAK,YAAW;AAChB,0BACE,SACE,iBACA,IAAI,MACF,kEAAkE,CACnE;AAEL;;;AAIJ,eAAK,SAAS,mBAAmB;AAEjC,sBAAY,SAAS,YAAY;AACjC;;MAEJ,CAAC,EAEF,QAAQ,SAAS,CAAC,UAAiC;AAClD,oBACE,SACE,iBACA,IAAI,MACF,KAAK,UAAU,OAAO,OAAO,KAAK,EAAE,KAAK,IAAI,KAAK,OAAO,CAAC,CAC3D;AAEL;MACF,CAAC,EACA,QAAQ,WAAW,MAAK;AACvB,oBAAY,SAAS,WAAW;AAChC;MACF,CAAC;;AAGL,WAAO;EACT;EAEA,gBAAa;AAGX,WAAO,KAAK,SAAS;EACvB;EAEA,MAAM,MACJ,SACA,OAA+B,CAAA,GAAE;AAEjC,WAAO,MAAM,KAAK,KAChB;MACE,MAAM;MACN,OAAO;MACP;OAEF,KAAK,WAAW,KAAK,OAAO;EAEhC;EAEA,MAAM,QACJ,OAA+B,CAAA,GAAE;AAEjC,WAAO,MAAM,KAAK,KAChB;MACE,MAAM;MACN,OAAO;OAET,IAAI;EAER;EAgEA,GACE,MACA,QACA,UAAgC;AAEhC,WAAO,KAAK,IAAI,MAAM,QAAQ,QAAQ;EACxC;;;;;;;;;;EAUA,MAAM,KACJ,MAMA,OAA+B,CAAA,GAAE;;AAEjC,QAAI,CAAC,KAAK,SAAQ,KAAM,KAAK,SAAS,aAAa;AACjD,YAAM,EAAE,OAAO,SAAS,iBAAgB,IAAK;AAC7C,YAAM,UAAU;QACd,QAAQ;QACR,SAAS;UACP,eAAe,KAAK,OAAO,cACvB,UAAU,KAAK,OAAO,WAAW,KACjC;UACJ,QAAQ,KAAK,OAAO,SAAS,KAAK,OAAO,SAAS;UAClD,gBAAgB;;QAElB,MAAM,KAAK,UAAU;UACnB,UAAU;YACR,EAAE,OAAO,KAAK,UAAU,OAAO,SAAS,iBAAgB;;SAE3D;;AAGH,UAAI;AACF,cAAM,WAAW,MAAM,KAAK,kBAC1B,KAAK,sBACL,UACA,KAAA,KAAK,aAAO,QAAA,OAAA,SAAA,KAAI,KAAK,OAAO;AAG9B,gBAAM,KAAA,SAAS,UAAI,QAAA,OAAA,SAAA,SAAA,GAAE,OAAM;AAC3B,eAAO,SAAS,KAAK,OAAO;eACrB,OAAY;AACnB,YAAI,MAAM,SAAS,cAAc;AAC/B,iBAAO;eACF;AACL,iBAAO;;;WAGN;AACL,aAAO,IAAI,QAAQ,CAAC,YAAW;;AAC7B,cAAM,OAAO,KAAK,MAAM,KAAK,MAAM,MAAM,KAAK,WAAW,KAAK,OAAO;AAErE,YAAI,KAAK,SAAS,eAAe,GAAC,MAAAC,OAAAD,MAAA,KAAK,YAAM,QAAAA,QAAA,SAAA,SAAAA,IAAE,YAAM,QAAAC,QAAA,SAAA,SAAAA,IAAE,eAAS,QAAA,OAAA,SAAA,SAAA,GAAE,MAAK;AACrE,kBAAQ,IAAI;;AAGd,aAAK,QAAQ,MAAM,MAAM,QAAQ,IAAI,CAAC;AACtC,aAAK,QAAQ,SAAS,MAAM,QAAQ,OAAO,CAAC;AAC5C,aAAK,QAAQ,WAAW,MAAM,QAAQ,WAAW,CAAC;MACpD,CAAC;;EAEL;EAEA,kBAAkB,SAA+B;AAC/C,SAAK,SAAS,cAAc,OAAO;EACrC;;;;;;;;;;EAWA,YAAY,UAAU,KAAK,SAAO;AAChC,SAAK,QAAQ,eAAe;AAC5B,UAAM,UAAU,MAAK;AACnB,WAAK,OAAO,IAAI,WAAW,SAAS,KAAK,KAAK,EAAE;AAChD,WAAK,SAAS,eAAe,OAAO,SAAS,KAAK,SAAQ,CAAE;IAC9D;AAEA,SAAK,YAAY,MAAK;AAEtB,SAAK,SAAS,QAAO;AAErB,WAAO,IAAI,QAAQ,CAAC,YAAW;AAC7B,YAAM,YAAY,IAAI,KAAK,MAAM,eAAe,OAAO,CAAA,GAAI,OAAO;AAElE,gBACG,QAAQ,MAAM,MAAK;AAClB,gBAAO;AACP,gBAAQ,IAAI;MACd,CAAC,EACA,QAAQ,WAAW,MAAK;AACvB,gBAAO;AACP,gBAAQ,WAAW;MACrB,CAAC,EACA,QAAQ,SAAS,MAAK;AACrB,gBAAQ,OAAO;MACjB,CAAC;AAEH,gBAAU,KAAI;AAEd,UAAI,CAAC,KAAK,SAAQ,GAAI;AACpB,kBAAU,QAAQ,MAAM,CAAA,CAAE;;IAE9B,CAAC;EACH;;EAIA,MAAM,kBACJ,KACA,SACA,SAAe;AAEf,UAAM,aAAa,IAAI,gBAAe;AACtC,UAAM,KAAK,WAAW,MAAM,WAAW,MAAK,GAAI,OAAO;AAEvD,UAAM,WAAW,MAAM,KAAK,OAAO,MAAM,KAAG,OAAA,OAAA,OAAA,OAAA,CAAA,GACvC,OAAO,GAAA,EACV,QAAQ,WAAW,OAAM,CAAA,CAAA;AAG3B,iBAAa,EAAE;AAEf,WAAO;EACT;;EAGA,MACE,OACA,SACA,UAAU,KAAK,SAAO;AAEtB,QAAI,CAAC,KAAK,YAAY;AACpB,YAAM,kBAAkB,KAAK,SAAS,KAAK,KAAK;;AAElD,QAAI,YAAY,IAAI,KAAK,MAAM,OAAO,SAAS,OAAO;AACtD,QAAI,KAAK,SAAQ,GAAI;AACnB,gBAAU,KAAI;WACT;AACL,gBAAU,aAAY;AACtB,WAAK,WAAW,KAAK,SAAS;;AAGhC,WAAO;EACT;;;;;;;;;EAUA,WAAW,QAAgB,SAAc,MAAa;AACpD,WAAO;EACT;;EAGA,UAAU,OAAa;AACrB,WAAO,KAAK,UAAU;EACxB;;EAGA,WAAQ;AACN,WAAO,KAAK,SAAS;EACvB;;EAGA,SAAS,MAAc,SAAe,KAAY;;AAChD,UAAM,YAAY,KAAK,kBAAiB;AACxC,UAAM,EAAE,OAAO,OAAO,OAAO,KAAI,IAAK;AACtC,UAAM,SAAmB,CAAC,OAAO,OAAO,OAAO,IAAI;AACnD,QAAI,OAAO,OAAO,QAAQ,SAAS,KAAK,KAAK,QAAQ,KAAK,SAAQ,GAAI;AACpE;;AAEF,QAAI,iBAAiB,KAAK,WAAW,WAAW,SAAS,GAAG;AAC5D,QAAI,WAAW,CAAC,gBAAgB;AAC9B,YAAM;;AAGR,QAAI,CAAC,UAAU,UAAU,QAAQ,EAAE,SAAS,SAAS,GAAG;AACtD,OAAA,KAAA,KAAK,SAAS,sBAAgB,QAAA,OAAA,SAAA,SAAA,GAC1B,OAAO,CAAC,SAAQ;;AAChB,iBACED,MAAA,KAAK,YAAM,QAAAA,QAAA,SAAA,SAAAA,IAAE,WAAU,SACvB,MAAAC,MAAA,KAAK,YAAM,QAAAA,QAAA,SAAA,SAAAA,IAAE,WAAK,QAAA,OAAA,SAAA,SAAA,GAAE,kBAAiB,OAAO;MAEhD,CAAC,EACA,IAAI,CAAC,SAAS,KAAK,SAAS,gBAAgB,GAAG,CAAC;WAC9C;AACL,OAAA,KAAA,KAAK,SAAS,SAAS,OAAC,QAAA,OAAA,SAAA,SAAA,GACpB,OAAO,CAAC,SAAQ;;AAChB,YACE,CAAC,aAAa,YAAY,kBAAkB,EAAE,SAAS,SAAS,GAChE;AACA,cAAI,QAAQ,MAAM;AAChB,kBAAM,SAAS,KAAK;AACpB,kBAAM,aAAYD,MAAA,KAAK,YAAM,QAAAA,QAAA,SAAA,SAAAA,IAAE;AAC/B,mBACE,YACAC,MAAA,QAAQ,SAAG,QAAAA,QAAA,SAAA,SAAAA,IAAE,SAAS,MAAM,OAC3B,cAAc,QACb,cAAS,QAAT,cAAS,SAAA,SAAT,UAAW,kBAAiB,SAC1B,KAAA,QAAQ,UAAI,QAAA,OAAA,SAAA,SAAA,GAAE,KAAK,kBAAiB;iBAErC;AACL,kBAAM,aAAY,MAAA,KAAA,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM,YAAM,QAAA,OAAA,SAAA,SAAA,GAAE,WAAK,QAAA,OAAA,SAAA,SAAA,GAAE,kBAAiB;AACxD,mBACE,cAAc,OACd,gBAAc,KAAA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,WAAK,QAAA,OAAA,SAAA,SAAA,GAAE,kBAAiB;;eAG9C;AACL,iBAAO,KAAK,KAAK,kBAAiB,MAAO;;MAE7C,CAAC,EACA,IAAI,CAAC,SAAQ;AACZ,YAAI,OAAO,mBAAmB,YAAY,SAAS,gBAAgB;AACjE,gBAAM,kBAAkB,eAAe;AACvC,gBAAM,EAAE,QAAQ,OAAO,kBAAkB,MAAAC,OAAM,OAAM,IACnD;AACF,gBAAM,kBAAkB;YACtB;YACA;YACA;YACA,WAAWA;YACX,KAAK,CAAA;YACL,KAAK,CAAA;YACL;;AAEF,2BAAc,OAAA,OAAA,OAAA,OAAA,CAAA,GACT,eAAe,GACf,KAAK,mBAAmB,eAAe,CAAC;;AAG/C,aAAK,SAAS,gBAAgB,GAAG;MACnC,CAAC;;EAEP;;EAGA,YAAS;AACP,WAAO,KAAK,UAAU,eAAe;EACvC;;EAGA,YAAS;AACP,WAAO,KAAK,UAAU,eAAe;EACvC;;EAGA,aAAU;AACR,WAAO,KAAK,UAAU,eAAe;EACvC;;EAGA,aAAU;AACR,WAAO,KAAK,UAAU,eAAe;EACvC;;EAGA,gBAAgB,KAAW;AACzB,WAAO,cAAc,GAAG;EAC1B;;EAGA,IAAI,MAAc,QAAgC,UAAkB;AAClE,UAAM,YAAY,KAAK,kBAAiB;AAExC,UAAM,UAAU;MACd,MAAM;MACN;MACA;;AAGF,QAAI,KAAK,SAAS,SAAS,GAAG;AAC5B,WAAK,SAAS,SAAS,EAAE,KAAK,OAAO;WAChC;AACL,WAAK,SAAS,SAAS,IAAI,CAAC,OAAO;;AAGrC,WAAO;EACT;;EAGA,KAAK,MAAc,QAA8B;AAC/C,UAAM,YAAY,KAAK,kBAAiB;AAExC,SAAK,SAAS,SAAS,IAAI,KAAK,SAAS,SAAS,EAAE,OAAO,CAAC,SAAQ;;AAClE,aAAO,IACL,KAAA,KAAK,UAAI,QAAA,OAAA,SAAA,SAAA,GAAE,kBAAiB,OAAO,aACnC,iBAAgB,QAAQ,KAAK,QAAQ,MAAM;IAE/C,CAAC;AACD,WAAO;EACT;;EAGQ,OAAO,QACb,MACA,MAA+B;AAE/B,QAAI,OAAO,KAAK,IAAI,EAAE,WAAW,OAAO,KAAK,IAAI,EAAE,QAAQ;AACzD,aAAO;;AAGT,eAAW,KAAK,MAAM;AACpB,UAAI,KAAK,CAAC,MAAM,KAAK,CAAC,GAAG;AACvB,eAAO;;;AAIX,WAAO;EACT;;EAGQ,wBAAqB;AAC3B,SAAK,YAAY,gBAAe;AAChC,QAAI,KAAK,OAAO,YAAW,GAAI;AAC7B,WAAK,QAAO;;EAEhB;;;;;;EAOQ,SAAS,UAAkB;AACjC,SAAK,IAAI,eAAe,OAAO,CAAA,GAAI,QAAQ;EAC7C;;;;;;EAOQ,SAAS,UAAkB;AACjC,SAAK,IAAI,eAAe,OAAO,CAAA,GAAI,CAAC,WAAmB,SAAS,MAAM,CAAC;EACzE;;;;;;EAOQ,WAAQ;AACd,WAAO,KAAK,OAAO,YAAW,KAAM,KAAK,UAAS;EACpD;;EAGQ,QAAQ,UAAU,KAAK,SAAO;AACpC,QAAI,KAAK,WAAU,GAAI;AACrB;;AAEF,SAAK,OAAO,gBAAgB,KAAK,KAAK;AACtC,SAAK,QAAQ,eAAe;AAC5B,SAAK,SAAS,OAAO,OAAO;EAC9B;;EAGQ,mBAAmB,SAAY;AACrC,UAAM,UAAU;MACd,KAAK,CAAA;MACL,KAAK,CAAA;;AAGP,QAAI,QAAQ,SAAS,YAAY,QAAQ,SAAS,UAAU;AAC1D,cAAQ,MAAmB,kBACzB,QAAQ,SACR,QAAQ,MAAM;;AAIlB,QAAI,QAAQ,SAAS,YAAY,QAAQ,SAAS,UAAU;AAC1D,cAAQ,MAAmB,kBACzB,QAAQ,SACR,QAAQ,UAAU;;AAItB,WAAO;EACT;;;;AC1vBF,IAAMC,QAAO,MAAK;AAAE;AAkBpB,IAAM,6BAA6B,OAAO,cAAc;AAExD,IAAqB,iBAArB,MAAmC;;;;;;;;;;;;;;;;EAkDjC,YAAY,UAAkB,SAA+B;;AAjD7D,SAAA,cAA6B;AAC7B,SAAA,SAAwB;AACxB,SAAA,WAA8B,CAAA;AAC9B,SAAA,WAAmB;AACnB,SAAA,eAAuB;AACvB,SAAA,UAAsC;AACtC,SAAA,SAAqC,CAAA;AACrC,SAAA,UAAkB;AAElB,SAAA,sBAA8B;AAC9B,SAAA,iBAA6D;AAC7D,SAAA,sBAAqC;AACrC,SAAA,MAAc;AAEd,SAAA,SAAmBA;AAInB,SAAA,OAA6B;AAC7B,SAAA,aAAyB,CAAA;AACzB,SAAA,aAAyB,IAAI,WAAU;AACvC,SAAA,uBAKI;MACF,MAAM,CAAA;MACN,OAAO,CAAA;MACP,OAAO,CAAA;MACP,SAAS,CAAA;;AAwOX,SAAA,gBAAgB,CAAC,gBAA8B;AAC7C,UAAI;AACJ,UAAI,aAAa;AACf,iBAAS;iBACA,OAAO,UAAU,aAAa;AACvC,iBAAS,IAAI,SACX,OAAO,uBAA6B,EAAE,KAAK,CAAC,EAAE,SAASC,OAAK,MAC1DA,OAAM,GAAG,IAAI,CAAC;aAEb;AACL,iBAAS;;AAEX,aAAO,IAAI,SAAS,OAAO,GAAG,IAAI;IACpC;AAjOE,SAAK,WAAW,GAAG,QAAQ,IAAI,WAAW,SAAS;AACnD,SAAK,eAAe,gBAAgB,QAAQ;AAC5C,QAAI,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,WAAW;AACtB,WAAK,YAAY,QAAQ;WACpB;AACL,WAAK,YAAY;;AAEnB,QAAI,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS;AAAQ,WAAK,SAAS,QAAQ;AAC3C,QAAI,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS;AAAS,WAAK,UAAO,OAAA,OAAA,OAAA,OAAA,CAAA,GAAQ,KAAK,OAAO,GAAK,QAAQ,OAAO;AAC1E,QAAI,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS;AAAS,WAAK,UAAU,QAAQ;AAC7C,QAAI,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS;AAAQ,WAAK,SAAS,QAAQ;AAC3C,QAAI,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS;AACX,WAAK,sBAAsB,QAAQ;AAErC,UAAM,eAAc,KAAA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,YAAM,QAAA,OAAA,SAAA,SAAA,GAAE;AACrC,QAAI,aAAa;AACf,WAAK,cAAc;AACnB,WAAK,SAAS;;AAGhB,SAAK,oBAAmB,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,oBAC7B,QAAQ,mBACR,CAAC,UAAiB;AAChB,aAAO,CAAC,KAAM,KAAM,KAAM,GAAK,EAAE,QAAQ,CAAC,KAAK;IACjD;AACJ,SAAK,UAAS,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,UACnB,QAAQ,SACR,CAAC,SAAe,aAAsB;AACpC,aAAO,SAAS,KAAK,UAAU,OAAO,CAAC;IACzC;AACJ,SAAK,UAAS,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,UACnB,QAAQ,SACR,KAAK,WAAW,OAAO,KAAK,KAAK,UAAU;AAC/C,SAAK,iBAAiB,IAAI,MAAM,YAAW;AACzC,WAAK,WAAU;AACf,WAAK,QAAO;IACd,GAAG,KAAK,gBAAgB;AAExB,SAAK,QAAQ,KAAK,cAAc,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,KAAK;EAChD;;;;EAKA,UAAO;AACL,QAAI,KAAK,MAAM;AACb;;AAGF,QAAI,KAAK,WAAW;AAClB,WAAK,OAAO,IAAI,KAAK,UAAU,KAAK,aAAY,GAAI,QAAW;QAC7D,SAAS,KAAK;OACf;AACD;;AAGF,QAAI,4BAA4B;AAC9B,WAAK,OAAO,IAAI,UAAU,KAAK,aAAY,CAAE;AAC7C,WAAK,gBAAe;AACpB;;AAGF,SAAK,OAAO,IAAI,iBAAiB,KAAK,aAAY,GAAI,QAAW;MAC/D,OAAO,MAAK;AACV,aAAK,OAAO;MACd;KACD;AAED,WAAO,uBAAI,EAAE,KAAK,CAAC,EAAE,SAAS,GAAE,MAAM;AACpC,WAAK,OAAO,IAAI,GAAG,KAAK,aAAY,GAAI,QAAW;QACjD,SAAS,KAAK;OACf;AACD,WAAK,gBAAe;IACtB,CAAC;EACH;;;;;;;EAQA,WAAW,MAAe,QAAe;AACvC,QAAI,KAAK,MAAM;AACb,WAAK,KAAK,UAAU,WAAA;MAAa;AACjC,UAAI,MAAM;AACR,aAAK,KAAK,MAAM,MAAM,WAAM,QAAN,WAAM,SAAN,SAAU,EAAE;aAC7B;AACL,aAAK,KAAK,MAAK;;AAEjB,WAAK,OAAO;AAEZ,WAAK,kBAAkB,cAAc,KAAK,cAAc;AACxD,WAAK,eAAe,MAAK;;EAE7B;;;;EAKA,cAAW;AACT,WAAO,KAAK;EACd;;;;;EAMA,MAAM,cACJ,SAAwB;AAExB,UAAM,SAAS,MAAM,QAAQ,YAAW;AACxC,QAAI,KAAK,SAAS,WAAW,GAAG;AAC9B,WAAK,WAAU;;AAEjB,WAAO;EACT;;;;EAKA,MAAM,oBAAiB;AACrB,UAAM,WAAW,MAAM,QAAQ,IAC7B,KAAK,SAAS,IAAI,CAAC,YAAY,QAAQ,YAAW,CAAE,CAAC;AAEvD,SAAK,WAAU;AACf,WAAO;EACT;;;;;;EAOA,IAAI,MAAc,KAAa,MAAU;AACvC,SAAK,OAAO,MAAM,KAAK,IAAI;EAC7B;;;;EAKA,kBAAe;AACb,YAAQ,KAAK,QAAQ,KAAK,KAAK,YAAY;MACzC,KAAK,cAAc;AACjB,eAAO,iBAAiB;MAC1B,KAAK,cAAc;AACjB,eAAO,iBAAiB;MAC1B,KAAK,cAAc;AACjB,eAAO,iBAAiB;MAC1B;AACE,eAAO,iBAAiB;;EAE9B;;;;EAKA,cAAW;AACT,WAAO,KAAK,gBAAe,MAAO,iBAAiB;EACrD;EAEA,QACE,OACA,SAAiC,EAAE,QAAQ,CAAA,EAAE,GAAE;AAE/C,UAAM,OAAO,IAAI,gBAAgB,YAAY,KAAK,IAAI,QAAQ,IAAI;AAClE,SAAK,SAAS,KAAK,IAAI;AACvB,WAAO;EACT;;;;;;EAOA,KAAK,MAAqB;AACxB,UAAM,EAAE,OAAO,OAAO,SAAS,IAAG,IAAK;AACvC,UAAM,WAAW,MAAK;AACpB,WAAK,OAAO,MAAM,CAAC,WAAe;;AAChC,SAAA,KAAA,KAAK,UAAI,QAAA,OAAA,SAAA,SAAA,GAAE,KAAK,MAAM;MACxB,CAAC;IACH;AACA,SAAK,IAAI,QAAQ,GAAG,KAAK,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO;AACtD,QAAI,KAAK,YAAW,GAAI;AACtB,eAAQ;WACH;AACL,WAAK,WAAW,KAAK,QAAQ;;EAEjC;;;;;;EAOA,QAAQ,OAAoB;AAC1B,SAAK,cAAc;AAEnB,SAAK,SAAS,QAAQ,CAAC,YAAW;AAChC,eAAS,QAAQ,kBAAkB,EAAE,cAAc,MAAK,CAAE;AAE1D,UAAI,QAAQ,cAAc,QAAQ,UAAS,GAAI;AAC7C,gBAAQ,MAAM,eAAe,cAAc,EAAE,cAAc,MAAK,CAAE;;IAEtE,CAAC;EACH;;;;;;EA2BA,WAAQ;AACN,QAAI,SAAS,KAAK,MAAM;AACxB,QAAI,WAAW,KAAK,KAAK;AACvB,WAAK,MAAM;WACN;AACL,WAAK,MAAM;;AAGb,WAAO,KAAK,IAAI,SAAQ;EAC1B;;;;;;EAOA,gBAAgB,OAAa;AAC3B,QAAI,aAAa,KAAK,SAAS,KAC7B,CAAC,MAAM,EAAE,UAAU,UAAU,EAAE,UAAS,KAAM,EAAE,WAAU,EAAG;AAE/D,QAAI,YAAY;AACd,WAAK,IAAI,aAAa,4BAA4B,KAAK,GAAG;AAC1D,iBAAW,YAAW;;EAE1B;;;;;;;;EASA,QAAQ,SAAwB;AAC9B,SAAK,WAAW,KAAK,SAAS,OAC5B,CAAC,MAAuB,EAAE,SAAQ,MAAO,QAAQ,SAAQ,CAAE;EAE/D;;;;;;EAOQ,kBAAe;AACrB,QAAI,KAAK,MAAM;AACb,WAAK,KAAK,aAAa;AACvB,WAAK,KAAK,SAAS,MAAM,KAAK,YAAW;AACzC,WAAK,KAAK,UAAU,CAAC,UACnB,KAAK,aAAa,KAA2B;AAC/C,WAAK,KAAK,YAAY,CAAC,UAAe,KAAK,eAAe,KAAK;AAC/D,WAAK,KAAK,UAAU,CAAC,UAAe,KAAK,aAAa,KAAK;;EAE/D;;;;;;EAOQ,eAAY;AAClB,WAAO,KAAK,cACV,KAAK,UACL,OAAO,OAAO,CAAA,GAAI,KAAK,QAAQ,EAAE,KAAK,IAAG,CAAE,CAAC;EAEhD;;EAGQ,eAAe,YAAyB;AAC9C,SAAK,OAAO,WAAW,MAAM,CAAC,QAAwB;AACpD,UAAI,EAAE,OAAO,OAAO,SAAS,IAAG,IAAK;AAErC,UACG,OAAO,QAAQ,KAAK,uBACrB,WAAU,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,OACnB;AACA,aAAK,sBAAsB;;AAG7B,WAAK,IACH,WACA,GAAG,QAAQ,UAAU,EAAE,IAAI,KAAK,IAAI,KAAK,IACtC,OAAO,MAAM,MAAM,OAAQ,EAC9B,IACA,OAAO;AAET,WAAK,SACF,OAAO,CAAC,YAA6B,QAAQ,UAAU,KAAK,CAAC,EAC7D,QAAQ,CAAC,YACR,QAAQ,SAAS,OAAO,SAAS,GAAG,CAAC;AAEzC,WAAK,qBAAqB,QAAQ,QAAQ,CAAC,aAAa,SAAS,GAAG,CAAC;IACvE,CAAC;EACH;;EAGQ,cAAW;AACjB,SAAK,IAAI,aAAa,gBAAgB,KAAK,aAAY,CAAE,EAAE;AAC3D,SAAK,iBAAgB;AACrB,SAAK,eAAe,MAAK;AACzB,SAAK,kBAAkB,cAAc,KAAK,cAAc;AACxD,SAAK,iBAAiB,YACpB,MAAM,KAAK,eAAc,GACzB,KAAK,mBAAmB;AAE1B,SAAK,qBAAqB,KAAK,QAAQ,CAAC,aAAa,SAAQ,CAAE;EACjE;;EAGQ,aAAa,OAAU;AAC7B,SAAK,IAAI,aAAa,SAAS,KAAK;AACpC,SAAK,kBAAiB;AACtB,SAAK,kBAAkB,cAAc,KAAK,cAAc;AACxD,SAAK,eAAe,gBAAe;AACnC,SAAK,qBAAqB,MAAM,QAAQ,CAAC,aAAa,SAAS,KAAK,CAAC;EACvE;;EAGQ,aAAa,OAAyB;AAC5C,SAAK,IAAI,aAAa,MAAM,OAAO;AACnC,SAAK,kBAAiB;AACtB,SAAK,qBAAqB,MAAM,QAAQ,CAAC,aAAa,SAAS,KAAK,CAAC;EACvE;;EAGQ,oBAAiB;AACvB,SAAK,SAAS,QAAQ,CAAC,YACrB,QAAQ,SAAS,eAAe,KAAK,CAAC;EAE1C;;EAGQ,cACN,KACA,QAAiC;AAEjC,QAAI,OAAO,KAAK,MAAM,EAAE,WAAW,GAAG;AACpC,aAAO;;AAET,UAAM,SAAS,IAAI,MAAM,IAAI,IAAI,MAAM;AACvC,UAAM,QAAQ,IAAI,gBAAgB,MAAM;AAExC,WAAO,GAAG,GAAG,GAAG,MAAM,GAAG,KAAK;EAChC;;EAGQ,mBAAgB;AACtB,QAAI,KAAK,YAAW,KAAM,KAAK,WAAW,SAAS,GAAG;AACpD,WAAK,WAAW,QAAQ,CAAC,aAAa,SAAQ,CAAE;AAChD,WAAK,aAAa,CAAA;;EAEtB;;EAEQ,iBAAc;;AACpB,QAAI,CAAC,KAAK,YAAW,GAAI;AACvB;;AAEF,QAAI,KAAK,qBAAqB;AAC5B,WAAK,sBAAsB;AAC3B,WAAK,IACH,aACA,0DAA0D;AAE5D,OAAA,KAAA,KAAK,UAAI,QAAA,OAAA,SAAA,SAAA,GAAE,MAAM,iBAAiB,kBAAkB;AACpD;;AAEF,SAAK,sBAAsB,KAAK,SAAQ;AACxC,SAAK,KAAK;MACR,OAAO;MACP,OAAO;MACP,SAAS,CAAA;MACT,KAAK,KAAK;KACX;AACD,SAAK,QAAQ,KAAK,WAAW;EAC/B;;AAGF,IAAM,mBAAN,MAAsB;EAWpB,YACE,SACA,YACA,SAA4B;AAb9B,SAAA,aAAqB;AAErB,SAAA,UAAoB,MAAK;IAAE;AAC3B,SAAA,UAAoB,MAAK;IAAE;AAC3B,SAAA,YAAsB,MAAK;IAAE;AAC7B,SAAA,SAAmB,MAAK;IAAE;AAC1B,SAAA,aAAqB,cAAc;AACnC,SAAA,OAAiB,MAAK;IAAE;AACxB,SAAA,MAA2B;AAOzB,SAAK,MAAM;AACX,SAAK,QAAQ,QAAQ;EACvB;;;;ACtiBI,IAAO,eAAP,cAA4B,MAAK;EAGrC,YAAY,SAAe;AACzB,UAAM,OAAO;AAHL,SAAA,mBAAmB;AAI3B,SAAK,OAAO;EACd;;AAGI,SAAU,eAAe,OAAc;AAC3C,SAAO,OAAO,UAAU,YAAY,UAAU,QAAQ,sBAAsB;AAC9E;AAEM,IAAO,kBAAP,cAA+B,aAAY;EAG/C,YAAY,SAAiB,QAAc;AACzC,UAAM,OAAO;AACb,SAAK,OAAO;AACZ,SAAK,SAAS;EAChB;EAEA,SAAM;AACJ,WAAO;MACL,MAAM,KAAK;MACX,SAAS,KAAK;MACd,QAAQ,KAAK;;EAEjB;;AAGI,IAAO,sBAAP,cAAmC,aAAY;EAGnD,YAAY,SAAiB,eAAsB;AACjD,UAAM,OAAO;AACb,SAAK,OAAO;AACZ,SAAK,gBAAgB;EACvB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpCK,IAAMC,gBAAe,CAAC,gBAA8B;AACzD,MAAI;AACJ,MAAI,aAAa;AACf,aAAS;aACA,OAAO,UAAU,aAAa;AACvC,aAAS,IAAI,SACX,OAAO,uBAA6B,EAAE,KAAK,CAAC,EAAE,SAASC,OAAK,MAAOA,OAAM,GAAG,IAAI,CAAC;SAC9E;AACL,aAAS;;AAEX,SAAO,IAAI,SAAS,OAAO,GAAG,IAAI;AACpC;AAEO,IAAM,kBAAkB,MAAqCC,WAAA,QAAA,QAAA,QAAA,aAAA;AAClE,MAAI,OAAO,aAAa,aAAa;AAEnC,YAAQ,MAAM,OAAO,uBAA6B,GAAG;;AAGvD,SAAO;AACT,CAAC;AAEM,IAAM,mBAAmB,CAAC,SAAsC;AACrE,MAAI,MAAM,QAAQ,IAAI,GAAG;AACvB,WAAO,KAAK,IAAI,CAAC,OAAO,iBAAiB,EAAE,CAAC;aACnC,OAAO,SAAS,cAAc,SAAS,OAAO,IAAI,GAAG;AAC9D,WAAO;;AAGT,QAAM,SAA8B,CAAA;AACpC,SAAO,QAAQ,IAAI,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAK;AAC5C,UAAM,SAAS,IAAI,QAAQ,iBAAiB,CAAC,MAAM,EAAE,YAAW,EAAG,QAAQ,SAAS,EAAE,CAAC;AACvF,WAAO,MAAM,IAAI,iBAAiB,KAAK;EACzC,CAAC;AAED,SAAO;AACT;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvBA,IAAM,mBAAmB,CAAC,QACxB,IAAI,OAAO,IAAI,WAAW,IAAI,qBAAqB,IAAI,SAAS,KAAK,UAAU,GAAG;AAEpF,IAAM,cAAc,CAClB,OACA,QACA,YACEC,WAAA,QAAA,QAAA,QAAA,aAAA;AACF,QAAM,MAAM,MAAM,gBAAe;AAEjC,MAAI,iBAAiB,OAAO,EAAC,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,gBAAe;AACnD,UACG,KAAI,EACJ,KAAK,CAAC,QAAO;AACZ,aAAO,IAAI,gBAAgB,iBAAiB,GAAG,GAAG,MAAM,UAAU,GAAG,CAAC;IACxE,CAAC,EACA,MAAM,CAAC,QAAO;AACb,aAAO,IAAI,oBAAoB,iBAAiB,GAAG,GAAG,GAAG,CAAC;IAC5D,CAAC;SACE;AACL,WAAO,IAAI,oBAAoB,iBAAiB,KAAK,GAAG,KAAK,CAAC;;AAElE,CAAC;AAED,IAAM,oBAAoB,CACxB,QACA,SACA,YACA,SACE;AACF,QAAM,SAA+B,EAAE,QAAQ,UAAS,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,YAAW,CAAA,EAAE;AAE9E,MAAI,WAAW,OAAO;AACpB,WAAO;;AAGT,SAAO,UAAO,OAAA,OAAA,EAAK,gBAAgB,mBAAkB,GAAK,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,OAAO;AAE1E,MAAI,MAAM;AACR,WAAO,OAAO,KAAK,UAAU,IAAI;;AAEnC,SAAA,OAAA,OAAA,OAAA,OAAA,CAAA,GAAY,MAAM,GAAK,UAAU;AACnC;AAEA,SAAe,eACb,SACA,QACA,KACA,SACA,YACA,MAAa;;AAEb,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAU;AACrC,cAAQ,KAAK,kBAAkB,QAAQ,SAAS,YAAY,IAAI,CAAC,EAC9D,KAAK,CAAC,WAAU;AACf,YAAI,CAAC,OAAO;AAAI,gBAAM;AACtB,YAAI,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS;AAAe,iBAAO;AACnC,eAAO,OAAO,KAAI;MACpB,CAAC,EACA,KAAK,CAAC,SAAS,QAAQ,IAAI,CAAC,EAC5B,MAAM,CAAC,UAAU,YAAY,OAAO,QAAQ,OAAO,CAAC;IACzD,CAAC;EACH,CAAC;;AAEK,SAAgB,IACpB,SACA,KACA,SACA,YAA4B;;AAE5B,WAAO,eAAe,SAAS,OAAO,KAAK,SAAS,UAAU;EAChE,CAAC;;AAEK,SAAgB,KACpB,SACA,KACA,MACA,SACA,YAA4B;;AAE5B,WAAO,eAAe,SAAS,QAAQ,KAAK,SAAS,YAAY,IAAI;EACvE,CAAC;;AAEK,SAAgB,IACpB,SACA,KACA,MACA,SACA,YAA4B;;AAE5B,WAAO,eAAe,SAAS,OAAO,KAAK,SAAS,YAAY,IAAI;EACtE,CAAC;;AAEK,SAAgB,KACpB,SACA,KACA,SACA,YAA4B;;AAE5B,WAAO,eACL,SACA,QACA,KAAG,OAAA,OAAA,OAAA,OAAA,CAAA,GAEE,OAAO,GAAA,EACV,eAAe,KAAI,CAAA,GAErB,UAAU;EAEd,CAAC;;AAEK,SAAgB,OACpB,SACA,KACA,MACA,SACA,YAA4B;;AAE5B,WAAO,eAAe,SAAS,UAAU,KAAK,SAAS,YAAY,IAAI;EACzE,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxHD,IAAM,yBAAyB;EAC7B,OAAO;EACP,QAAQ;EACR,QAAQ;IACN,QAAQ;IACR,OAAO;;;AAIX,IAAM,uBAAoC;EACxC,cAAc;EACd,aAAa;EACb,QAAQ;;AAeV,IAAqB,iBAArB,MAAmC;EAMjC,YACE,KACA,UAAqC,CAAA,GACrC,UACAC,QAAa;AAEb,SAAK,MAAM;AACX,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,QAAQC,cAAaD,MAAK;EACjC;;;;;;;;EASc,eACZ,QACA,MACA,UACA,aAAyB;;AAWzB,UAAI;AACF,YAAI;AACJ,cAAM,UAAO,OAAA,OAAA,OAAA,OAAA,CAAA,GAAQ,oBAAoB,GAAK,WAAW;AACzD,YAAI,UAAO,OAAA,OAAA,OAAA,OAAA,CAAA,GACN,KAAK,OAAO,GACX,WAAW,UAAU,EAAE,YAAY,OAAO,QAAQ,MAAiB,EAAC,CAAG;AAG7E,cAAM,WAAW,QAAQ;AAEzB,YAAI,OAAO,SAAS,eAAe,oBAAoB,MAAM;AAC3D,iBAAO,IAAI,SAAQ;AACnB,eAAK,OAAO,gBAAgB,QAAQ,YAAsB;AAC1D,eAAK,OAAO,IAAI,QAAQ;AAExB,cAAI,UAAU;AACZ,iBAAK,OAAO,YAAY,KAAK,eAAe,QAAQ,CAAC;;mBAE9C,OAAO,aAAa,eAAe,oBAAoB,UAAU;AAC1E,iBAAO;AACP,eAAK,OAAO,gBAAgB,QAAQ,YAAsB;AAC1D,cAAI,UAAU;AACZ,iBAAK,OAAO,YAAY,KAAK,eAAe,QAAQ,CAAC;;eAElD;AACL,iBAAO;AACP,kBAAQ,eAAe,IAAI,WAAW,QAAQ,YAAY;AAC1D,kBAAQ,cAAc,IAAI,QAAQ;AAElC,cAAI,UAAU;AACZ,oBAAQ,YAAY,IAAI,KAAK,SAAS,KAAK,eAAe,QAAQ,CAAC;;;AAIvE,YAAI,gBAAW,QAAX,gBAAW,SAAA,SAAX,YAAa,SAAS;AACxB,oBAAO,OAAA,OAAA,OAAA,OAAA,CAAA,GAAQ,OAAO,GAAK,YAAY,OAAO;;AAGhD,cAAM,YAAY,KAAK,oBAAoB,IAAI;AAC/C,cAAM,QAAQ,KAAK,cAAc,SAAS;AAC1C,cAAM,MAAM,MAAM,KAAK,MAAM,GAAG,KAAK,GAAG,WAAW,KAAK,IAAE,OAAA,OAAA,EACxD,QACA,MACA,QAAO,IACH,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,UAAS,EAAE,QAAQ,QAAQ,OAAM,IAAK,CAAA,CAAG,CAAA;AAGxD,cAAM,OAAO,MAAM,IAAI,KAAI;AAE3B,YAAI,IAAI,IAAI;AACV,iBAAO;YACL,MAAM,EAAE,MAAM,WAAW,IAAI,KAAK,IAAI,UAAU,KAAK,IAAG;YACxD,OAAO;;eAEJ;AACL,gBAAM,QAAQ;AACd,iBAAO,EAAE,MAAM,MAAM,MAAK;;eAErB,OAAO;AACd,YAAI,eAAe,KAAK,GAAG;AACzB,iBAAO,EAAE,MAAM,MAAM,MAAK;;AAG5B,cAAM;;IAEV,CAAC;;;;;;;;EAQK,OACJ,MACA,UACA,aAAyB;;AAWzB,aAAO,KAAK,eAAe,QAAQ,MAAM,UAAU,WAAW;IAChE,CAAC;;;;;;;;EAQK,kBACJ,MACA,OACA,UACA,aAAyB;;AAEzB,YAAM,YAAY,KAAK,oBAAoB,IAAI;AAC/C,YAAM,QAAQ,KAAK,cAAc,SAAS;AAE1C,YAAM,MAAM,IAAI,IAAI,KAAK,MAAM,uBAAuB,KAAK,EAAE;AAC7D,UAAI,aAAa,IAAI,SAAS,KAAK;AAEnC,UAAI;AACF,YAAI;AACJ,cAAM,UAAO,OAAA,OAAA,EAAK,QAAQ,qBAAqB,OAAM,GAAK,WAAW;AACrE,cAAM,UAAO,OAAA,OAAA,OAAA,OAAA,CAAA,GACR,KAAK,OAAO,GACZ,EAAE,YAAY,OAAO,QAAQ,MAAiB,EAAC,CAAE;AAGtD,YAAI,OAAO,SAAS,eAAe,oBAAoB,MAAM;AAC3D,iBAAO,IAAI,SAAQ;AACnB,eAAK,OAAO,gBAAgB,QAAQ,YAAsB;AAC1D,eAAK,OAAO,IAAI,QAAQ;mBACf,OAAO,aAAa,eAAe,oBAAoB,UAAU;AAC1E,iBAAO;AACP,eAAK,OAAO,gBAAgB,QAAQ,YAAsB;eACrD;AACL,iBAAO;AACP,kBAAQ,eAAe,IAAI,WAAW,QAAQ,YAAY;AAC1D,kBAAQ,cAAc,IAAI,QAAQ;;AAGpC,cAAM,MAAM,MAAM,KAAK,MAAM,IAAI,SAAQ,GAAI;UAC3C,QAAQ;UACR;UACA;SACD;AAED,cAAM,OAAO,MAAM,IAAI,KAAI;AAE3B,YAAI,IAAI,IAAI;AACV,iBAAO;YACL,MAAM,EAAE,MAAM,WAAW,UAAU,KAAK,IAAG;YAC3C,OAAO;;eAEJ;AACL,gBAAM,QAAQ;AACd,iBAAO,EAAE,MAAM,MAAM,MAAK;;eAErB,OAAO;AACd,YAAI,eAAe,KAAK,GAAG;AACzB,iBAAO,EAAE,MAAM,MAAM,MAAK;;AAG5B,cAAM;;IAEV,CAAC;;;;;;;;;EASK,sBACJ,MACA,SAA6B;;AAW7B,UAAI;AACF,YAAI,QAAQ,KAAK,cAAc,IAAI;AAEnC,cAAM,UAAO,OAAA,OAAA,CAAA,GAAQ,KAAK,OAAO;AAEjC,YAAI,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,QAAQ;AACnB,kBAAQ,UAAU,IAAI;;AAGxB,cAAM,OAAO,MAAM,KACjB,KAAK,OACL,GAAG,KAAK,GAAG,uBAAuB,KAAK,IACvC,CAAA,GACA,EAAE,QAAO,CAAE;AAGb,cAAM,MAAM,IAAI,IAAI,KAAK,MAAM,KAAK,GAAG;AAEvC,cAAM,QAAQ,IAAI,aAAa,IAAI,OAAO;AAE1C,YAAI,CAAC,OAAO;AACV,gBAAM,IAAI,aAAa,0BAA0B;;AAGnD,eAAO,EAAE,MAAM,EAAE,WAAW,IAAI,SAAQ,GAAI,MAAM,MAAK,GAAI,OAAO,KAAI;eAC/D,OAAO;AACd,YAAI,eAAe,KAAK,GAAG;AACzB,iBAAO,EAAE,MAAM,MAAM,MAAK;;AAG5B,cAAM;;IAEV,CAAC;;;;;;;;EAQK,OACJ,MACA,UAWA,aAAyB;;AAWzB,aAAO,KAAK,eAAe,OAAO,MAAM,UAAU,WAAW;IAC/D,CAAC;;;;;;;;;EASK,KACJ,UACA,QACA,SAA4B;;AAW5B,UAAI;AACF,cAAM,OAAO,MAAM,KACjB,KAAK,OACL,GAAG,KAAK,GAAG,gBACX;UACE,UAAU,KAAK;UACf,WAAW;UACX,gBAAgB;UAChB,mBAAmB,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS;WAE9B,EAAE,SAAS,KAAK,QAAO,CAAE;AAE3B,eAAO,EAAE,MAAM,OAAO,KAAI;eACnB,OAAO;AACd,YAAI,eAAe,KAAK,GAAG;AACzB,iBAAO,EAAE,MAAM,MAAM,MAAK;;AAG5B,cAAM;;IAEV,CAAC;;;;;;;;;EASK,KACJ,UACA,QACA,SAA4B;;AAW5B,UAAI;AACF,cAAM,OAAO,MAAM,KACjB,KAAK,OACL,GAAG,KAAK,GAAG,gBACX;UACE,UAAU,KAAK;UACf,WAAW;UACX,gBAAgB;UAChB,mBAAmB,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS;WAE9B,EAAE,SAAS,KAAK,QAAO,CAAE;AAE3B,eAAO,EAAE,MAAM,EAAE,MAAM,KAAK,IAAG,GAAI,OAAO,KAAI;eACvC,OAAO;AACd,YAAI,eAAe,KAAK,GAAG;AACzB,iBAAO,EAAE,MAAM,MAAM,MAAK;;AAG5B,cAAM;;IAEV,CAAC;;;;;;;;;;EAUK,gBACJ,MACA,WACA,SAAuE;;AAWvE,UAAI;AACF,YAAI,QAAQ,KAAK,cAAc,IAAI;AAEnC,YAAI,OAAO,MAAM,KACf,KAAK,OACL,GAAG,KAAK,GAAG,gBAAgB,KAAK,IAAE,OAAA,OAAA,EAChC,UAAS,IAAM,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,aAAY,EAAE,WAAW,QAAQ,UAAS,IAAK,CAAA,CAAG,GAC5E,EAAE,SAAS,KAAK,QAAO,CAAE;AAE3B,cAAM,sBAAqB,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,YAChC,aAAa,QAAQ,aAAa,OAAO,KAAK,QAAQ,QAAQ,KAC9D;AACJ,cAAM,YAAY,UAAU,GAAG,KAAK,GAAG,GAAG,KAAK,SAAS,GAAG,kBAAkB,EAAE;AAC/E,eAAO,EAAE,UAAS;AAClB,eAAO,EAAE,MAAM,OAAO,KAAI;eACnB,OAAO;AACd,YAAI,eAAe,KAAK,GAAG;AACzB,iBAAO,EAAE,MAAM,MAAM,MAAK;;AAG5B,cAAM;;IAEV,CAAC;;;;;;;;;EASK,iBACJ,OACA,WACA,SAAwC;;AAWxC,UAAI;AACF,cAAM,OAAO,MAAM,KACjB,KAAK,OACL,GAAG,KAAK,GAAG,gBAAgB,KAAK,QAAQ,IACxC,EAAE,WAAW,MAAK,GAClB,EAAE,SAAS,KAAK,QAAO,CAAE;AAG3B,cAAM,sBAAqB,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,YAChC,aAAa,QAAQ,aAAa,OAAO,KAAK,QAAQ,QAAQ,KAC9D;AACJ,eAAO;UACL,MAAM,KAAK,IAAI,CAAC,UAAiC,OAAA,OAAA,OAAA,OAAA,CAAA,GAC5C,KAAK,GAAA,EACR,WAAW,MAAM,YACb,UAAU,GAAG,KAAK,GAAG,GAAG,MAAM,SAAS,GAAG,kBAAkB,EAAE,IAC9D,KAAI,CAAA,CACR;UACF,OAAO;;eAEF,OAAO;AACd,YAAI,eAAe,KAAK,GAAG;AACzB,iBAAO,EAAE,MAAM,MAAM,MAAK;;AAG5B,cAAM;;IAEV,CAAC;;;;;;;;EAQK,SACJ,MACA,SAA0C;;AAW1C,YAAM,sBAAsB,QAAO,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,eAAc;AAC1D,YAAM,aAAa,sBAAsB,+BAA+B;AACxE,YAAM,sBAAsB,KAAK,4BAA2B,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,cAAa,CAAA,CAAE;AACpF,YAAM,cAAc,sBAAsB,IAAI,mBAAmB,KAAK;AAEtE,UAAI;AACF,cAAM,QAAQ,KAAK,cAAc,IAAI;AACrC,cAAM,MAAM,MAAM,IAAI,KAAK,OAAO,GAAG,KAAK,GAAG,IAAI,UAAU,IAAI,KAAK,GAAG,WAAW,IAAI;UACpF,SAAS,KAAK;UACd,eAAe;SAChB;AACD,cAAM,OAAO,MAAM,IAAI,KAAI;AAC3B,eAAO,EAAE,MAAM,OAAO,KAAI;eACnB,OAAO;AACd,YAAI,eAAe,KAAK,GAAG;AACzB,iBAAO,EAAE,MAAM,MAAM,MAAK;;AAG5B,cAAM;;IAEV,CAAC;;;;;;EAMK,KACJ,MAAY;;AAWZ,YAAM,QAAQ,KAAK,cAAc,IAAI;AAErC,UAAI;AACF,cAAM,OAAO,MAAM,IAAI,KAAK,OAAO,GAAG,KAAK,GAAG,gBAAgB,KAAK,IAAI;UACrE,SAAS,KAAK;SACf;AAED,eAAO,EAAE,MAAM,iBAAiB,IAAI,GAA6B,OAAO,KAAI;eACrE,OAAO;AACd,YAAI,eAAe,KAAK,GAAG;AACzB,iBAAO,EAAE,MAAM,MAAM,MAAK;;AAG5B,cAAM;;IAEV,CAAC;;;;;;EAMK,OACJ,MAAY;;AAWZ,YAAM,QAAQ,KAAK,cAAc,IAAI;AAErC,UAAI;AACF,cAAM,KAAK,KAAK,OAAO,GAAG,KAAK,GAAG,WAAW,KAAK,IAAI;UACpD,SAAS,KAAK;SACf;AAED,eAAO,EAAE,MAAM,MAAM,OAAO,KAAI;eACzB,OAAO;AACd,YAAI,eAAe,KAAK,KAAK,iBAAiB,qBAAqB;AACjE,gBAAM,gBAAiB,MAAM;AAE7B,cAAI,CAAC,KAAK,GAAG,EAAE,SAAS,kBAAa,QAAb,kBAAa,SAAA,SAAb,cAAe,MAAM,GAAG;AAC9C,mBAAO,EAAE,MAAM,OAAO,MAAK;;;AAI/B,cAAM;;IAEV,CAAC;;;;;;;;;;EAUD,aACE,MACA,SAAuE;AAEvE,UAAM,QAAQ,KAAK,cAAc,IAAI;AACrC,UAAM,eAAe,CAAA;AAErB,UAAM,sBAAqB,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,YAChC,YAAY,QAAQ,aAAa,OAAO,KAAK,QAAQ,QAAQ,KAC7D;AAEJ,QAAI,uBAAuB,IAAI;AAC7B,mBAAa,KAAK,kBAAkB;;AAGtC,UAAM,sBAAsB,QAAO,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,eAAc;AAC1D,UAAM,aAAa,sBAAsB,iBAAiB;AAC1D,UAAM,sBAAsB,KAAK,4BAA2B,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,cAAa,CAAA,CAAE;AAEpF,QAAI,wBAAwB,IAAI;AAC9B,mBAAa,KAAK,mBAAmB;;AAGvC,QAAI,cAAc,aAAa,KAAK,GAAG;AACvC,QAAI,gBAAgB,IAAI;AACtB,oBAAc,IAAI,WAAW;;AAG/B,WAAO;MACL,MAAM,EAAE,WAAW,UAAU,GAAG,KAAK,GAAG,IAAI,UAAU,WAAW,KAAK,GAAG,WAAW,EAAE,EAAC;;EAE3F;;;;;;EAOM,OACJ,OAAe;;AAWf,UAAI;AACF,cAAM,OAAO,MAAM,OACjB,KAAK,OACL,GAAG,KAAK,GAAG,WAAW,KAAK,QAAQ,IACnC,EAAE,UAAU,MAAK,GACjB,EAAE,SAAS,KAAK,QAAO,CAAE;AAE3B,eAAO,EAAE,MAAM,OAAO,KAAI;eACnB,OAAO;AACd,YAAI,eAAe,KAAK,GAAG;AACzB,iBAAO,EAAE,MAAM,MAAM,MAAK;;AAG5B,cAAM;;IAEV,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAqEK,KACJ,MACA,SACA,YAA4B;;AAW5B,UAAI;AACF,cAAM,OAAI,OAAA,OAAA,OAAA,OAAA,OAAA,OAAA,CAAA,GAAQ,sBAAsB,GAAK,OAAO,GAAA,EAAE,QAAQ,QAAQ,GAAE,CAAA;AACxE,cAAM,OAAO,MAAM,KACjB,KAAK,OACL,GAAG,KAAK,GAAG,gBAAgB,KAAK,QAAQ,IACxC,MACA,EAAE,SAAS,KAAK,QAAO,GACvB,UAAU;AAEZ,eAAO,EAAE,MAAM,OAAO,KAAI;eACnB,OAAO;AACd,YAAI,eAAe,KAAK,GAAG;AACzB,iBAAO,EAAE,MAAM,MAAM,MAAK;;AAG5B,cAAM;;IAEV,CAAC;;EAES,eAAe,UAA6B;AACpD,WAAO,KAAK,UAAU,QAAQ;EAChC;EAEA,SAAS,MAAY;AACnB,QAAI,OAAO,WAAW,aAAa;AACjC,aAAO,OAAO,KAAK,IAAI,EAAE,SAAS,QAAQ;;AAE5C,WAAO,KAAK,IAAI;EAClB;EAEQ,cAAc,MAAY;AAChC,WAAO,GAAG,KAAK,QAAQ,IAAI,IAAI;EACjC;EAEQ,oBAAoB,MAAY;AACtC,WAAO,KAAK,QAAQ,YAAY,EAAE,EAAE,QAAQ,QAAQ,GAAG;EACzD;EAEQ,2BAA2B,WAA2B;AAC5D,UAAM,SAAS,CAAA;AACf,QAAI,UAAU,OAAO;AACnB,aAAO,KAAK,SAAS,UAAU,KAAK,EAAE;;AAGxC,QAAI,UAAU,QAAQ;AACpB,aAAO,KAAK,UAAU,UAAU,MAAM,EAAE;;AAG1C,QAAI,UAAU,QAAQ;AACpB,aAAO,KAAK,UAAU,UAAU,MAAM,EAAE;;AAG1C,QAAI,UAAU,QAAQ;AACpB,aAAO,KAAK,UAAU,UAAU,MAAM,EAAE;;AAG1C,QAAI,UAAU,SAAS;AACrB,aAAO,KAAK,WAAW,UAAU,OAAO,EAAE;;AAG5C,WAAO,OAAO,KAAK,GAAG;EACxB;;;;ACh0BK,IAAME,WAAU;;;ACAhB,IAAMC,mBAAkB,EAAE,iBAAiB,cAAcC,QAAO,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACKzE,IAAqB,mBAArB,MAAqC;EAKnC,YAAY,KAAa,UAAqC,CAAA,GAAIC,QAAa;AAC7E,SAAK,MAAM;AACX,SAAK,UAAO,OAAA,OAAA,OAAA,OAAA,CAAA,GAAQC,gBAAe,GAAK,OAAO;AAC/C,SAAK,QAAQC,cAAaF,MAAK;EACjC;;;;EAKM,cAAW;;AAUf,UAAI;AACF,cAAM,OAAO,MAAM,IAAI,KAAK,OAAO,GAAG,KAAK,GAAG,WAAW,EAAE,SAAS,KAAK,QAAO,CAAE;AAClF,eAAO,EAAE,MAAM,OAAO,KAAI;eACnB,OAAO;AACd,YAAI,eAAe,KAAK,GAAG;AACzB,iBAAO,EAAE,MAAM,MAAM,MAAK;;AAG5B,cAAM;;IAEV,CAAC;;;;;;;EAOK,UACJ,IAAU;;AAWV,UAAI;AACF,cAAM,OAAO,MAAM,IAAI,KAAK,OAAO,GAAG,KAAK,GAAG,WAAW,EAAE,IAAI,EAAE,SAAS,KAAK,QAAO,CAAE;AACxF,eAAO,EAAE,MAAM,OAAO,KAAI;eACnB,OAAO;AACd,YAAI,eAAe,KAAK,GAAG;AACzB,iBAAO,EAAE,MAAM,MAAM,MAAK;;AAG5B,cAAM;;IAEV,CAAC;;;;;;;;;;;;;;;EAeK,aACJ,IACA,UAII;IACF,QAAQ;KACT;;AAWD,UAAI;AACF,cAAM,OAAO,MAAM,KACjB,KAAK,OACL,GAAG,KAAK,GAAG,WACX;UACE;UACA,MAAM;UACN,QAAQ,QAAQ;UAChB,iBAAiB,QAAQ;UACzB,oBAAoB,QAAQ;WAE9B,EAAE,SAAS,KAAK,QAAO,CAAE;AAE3B,eAAO,EAAE,MAAM,OAAO,KAAI;eACnB,OAAO;AACd,YAAI,eAAe,KAAK,GAAG;AACzB,iBAAO,EAAE,MAAM,MAAM,MAAK;;AAG5B,cAAM;;IAEV,CAAC;;;;;;;;;;;;;;EAcK,aACJ,IACA,SAIC;;AAWD,UAAI;AACF,cAAM,OAAO,MAAM,IACjB,KAAK,OACL,GAAG,KAAK,GAAG,WAAW,EAAE,IACxB;UACE;UACA,MAAM;UACN,QAAQ,QAAQ;UAChB,iBAAiB,QAAQ;UACzB,oBAAoB,QAAQ;WAE9B,EAAE,SAAS,KAAK,QAAO,CAAE;AAE3B,eAAO,EAAE,MAAM,OAAO,KAAI;eACnB,OAAO;AACd,YAAI,eAAe,KAAK,GAAG;AACzB,iBAAO,EAAE,MAAM,MAAM,MAAK;;AAG5B,cAAM;;IAEV,CAAC;;;;;;;EAOK,YACJ,IAAU;;AAWV,UAAI;AACF,cAAM,OAAO,MAAM,KACjB,KAAK,OACL,GAAG,KAAK,GAAG,WAAW,EAAE,UACxB,CAAA,GACA,EAAE,SAAS,KAAK,QAAO,CAAE;AAE3B,eAAO,EAAE,MAAM,OAAO,KAAI;eACnB,OAAO;AACd,YAAI,eAAe,KAAK,GAAG;AACzB,iBAAO,EAAE,MAAM,MAAM,MAAK;;AAG5B,cAAM;;IAEV,CAAC;;;;;;;;EAQK,aACJ,IAAU;;AAWV,UAAI;AACF,cAAM,OAAO,MAAM,OACjB,KAAK,OACL,GAAG,KAAK,GAAG,WAAW,EAAE,IACxB,CAAA,GACA,EAAE,SAAS,KAAK,QAAO,CAAE;AAE3B,eAAO,EAAE,MAAM,OAAO,KAAI;eACnB,OAAO;AACd,YAAI,eAAe,KAAK,GAAG;AACzB,iBAAO,EAAE,MAAM,MAAM,MAAK;;AAG5B,cAAM;;IAEV,CAAC;;;;;ACjPG,IAAO,gBAAP,cAA6B,iBAAgB;EACjD,YAAY,KAAa,UAAqC,CAAA,GAAIG,QAAa;AAC7E,UAAM,KAAK,SAASA,MAAK;EAC3B;;;;;;EAOA,KAAK,IAAU;AACb,WAAO,IAAI,eAAe,KAAK,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK;EAClE;;;;AChBK,IAAMC,WAAU;;;ACKvB,IAAI,SAAS;AAEb,IAAI,OAAO,SAAS,aAAa;AAC/B,WAAS;WACA,OAAO,aAAa,aAAa;AAC1C,WAAS;WACA,OAAO,cAAc,eAAe,UAAU,YAAY,eAAe;AAClF,WAAS;OACJ;AACL,WAAS;;AAGJ,IAAMC,mBAAkB,EAAE,iBAAiB,eAAe,MAAM,IAAIC,QAAO,GAAE;AAE7E,IAAM,yBAAyB;EACpC,SAASD;;AAGJ,IAAM,qBAAqB;EAChC,QAAQ;;AAGH,IAAM,uBAAkD;EAC7D,kBAAkB;EAClB,gBAAgB;EAChB,oBAAoB;EACpB,UAAU;;AAGL,IAAM,2BAAkD,CAAA;;;ACjC/D;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIO,IAAME,gBAAe,CAAC,gBAA8B;AACzD,MAAI;AACJ,MAAI,aAAa;AACf,aAAS;aACA,OAAO,UAAU,aAAa;AACvC,aAAS;SACJ;AACL,aAAS;;AAEX,SAAO,IAAI,SAA4B,OAAO,GAAG,IAAI;AACvD;AAEO,IAAM,4BAA4B,MAAK;AAC5C,MAAI,OAAO,YAAY,aAAa;AAClC,WAAOC;;AAGT,SAAO;AACT;AAEO,IAAM,gBAAgB,CAC3B,aACA,gBACA,gBACS;AACT,QAAMC,SAAQF,cAAa,WAAW;AACtC,QAAM,qBAAqB,0BAAyB;AAEpD,SAAO,CAAO,OAAO,SAAQG,WAAA,QAAA,QAAA,QAAA,aAAA;;AAC3B,UAAM,eAAc,KAAC,MAAM,eAAc,OAAG,QAAA,OAAA,SAAA,KAAI;AAChD,QAAI,UAAU,IAAI,mBAAmB,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM,OAAO;AAElD,QAAI,CAAC,QAAQ,IAAI,QAAQ,GAAG;AAC1B,cAAQ,IAAI,UAAU,WAAW;;AAGnC,QAAI,CAAC,QAAQ,IAAI,eAAe,GAAG;AACjC,cAAQ,IAAI,iBAAiB,UAAU,WAAW,EAAE;;AAGtD,WAAOD,OAAM,OAAK,OAAA,OAAA,OAAA,OAAA,CAAA,GAAO,IAAI,GAAA,EAAE,QAAO,CAAA,CAAA;EACxC,CAAC;AACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpCM,SAAU,mBAAmB,KAAW;AAC5C,SAAO,IAAI,QAAQ,OAAO,EAAE;AAC9B;AAIM,SAAU,qBAMd,SACA,UAAoC;AAEpC,QAAM,EACJ,IAAI,WACJ,MAAM,aACN,UAAU,iBACV,QAAQ,cAAa,IACnB;AACJ,QAAM,EACJ,IAAIE,qBACJ,MAAMC,uBACN,UAAUC,2BACV,QAAQC,wBAAsB,IAC5B;AAEJ,QAAM,SAAsD;IAC1D,IAAE,OAAA,OAAA,OAAA,OAAA,CAAA,GACGH,mBAAkB,GAClB,SAAS;IAEd,MAAI,OAAA,OAAA,OAAA,OAAA,CAAA,GACCC,qBAAoB,GACpB,WAAW;IAEhB,UAAQ,OAAA,OAAA,OAAA,OAAA,CAAA,GACHC,yBAAwB,GACxB,eAAe;IAEpB,QAAM,OAAA,OAAA,OAAA,OAAA,CAAA,GACDC,uBAAsB,GACtB,aAAa;IAElB,aAAa,MAAWC,WAAA,MAAA,QAAA,QAAA,aAAA;AAAC,aAAA;IAAE,CAAA;;AAG7B,MAAI,QAAQ,aAAa;AACvB,WAAO,cAAc,QAAQ;SACxB;AAEL,WAAQ,OAAe;;AAGzB,SAAO;AACT;;;ACnEO,IAAMC,WAAU;;;ACChB,IAAM,aAAa;AACnB,IAAM,cAAc;AAEpB,IAAMC,mBAAkB,EAAE,iBAAiB,aAAaC,QAAO,GAAE;AACjE,IAAM,gBAAgB;AAMtB,IAAM,0BAA0B;AAChC,IAAM,eAAe;EAC1B,cAAc;IACZ,WAAW,KAAK,MAAM,wBAAwB;IAC9C,MAAM;;;;;ACZJ,SAAU,UAAU,WAAiB;AACzC,QAAM,UAAU,KAAK,MAAM,KAAK,IAAG,IAAK,GAAI;AAC5C,SAAO,UAAU;AACnB;AAEM,SAAU,OAAI;AAClB,SAAO,uCAAuC,QAAQ,SAAS,SAAU,GAAC;AACxE,UAAM,IAAK,KAAK,OAAM,IAAK,KAAM,GAC/B,IAAI,KAAK,MAAM,IAAK,IAAI,IAAO;AACjC,WAAO,EAAE,SAAS,EAAE;EACtB,CAAC;AACH;AAEO,IAAM,YAAY,MAAM,OAAO,aAAa;AAEnD,IAAM,yBAAyB;EAC7B,QAAQ;EACR,UAAU;;AAML,IAAM,uBAAuB,MAAK;AACvC,MAAI,CAAC,UAAS,GAAI;AAChB,WAAO;;AAGT,MAAI;AACF,QAAI,OAAO,WAAW,iBAAiB,UAAU;AAC/C,aAAO;;WAEF,GAAG;AAEV,WAAO;;AAGT,MAAI,uBAAuB,QAAQ;AACjC,WAAO,uBAAuB;;AAGhC,QAAM,YAAY,QAAQ,KAAK,OAAM,CAAE,GAAG,KAAK,OAAM,CAAE;AAEvD,MAAI;AACF,eAAW,aAAa,QAAQ,WAAW,SAAS;AACpD,eAAW,aAAa,WAAW,SAAS;AAE5C,2BAAuB,SAAS;AAChC,2BAAuB,WAAW;WAC3B,GAAG;AAIV,2BAAuB,SAAS;AAChC,2BAAuB,WAAW;;AAGpC,SAAO,uBAAuB;AAChC;AAKM,SAAU,uBAAuB,MAAY;AACjD,QAAM,SAA0C,CAAA;AAEhD,QAAM,MAAM,IAAI,IAAI,IAAI;AAExB,MAAI,IAAI,QAAQ,IAAI,KAAK,CAAC,MAAM,KAAK;AACnC,QAAI;AACF,YAAM,mBAAmB,IAAI,gBAAgB,IAAI,KAAK,UAAU,CAAC,CAAC;AAClE,uBAAiB,QAAQ,CAAC,OAAO,QAAO;AACtC,eAAO,GAAG,IAAI;MAChB,CAAC;aACM,GAAQ;;;AAMnB,MAAI,aAAa,QAAQ,CAAC,OAAO,QAAO;AACtC,WAAO,GAAG,IAAI;EAChB,CAAC;AAED,SAAO;AACT;AAIO,IAAMC,gBAAe,CAAC,gBAA8B;AACzD,MAAI;AACJ,MAAI,aAAa;AACf,aAAS;aACA,OAAO,UAAU,aAAa;AACvC,aAAS,IAAI,SACX,OAAO,uBAA6B,EAAE,KAAK,CAAC,EAAE,SAASC,OAAK,MAAOA,OAAM,GAAG,IAAI,CAAC;SAC9E;AACL,aAAS;;AAEX,SAAO,IAAI,SAAS,OAAO,GAAG,IAAI;AACpC;AAEO,IAAM,yBAAyB,CAAC,kBAAqD;AAC1F,SACE,OAAO,kBAAkB,YACzB,kBAAkB,QAClB,YAAY,iBACZ,QAAQ,iBACR,UAAU,iBACV,OAAQ,cAAsB,SAAS;AAE3C;AAGO,IAAM,eAAe,OAC1B,SACA,KACA,SACiB;AACjB,QAAM,QAAQ,QAAQ,KAAK,KAAK,UAAU,IAAI,CAAC;AACjD;AAEO,IAAM,eAAe,OAAO,SAA2B,QAAiC;AAC7F,QAAM,QAAQ,MAAM,QAAQ,QAAQ,GAAG;AAEvC,MAAI,CAAC,OAAO;AACV,WAAO;;AAGT,MAAI;AACF,WAAO,KAAK,MAAM,KAAK;WACvB,IAAM;AACN,WAAO;;AAEX;AAEO,IAAM,kBAAkB,OAAO,SAA2B,QAA8B;AAC7F,QAAM,QAAQ,WAAW,GAAG;AAC9B;AAEM,SAAU,gBAAgB,OAAa;AAC3C,QAAM,MAAM;AACZ,MAAI,SAAS;AACb,MAAI,MAAM,MAAM;AAChB,MAAI,MAAM,MAAM,MAAM;AACtB,MAAI,IAAI;AACR,UAAQ,MAAM,QAAQ,KAAK,GAAG,EAAE,QAAQ,KAAK,GAAG;AAEhD,SAAO,IAAI,MAAM,QAAQ;AACvB,WAAO,IAAI,QAAQ,MAAM,OAAO,GAAG,CAAC;AACpC,WAAO,IAAI,QAAQ,MAAM,OAAO,GAAG,CAAC;AACpC,WAAO,IAAI,QAAQ,MAAM,OAAO,GAAG,CAAC;AACpC,WAAO,IAAI,QAAQ,MAAM,OAAO,GAAG,CAAC;AACpC,WAAQ,QAAQ,IAAM,QAAQ;AAC9B,YAAS,OAAO,OAAO,IAAM,QAAQ;AACrC,YAAS,OAAO,MAAM,IAAK;AAC3B,aAAS,SAAS,OAAO,aAAa,IAAI;AAE1C,QAAI,QAAQ,MAAM,QAAQ,GAAG;AAC3B,eAAS,SAAS,OAAO,aAAa,IAAI;;AAE5C,QAAI,QAAQ,MAAM,QAAQ,GAAG;AAC3B,eAAS,SAAS,OAAO,aAAa,IAAI;;;AAG9C,SAAO;AACT;AAOM,IAAO,WAAP,MAAO,UAAQ;EASnB,cAAA;AAEE;AAAE,SAAa,UAAU,IAAI,UAAS,mBAAmB,CAAC,KAAK,QAAO;AAEpE;AAAE,WAAa,UAAU;AAEvB,WAAa,SAAS;IAC1B,CAAC;EACH;;AAhBc,SAAA,qBAAyC;AAoBnD,SAAU,iBAAiB,OAAa;AAE5C,QAAM,iBAAiB;AAEvB,QAAM,QAAQ,MAAM,MAAM,GAAG;AAE7B,MAAI,MAAM,WAAW,GAAG;AACtB,UAAM,IAAI,MAAM,uCAAuC;;AAGzD,MAAI,CAAC,eAAe,KAAK,MAAM,CAAC,CAAC,GAAG;AAClC,UAAM,IAAI,MAAM,sDAAsD;;AAGxE,QAAM,YAAY,MAAM,CAAC;AACzB,SAAO,KAAK,MAAM,gBAAgB,SAAS,CAAC;AAC9C;AAKA,eAAsB,MAAM,MAAY;AACtC,SAAO,MAAM,IAAI,QAAQ,CAAC,WAAU;AAClC,eAAW,MAAM,OAAO,IAAI,GAAG,IAAI;EACrC,CAAC;AACH;AAOM,SAAU,UACd,IACA,aAAwE;AAExE,QAAM,UAAU,IAAI,QAAW,CAAC,QAAQ,WAAU;AAEhD;AAAC,KAAC,YAAW;AACX,eAAS,UAAU,GAAG,UAAU,UAAU,WAAW;AACnD,YAAI;AACF,gBAAM,SAAS,MAAM,GAAG,OAAO;AAE/B,cAAI,CAAC,YAAY,SAAS,MAAM,MAAM,GAAG;AACvC,mBAAO,MAAM;AACb;;iBAEK,GAAQ;AACf,cAAI,CAAC,YAAY,SAAS,CAAC,GAAG;AAC5B,mBAAO,CAAC;AACR;;;;IAIR,GAAE;EACJ,CAAC;AAED,SAAO;AACT;AAEA,SAAS,QAAQ,KAAW;AAC1B,UAAQ,MAAM,IAAI,SAAS,EAAE,GAAG,OAAO,EAAE;AAC3C;AAGM,SAAU,uBAAoB;AAClC,QAAM,iBAAiB;AACvB,QAAM,QAAQ,IAAI,YAAY,cAAc;AAC5C,MAAI,OAAO,WAAW,aAAa;AACjC,UAAM,UAAU;AAChB,UAAM,aAAa,QAAQ;AAC3B,QAAI,WAAW;AACf,aAAS,IAAI,GAAG,IAAI,gBAAgB,KAAK;AACvC,kBAAY,QAAQ,OAAO,KAAK,MAAM,KAAK,OAAM,IAAK,UAAU,CAAC;;AAEnE,WAAO;;AAET,SAAO,gBAAgB,KAAK;AAC5B,SAAO,MAAM,KAAK,OAAO,OAAO,EAAE,KAAK,EAAE;AAC3C;AAEA,eAAe,OAAO,cAAoB;AACxC,QAAM,UAAU,IAAI,YAAW;AAC/B,QAAM,cAAc,QAAQ,OAAO,YAAY;AAC/C,QAAM,OAAO,MAAM,OAAO,OAAO,OAAO,WAAW,WAAW;AAC9D,QAAM,QAAQ,IAAI,WAAW,IAAI;AAEjC,SAAO,MAAM,KAAK,KAAK,EACpB,IAAI,CAAC,MAAM,OAAO,aAAa,CAAC,CAAC,EACjC,KAAK,EAAE;AACZ;AAEA,SAAS,gBAAgB,KAAW;AAClC,SAAO,KAAK,GAAG,EAAE,QAAQ,OAAO,GAAG,EAAE,QAAQ,OAAO,GAAG,EAAE,QAAQ,OAAO,EAAE;AAC5E;AAEA,eAAsB,sBAAsB,UAAgB;AAC1D,QAAM,mBACJ,OAAO,WAAW,eAClB,OAAO,OAAO,WAAW,eACzB,OAAO,gBAAgB;AAEzB,MAAI,CAAC,kBAAkB;AACrB,YAAQ,KACN,oGAAoG;AAEtG,WAAO;;AAET,QAAM,SAAS,MAAM,OAAO,QAAQ;AACpC,SAAO,gBAAgB,MAAM;AAC/B;AAEA,eAAsB,0BACpB,SACA,YACA,qBAAqB,OAAK;AAE1B,QAAM,eAAe,qBAAoB;AACzC,MAAI,qBAAqB;AACzB,MAAI,oBAAoB;AACtB,0BAAsB;;AAExB,QAAM,aAAa,SAAS,GAAG,UAAU,kBAAkB,kBAAkB;AAC7E,QAAM,gBAAgB,MAAM,sBAAsB,YAAY;AAC9D,QAAM,sBAAsB,iBAAiB,gBAAgB,UAAU;AACvE,SAAO,CAAC,eAAe,mBAAmB;AAC5C;AAGA,IAAM,oBAAoB;AAEpB,SAAU,wBAAwB,UAAkB;AACxD,QAAM,aAAa,SAAS,QAAQ,IAAI,uBAAuB;AAE/D,MAAI,CAAC,YAAY;AACf,WAAO;;AAGT,MAAI,CAAC,WAAW,MAAM,iBAAiB,GAAG;AACxC,WAAO;;AAGT,MAAI;AACF,UAAM,OAAO,oBAAI,KAAK,GAAG,UAAU,cAAc;AACjD,WAAO;WACA,GAAQ;AACf,WAAO;;AAEX;;;ACtVM,IAAO,YAAP,cAAyB,MAAK;EAclC,YAAY,SAAiB,QAAiB,MAAa;AACzD,UAAM,OAAO;AAHL,SAAA,gBAAgB;AAIxB,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,SAAK,OAAO;EACd;;AAGI,SAAU,YAAY,OAAc;AACxC,SAAO,OAAO,UAAU,YAAY,UAAU,QAAQ,mBAAmB;AAC3E;AAEM,IAAO,eAAP,cAA4B,UAAS;EAGzC,YAAY,SAAiB,QAAgB,MAAwB;AACnE,UAAM,SAAS,QAAQ,IAAI;AAC3B,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,SAAK,OAAO;EACd;;AAGI,SAAU,eAAe,OAAc;AAC3C,SAAO,YAAY,KAAK,KAAK,MAAM,SAAS;AAC9C;AAEM,IAAO,mBAAP,cAAgC,UAAS;EAG7C,YAAY,SAAiB,eAAsB;AACjD,UAAM,OAAO;AACb,SAAK,OAAO;AACZ,SAAK,gBAAgB;EACvB;;AAGI,IAAO,kBAAP,cAA+B,UAAS;EAI5C,YAAY,SAAiB,MAAc,QAAgB,MAAwB;AACjF,UAAM,SAAS,QAAQ,IAAI;AAC3B,SAAK,OAAO;AACZ,SAAK,SAAS;EAChB;;AAGI,IAAO,0BAAP,cAAuC,gBAAe;EAC1D,cAAA;AACE,UAAM,yBAAyB,2BAA2B,KAAK,MAAS;EAC1E;;AAGI,SAAU,0BAA0B,OAAU;AAClD,SAAO,YAAY,KAAK,KAAK,MAAM,SAAS;AAC9C;AAEM,IAAO,gCAAP,cAA6C,gBAAe;EAChE,cAAA;AACE,UAAM,gCAAgC,iCAAiC,KAAK,MAAS;EACvF;;AAGI,IAAO,8BAAP,cAA2C,gBAAe;EAC9D,YAAY,SAAe;AACzB,UAAM,SAAS,+BAA+B,KAAK,MAAS;EAC9D;;AAGI,IAAO,iCAAP,cAA8C,gBAAe;EAEjE,YAAY,SAAiB,UAAkD,MAAI;AACjF,UAAM,SAAS,kCAAkC,KAAK,MAAS;AAFjE,SAAA,UAAkD;AAGhD,SAAK,UAAU;EACjB;EAEA,SAAM;AACJ,WAAO;MACL,MAAM,KAAK;MACX,SAAS,KAAK;MACd,QAAQ,KAAK;MACb,SAAS,KAAK;;EAElB;;AAGI,IAAO,iCAAP,cAA8C,gBAAe;EAGjE,YAAY,SAAiB,UAAkD,MAAI;AACjF,UAAM,SAAS,kCAAkC,KAAK,MAAS;AAHjE,SAAA,UAAkD;AAIhD,SAAK,UAAU;EACjB;EAEA,SAAM;AACJ,WAAO;MACL,MAAM,KAAK;MACX,SAAS,KAAK;MACd,QAAQ,KAAK;MACb,SAAS,KAAK;;EAElB;;AAGI,IAAO,0BAAP,cAAuC,gBAAe;EAC1D,YAAY,SAAiB,QAAc;AACzC,UAAM,SAAS,2BAA2B,QAAQ,MAAS;EAC7D;;AAGI,SAAU,0BAA0B,OAAc;AACtD,SAAO,YAAY,KAAK,KAAK,MAAM,SAAS;AAC9C;AAOM,IAAO,wBAAP,cAAqC,gBAAe;EAMxD,YAAY,SAAiB,QAAgB,SAAiB;AAC5D,UAAM,SAAS,yBAAyB,QAAQ,eAAe;AAE/D,SAAK,UAAU;EACjB;;AAGI,SAAU,wBAAwB,OAAc;AACpD,SAAO,YAAY,KAAK,KAAK,MAAM,SAAS;AAC9C;;;;;;;;;;;;;;ACtHA,IAAMC,oBAAmB,CAAC,QACxB,IAAI,OAAO,IAAI,WAAW,IAAI,qBAAqB,IAAI,SAAS,KAAK,UAAU,GAAG;AAEpF,IAAM,sBAAsB,CAAC,KAAK,KAAK,GAAG;AAE1C,eAAsBC,aAAY,OAAc;;AAC9C,MAAI,CAAC,uBAAuB,KAAK,GAAG;AAClC,UAAM,IAAI,wBAAwBD,kBAAiB,KAAK,GAAG,CAAC;;AAG9D,MAAI,oBAAoB,SAAS,MAAM,MAAM,GAAG;AAE9C,UAAM,IAAI,wBAAwBA,kBAAiB,KAAK,GAAG,MAAM,MAAM;;AAGzE,MAAI;AACJ,MAAI;AACF,WAAO,MAAM,MAAM,KAAI;WAChB,GAAQ;AACf,UAAM,IAAI,iBAAiBA,kBAAiB,CAAC,GAAG,CAAC;;AAGnD,MAAI,YAAgC;AAEpC,QAAM,qBAAqB,wBAAwB,KAAK;AACxD,MACE,sBACA,mBAAmB,QAAO,KAAM,aAAa,YAAY,EAAE,aAC3D,OAAO,SAAS,YAChB,QACA,OAAO,KAAK,SAAS,UACrB;AACA,gBAAY,KAAK;aACR,OAAO,SAAS,YAAY,QAAQ,OAAO,KAAK,eAAe,UAAU;AAClF,gBAAY,KAAK;;AAGnB,MAAI,CAAC,WAAW;AAEd,QACE,OAAO,SAAS,YAChB,QACA,OAAO,KAAK,kBAAkB,YAC9B,KAAK,iBACL,MAAM,QAAQ,KAAK,cAAc,OAAO,KACxC,KAAK,cAAc,QAAQ,UAC3B,KAAK,cAAc,QAAQ,OAAO,CAAC,GAAY,MAAW,KAAK,OAAO,MAAM,UAAU,IAAI,GAC1F;AACA,YAAM,IAAI,sBACRA,kBAAiB,IAAI,GACrB,MAAM,QACN,KAAK,cAAc,OAAO;;aAGrB,cAAc,iBAAiB;AACxC,UAAM,IAAI,sBACRA,kBAAiB,IAAI,GACrB,MAAM,UACN,KAAA,KAAK,mBAAa,QAAA,OAAA,SAAA,SAAA,GAAE,YAAW,CAAA,CAAE;aAE1B,cAAc,qBAAqB;AAI5C,UAAM,IAAI,wBAAuB;;AAGnC,QAAM,IAAI,aAAaA,kBAAiB,IAAI,GAAG,MAAM,UAAU,KAAK,SAAS;AAC/E;AAEA,IAAME,qBAAoB,CACxB,QACA,SACA,YACA,SACE;AACF,QAAM,SAA+B,EAAE,QAAQ,UAAS,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,YAAW,CAAA,EAAE;AAE9E,MAAI,WAAW,OAAO;AACpB,WAAO;;AAGT,SAAO,UAAO,OAAA,OAAA,EAAK,gBAAgB,iCAAgC,GAAK,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,OAAO;AACxF,SAAO,OAAO,KAAK,UAAU,IAAI;AACjC,SAAA,OAAA,OAAA,OAAA,OAAA,CAAA,GAAY,MAAM,GAAK,UAAU;AACnC;AAaA,eAAsB,SACpB,SACA,QACA,KACA,SAA8B;;AAE9B,QAAM,UAAO,OAAA,OAAA,CAAA,GACR,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,OAAO;AAGrB,MAAI,CAAC,QAAQ,uBAAuB,GAAG;AACrC,YAAQ,uBAAuB,IAAI,aAAa,YAAY,EAAE;;AAGhE,MAAI,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,KAAK;AAChB,YAAQ,eAAe,IAAI,UAAU,QAAQ,GAAG;;AAGlD,QAAM,MAAK,KAAA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,WAAK,QAAA,OAAA,SAAA,KAAI,CAAA;AAC7B,MAAI,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,YAAY;AACvB,OAAG,aAAa,IAAI,QAAQ;;AAG9B,QAAM,cAAc,OAAO,KAAK,EAAE,EAAE,SAAS,MAAM,IAAI,gBAAgB,EAAE,EAAE,SAAQ,IAAK;AACxF,QAAM,OAAO,MAAMC,gBACjB,SACA,QACA,MAAM,aACN;IACE;IACA,eAAe,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS;KAE1B,CAAA,GACA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,IAAI;AAEf,UAAO,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,SAAQ,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,MAAM,IAAI,IAAI,EAAE,MAAI,OAAA,OAAA,CAAA,GAAO,IAAI,GAAI,OAAO,KAAI;AACjF;AAEA,eAAeA,gBACb,SACA,QACA,KACA,SACA,YACA,MAAa;AAEb,QAAM,gBAAgBD,mBAAkB,QAAQ,SAAS,YAAY,IAAI;AAEzE,MAAI;AAEJ,MAAI;AACF,aAAS,MAAM,QAAQ,KAAG,OAAA,OAAA,CAAA,GACrB,aAAa,CAAA;WAEX,GAAG;AACV,YAAQ,MAAM,CAAC;AAGf,UAAM,IAAI,wBAAwBF,kBAAiB,CAAC,GAAG,CAAC;;AAG1D,MAAI,CAAC,OAAO,IAAI;AACd,UAAMC,aAAY,MAAM;;AAG1B,MAAI,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,eAAe;AAC1B,WAAO;;AAGT,MAAI;AACF,WAAO,MAAM,OAAO,KAAI;WACjB,GAAQ;AACf,UAAMA,aAAY,CAAC;;AAEvB;AAEM,SAAU,iBAAiB,MAAS;;AACxC,MAAI,UAAU;AACd,MAAI,WAAW,IAAI,GAAG;AACpB,cAAO,OAAA,OAAA,CAAA,GAAQ,IAAI;AAEnB,QAAI,CAAC,KAAK,YAAY;AACpB,cAAQ,aAAa,UAAU,KAAK,UAAU;;;AAIlD,QAAM,QAAa,KAAA,KAAK,UAAI,QAAA,OAAA,SAAA,KAAK;AACjC,SAAO,EAAE,MAAM,EAAE,SAAS,KAAI,GAAI,OAAO,KAAI;AAC/C;AAEM,SAAU,yBAAyB,MAAS;AAChD,QAAM,WAAW,iBAAiB,IAAI;AAEtC,MACE,CAAC,SAAS,SACV,KAAK,iBACL,OAAO,KAAK,kBAAkB,YAC9B,MAAM,QAAQ,KAAK,cAAc,OAAO,KACxC,KAAK,cAAc,QAAQ,UAC3B,KAAK,cAAc,WACnB,OAAO,KAAK,cAAc,YAAY,YACtC,KAAK,cAAc,QAAQ,OAAO,CAAC,GAAY,MAAW,KAAK,OAAO,MAAM,UAAU,IAAI,GAC1F;AACA,aAAS,KAAK,gBAAgB,KAAK;;AAGrC,SAAO;AACT;AAEM,SAAU,cAAc,MAAS;;AACrC,QAAM,QAAa,KAAA,KAAK,UAAI,QAAA,OAAA,SAAA,KAAK;AACjC,SAAO,EAAE,MAAM,EAAE,KAAI,GAAI,OAAO,KAAI;AACtC;AAEM,SAAU,aAAa,MAAS;AACpC,SAAO,EAAE,MAAM,OAAO,KAAI;AAC5B;AAEM,SAAU,sBAAsB,MAAS;AAC7C,QAAM,EAAE,aAAa,WAAW,cAAc,aAAa,kBAAiB,IAAc,MAAT,OAAI,OAAK,MAApF,CAAA,eAAA,aAAA,gBAAA,eAAA,mBAAA,CAAiF;AAEvF,QAAM,aAAqC;IACzC;IACA;IACA;IACA;IACA;;AAGF,QAAM,OAAI,OAAA,OAAA,CAAA,GAAc,IAAI;AAC5B,SAAO;IACL,MAAM;MACJ;MACA;;IAEF,OAAO;;AAEX;AAEM,SAAU,uBAAuB,MAAS;AAC9C,SAAO;AACT;AAOA,SAAS,WAAW,MAAS;AAC3B,SAAO,KAAK,gBAAgB,KAAK,iBAAiB,KAAK;AACzD;;;;;;;;;;;;;;AClQA,IAAqB,iBAArB,MAAmC;EAUjC,YAAY,EACV,MAAM,IACN,UAAU,CAAA,GACV,OAAAG,OAAK,GAON;AACC,SAAK,MAAM;AACX,SAAK,UAAU;AACf,SAAK,QAAQC,cAAaD,MAAK;AAC/B,SAAK,MAAM;MACT,aAAa,KAAK,aAAa,KAAK,IAAI;MACxC,cAAc,KAAK,cAAc,KAAK,IAAI;;EAE9C;;;;;;EAOA,MAAM,QACJ,KACA,QAAuC,UAAQ;AAE/C,QAAI;AACF,YAAM,SAAS,KAAK,OAAO,QAAQ,GAAG,KAAK,GAAG,iBAAiB,KAAK,IAAI;QACtE,SAAS,KAAK;QACd;QACA,eAAe;OAChB;AACD,aAAO,EAAE,MAAM,MAAM,OAAO,KAAI;aACzB,OAAO;AACd,UAAI,YAAY,KAAK,GAAG;AACtB,eAAO,EAAE,MAAM,MAAM,MAAK;;AAG5B,YAAM;;EAEV;;;;;;EAOA,MAAM,kBACJ,OACA,UAMI,CAAA,GAAE;AAEN,QAAI;AACF,aAAO,MAAM,SAAS,KAAK,OAAO,QAAQ,GAAG,KAAK,GAAG,WAAW;QAC9D,MAAM,EAAE,OAAO,MAAM,QAAQ,KAAI;QACjC,SAAS,KAAK;QACd,YAAY,QAAQ;QACpB,OAAO;OACR;aACM,OAAO;AACd,UAAI,YAAY,KAAK,GAAG;AACtB,eAAO,EAAE,MAAM,EAAE,MAAM,KAAI,GAAI,MAAK;;AAGtC,YAAM;;EAEV;;;;;;;;EASA,MAAM,aAAa,QAA0B;AAC3C,QAAI;AACF,YAAM,EAAE,QAAO,IAAc,QAAT,OAAIE,QAAK,QAAvB,CAAA,SAAA,CAAoB;AAC1B,YAAM,OAAI,OAAA,OAAA,OAAA,OAAA,CAAA,GAAa,IAAI,GAAK,OAAO;AACvC,UAAI,cAAc,MAAM;AAEtB,aAAK,YAAY,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM;AACvB,eAAO,KAAK,UAAU;;AAExB,aAAO,MAAM,SAAS,KAAK,OAAO,QAAQ,GAAG,KAAK,GAAG,wBAAwB;QAC3E;QACA,SAAS,KAAK;QACd,OAAO;QACP,YAAY,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS;OACtB;aACM,OAAO;AACd,UAAI,YAAY,KAAK,GAAG;AACtB,eAAO;UACL,MAAM;YACJ,YAAY;YACZ,MAAM;;UAER;;;AAGJ,YAAM;;EAEV;;;;;;EAOA,MAAM,WAAW,YAA+B;AAC9C,QAAI;AACF,aAAO,MAAM,SAAS,KAAK,OAAO,QAAQ,GAAG,KAAK,GAAG,gBAAgB;QACnE,MAAM;QACN,SAAS,KAAK;QACd,OAAO;OACR;aACM,OAAO;AACd,UAAI,YAAY,KAAK,GAAG;AACtB,eAAO,EAAE,MAAM,EAAE,MAAM,KAAI,GAAI,MAAK;;AAGtC,YAAM;;EAEV;;;;;;;EAQA,MAAM,UACJ,QAAmB;;AAKnB,QAAI;AACF,YAAM,aAAyB,EAAE,UAAU,MAAM,UAAU,GAAG,OAAO,EAAC;AACtE,YAAM,WAAW,MAAM,SAAS,KAAK,OAAO,OAAO,GAAG,KAAK,GAAG,gBAAgB;QAC5E,SAAS,KAAK;QACd,eAAe;QACf,OAAO;UACL,OAAM,MAAA,KAAA,WAAM,QAAN,WAAM,SAAA,SAAN,OAAQ,UAAI,QAAA,OAAA,SAAA,SAAA,GAAE,SAAQ,OAAE,QAAA,OAAA,SAAA,KAAI;UAClC,WAAU,MAAA,KAAA,WAAM,QAAN,WAAM,SAAA,SAAN,OAAQ,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE,SAAQ,OAAE,QAAA,OAAA,SAAA,KAAI;;QAE3C,OAAO;OACR;AACD,UAAI,SAAS;AAAO,cAAM,SAAS;AAEnC,YAAM,QAAQ,MAAM,SAAS,KAAI;AACjC,YAAM,SAAQ,KAAA,SAAS,QAAQ,IAAI,eAAe,OAAC,QAAA,OAAA,SAAA,KAAI;AACvD,YAAM,SAAQ,MAAA,KAAA,SAAS,QAAQ,IAAI,MAAM,OAAC,QAAA,OAAA,SAAA,SAAA,GAAE,MAAM,GAAG,OAAC,QAAA,OAAA,SAAA,KAAI,CAAA;AAC1D,UAAI,MAAM,SAAS,GAAG;AACpB,cAAM,QAAQ,CAAC,SAAgB;AAC7B,gBAAM,OAAO,SAAS,KAAK,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,EAAE,UAAU,GAAG,CAAC,CAAC;AACtE,gBAAM,MAAM,KAAK,MAAM,KAAK,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,CAAC;AACvD,qBAAW,GAAG,GAAG,MAAM,IAAI;QAC7B,CAAC;AAED,mBAAW,QAAQ,SAAS,KAAK;;AAEnC,aAAO,EAAE,MAAI,OAAA,OAAA,OAAA,OAAA,CAAA,GAAO,KAAK,GAAK,UAAU,GAAI,OAAO,KAAI;aAChD,OAAO;AACd,UAAI,YAAY,KAAK,GAAG;AACtB,eAAO,EAAE,MAAM,EAAE,OAAO,CAAA,EAAE,GAAI,MAAK;;AAErC,YAAM;;EAEV;;;;;;;;EASA,MAAM,YAAY,KAAW;AAC3B,QAAI;AACF,aAAO,MAAM,SAAS,KAAK,OAAO,OAAO,GAAG,KAAK,GAAG,gBAAgB,GAAG,IAAI;QACzE,SAAS,KAAK;QACd,OAAO;OACR;aACM,OAAO;AACd,UAAI,YAAY,KAAK,GAAG;AACtB,eAAO,EAAE,MAAM,EAAE,MAAM,KAAI,GAAI,MAAK;;AAGtC,YAAM;;EAEV;;;;;;;;EASA,MAAM,eAAe,KAAa,YAA+B;AAC/D,QAAI;AACF,aAAO,MAAM,SAAS,KAAK,OAAO,OAAO,GAAG,KAAK,GAAG,gBAAgB,GAAG,IAAI;QACzE,MAAM;QACN,SAAS,KAAK;QACd,OAAO;OACR;aACM,OAAO;AACd,UAAI,YAAY,KAAK,GAAG;AACtB,eAAO,EAAE,MAAM,EAAE,MAAM,KAAI,GAAI,MAAK;;AAGtC,YAAM;;EAEV;;;;;;;;;;EAWA,MAAM,WAAW,IAAY,mBAAmB,OAAK;AACnD,QAAI;AACF,aAAO,MAAM,SAAS,KAAK,OAAO,UAAU,GAAG,KAAK,GAAG,gBAAgB,EAAE,IAAI;QAC3E,SAAS,KAAK;QACd,MAAM;UACJ,oBAAoB;;QAEtB,OAAO;OACR;aACM,OAAO;AACd,UAAI,YAAY,KAAK,GAAG;AACtB,eAAO,EAAE,MAAM,EAAE,MAAM,KAAI,GAAI,MAAK;;AAGtC,YAAM;;EAEV;EAEQ,MAAM,aACZ,QAAqC;AAErC,QAAI;AACF,YAAM,EAAE,MAAM,MAAK,IAAK,MAAM,SAC5B,KAAK,OACL,OACA,GAAG,KAAK,GAAG,gBAAgB,OAAO,MAAM,YACxC;QACE,SAAS,KAAK;QACd,OAAO,CAAC,YAAgB;AACtB,iBAAO,EAAE,MAAM,EAAE,QAAO,GAAI,OAAO,KAAI;QACzC;OACD;AAEH,aAAO,EAAE,MAAM,MAAK;aACb,OAAO;AACd,UAAI,YAAY,KAAK,GAAG;AACtB,eAAO,EAAE,MAAM,MAAM,MAAK;;AAG5B,YAAM;;EAEV;EAEQ,MAAM,cACZ,QAAsC;AAEtC,QAAI;AACF,YAAM,OAAO,MAAM,SACjB,KAAK,OACL,UACA,GAAG,KAAK,GAAG,gBAAgB,OAAO,MAAM,YAAY,OAAO,EAAE,IAC7D;QACE,SAAS,KAAK;OACf;AAGH,aAAO,EAAE,MAAM,OAAO,KAAI;aACnB,OAAO;AACd,UAAI,YAAY,KAAK,GAAG;AACtB,eAAO,EAAE,MAAM,MAAM,MAAK;;AAG5B,YAAM;;EAEV;;;;ACrUK,IAAM,sBAAwC;EACnD,SAAS,CAAC,QAAO;AACf,QAAI,CAAC,qBAAoB,GAAI;AAC3B,aAAO;;AAGT,WAAO,WAAW,aAAa,QAAQ,GAAG;EAC5C;EACA,SAAS,CAAC,KAAK,UAAS;AACtB,QAAI,CAAC,qBAAoB,GAAI;AAC3B;;AAGF,eAAW,aAAa,QAAQ,KAAK,KAAK;EAC5C;EACA,YAAY,CAAC,QAAO;AAClB,QAAI,CAAC,qBAAoB,GAAI;AAC3B;;AAGF,eAAW,aAAa,WAAW,GAAG;EACxC;;AAOI,SAAU,0BAA0B,QAAmC,CAAA,GAAE;AAC7E,SAAO;IACL,SAAS,CAAC,QAAO;AACf,aAAO,MAAM,GAAG,KAAK;IACvB;IAEA,SAAS,CAAC,KAAK,UAAS;AACtB,YAAM,GAAG,IAAI;IACf;IAEA,YAAY,CAAC,QAAO;AAClB,aAAO,MAAM,GAAG;IAClB;;AAEJ;;;AC7CM,SAAU,qBAAkB;AAChC,MAAI,OAAO,eAAe;AAAU;AACpC,MAAI;AACF,WAAO,eAAe,OAAO,WAAW,aAAa;MACnD,KAAK,WAAA;AACH,eAAO;MACT;MACA,cAAc;KACf;AAED,cAAU,aAAa;AAEvB,WAAO,OAAO,UAAU;WACjB,GAAG;AACV,QAAI,OAAO,SAAS,aAAa;AAE/B,WAAK,aAAa;;;AAGxB;;;ACjBO,IAAM,YAAY;;;;EAIvB,OAAO,CAAC,EACN,cACA,qBAAoB,KACpB,WAAW,gBACX,WAAW,aAAa,QAAQ,gCAAgC,MAAM;;AASpE,IAAgB,0BAAhB,cAAgD,MAAK;EAGzD,YAAY,SAAe;AACzB,UAAM,OAAO;AAHC,SAAA,mBAAmB;EAInC;;AAGI,IAAO,mCAAP,cAAgD,wBAAuB;;AA2B7E,eAAsB,cACpB,MACA,gBACA,IAAoB;AAEpB,MAAI,UAAU,OAAO;AACnB,YAAQ,IAAI,oDAAoD,MAAM,cAAc;;AAGtF,QAAM,kBAAkB,IAAI,WAAW,gBAAe;AAEtD,MAAI,iBAAiB,GAAG;AACtB,eAAW,MAAK;AACd,sBAAgB,MAAK;AACrB,UAAI,UAAU,OAAO;AACnB,gBAAQ,IAAI,wDAAwD,IAAI;;IAE5E,GAAG,cAAc;;AAKnB,SAAO,MAAM,WAAW,UAAU,MAAM,QACtC,MACA,mBAAmB,IACf;IACE,MAAM;IACN,aAAa;MAEf;IACE,MAAM;IACN,QAAQ,gBAAgB;KAE9B,OAAO,SAAQ;AACb,QAAI,MAAM;AACR,UAAI,UAAU,OAAO;AACnB,gBAAQ,IAAI,gDAAgD,MAAM,KAAK,IAAI;;AAG7E,UAAI;AACF,eAAO,MAAM,GAAE;;AAEf,YAAI,UAAU,OAAO;AACnB,kBAAQ,IAAI,gDAAgD,MAAM,KAAK,IAAI;;;WAG1E;AACL,UAAI,mBAAmB,GAAG;AACxB,YAAI,UAAU,OAAO;AACnB,kBAAQ,IAAI,iEAAiE,IAAI;;AAGnF,cAAM,IAAI,iCACR,sDAAsD,IAAI,sBAAsB;aAE7E;AACL,YAAI,UAAU,OAAO;AACnB,cAAI;AACF,kBAAM,SAAS,MAAM,WAAW,UAAU,MAAM,MAAK;AAErD,oBAAQ,IACN,oDACA,KAAK,UAAU,QAAQ,MAAM,IAAI,CAAC;mBAE7B,GAAQ;AACf,oBAAQ,KACN,wEACA,CAAC;;;AASP,gBAAQ,KACN,yPAAyP;AAG3P,eAAO,MAAM,GAAE;;;EAGrB,CAAC;AAEL;;;AClDA,mBAAkB;AAElB,IAAM,kBAAqF;EACzF,KAAK;EACL,YAAY;EACZ,kBAAkB;EAClB,gBAAgB;EAChB,oBAAoB;EACpB,SAASC;EACT,UAAU;EACV,OAAO;EACP,8BAA8B;;AAIhC,IAAM,6BAA6B,KAAK;AAIxC,IAAM,8BAA8B;AAEpC,eAAe,SAAY,MAAc,gBAAwB,IAAoB;AACnF,SAAO,MAAM,GAAE;AACjB;AAEA,IAAqB,eAArB,MAAqB,cAAY;;;;EA2D/B,YAAY,SAA4B;;AAnC9B,SAAA,gBAAkD;AAClD,SAAA,sBAAiD,oBAAI,IAAG;AACxD,SAAA,oBAA2D;AAC3D,SAAA,4BAAyD;AACzD,SAAA,qBAA8D;AAO9D,SAAA,oBAAsD;AACtD,SAAA,qBAAqB;AAKrB,SAAA,+BAA+B;AAC/B,SAAA,4BAA4B;AAG5B,SAAA,eAAe;AACf,SAAA,gBAAgC,CAAA;AAKhC,SAAA,mBAA4C;AAG5C,SAAA,SAAoD,QAAQ;AAMpE,SAAK,aAAa,cAAa;AAC/B,kBAAa,kBAAkB;AAE/B,QAAI,KAAK,aAAa,KAAK,UAAS,GAAI;AACtC,cAAQ,KACN,8MAA8M;;AAIlN,UAAM,WAAQ,OAAA,OAAA,OAAA,OAAA,CAAA,GAAQ,eAAe,GAAK,OAAO;AAEjD,SAAK,mBAAmB,CAAC,CAAC,SAAS;AACnC,QAAI,OAAO,SAAS,UAAU,YAAY;AACxC,WAAK,SAAS,SAAS;;AAGzB,SAAK,iBAAiB,SAAS;AAC/B,SAAK,aAAa,SAAS;AAC3B,SAAK,mBAAmB,SAAS;AACjC,SAAK,QAAQ,IAAI,eAAe;MAC9B,KAAK,SAAS;MACd,SAAS,SAAS;MAClB,OAAO,SAAS;KACjB;AAED,SAAK,MAAM,SAAS;AACpB,SAAK,UAAU,SAAS;AACxB,SAAK,QAAQC,cAAa,SAAS,KAAK;AACxC,SAAK,OAAO,SAAS,QAAQ;AAC7B,SAAK,qBAAqB,SAAS;AACnC,SAAK,WAAW,SAAS;AACzB,SAAK,+BAA+B,SAAS;AAE7C,QAAI,SAAS,MAAM;AACjB,WAAK,OAAO,SAAS;eACZ,UAAS,OAAM,KAAA,eAAU,QAAV,eAAU,SAAA,SAAV,WAAY,eAAS,QAAA,OAAA,SAAA,SAAA,GAAE,QAAO;AACtD,WAAK,OAAO;WACP;AACL,WAAK,OAAO;;AAGd,SAAK,MAAM;MACT,QAAQ,KAAK,QAAQ,KAAK,IAAI;MAC9B,QAAQ,KAAK,QAAQ,KAAK,IAAI;MAC9B,UAAU,KAAK,UAAU,KAAK,IAAI;MAClC,WAAW,KAAK,WAAW,KAAK,IAAI;MACpC,aAAa,KAAK,aAAa,KAAK,IAAI;MACxC,oBAAoB,KAAK,oBAAoB,KAAK,IAAI;MACtD,gCAAgC,KAAK,gCAAgC,KAAK,IAAI;;AAGhF,QAAI,KAAK,gBAAgB;AACvB,UAAI,SAAS,SAAS;AACpB,aAAK,UAAU,SAAS;aACnB;AACL,YAAI,qBAAoB,GAAI;AAC1B,eAAK,UAAU;eACV;AACL,eAAK,gBAAgB,CAAA;AACrB,eAAK,UAAU,0BAA0B,KAAK,aAAa;;;WAG1D;AACL,WAAK,gBAAgB,CAAA;AACrB,WAAK,UAAU,0BAA0B,KAAK,aAAa;;AAG7D,QAAI,UAAS,KAAM,WAAW,oBAAoB,KAAK,kBAAkB,KAAK,YAAY;AACxF,UAAI;AACF,aAAK,mBAAmB,IAAI,WAAW,iBAAiB,KAAK,UAAU;eAChE,GAAQ;AACf,gBAAQ,MACN,0FACA,CAAC;;AAIL,OAAA,KAAA,KAAK,sBAAgB,QAAA,OAAA,SAAA,SAAA,GAAE,iBAAiB,WAAW,OAAO,UAAS;AACjE,aAAK,OAAO,4DAA4D,KAAK;AAE7E,cAAM,KAAK,sBAAsB,MAAM,KAAK,OAAO,MAAM,KAAK,SAAS,KAAK;MAC9E,CAAC;;AAGH,SAAK,WAAU;EACjB;EAEQ,UAAU,MAAW;AAC3B,QAAI,KAAK,kBAAkB;AACzB,WAAK,OACH,gBAAgB,KAAK,UAAU,KAAKC,QAAO,MAAK,oBAAI,KAAI,GAAG,YAAW,CAAE,IACxE,GAAG,IAAI;;AAIX,WAAO;EACT;;;;;;EAOA,MAAM,aAAU;AACd,QAAI,KAAK,mBAAmB;AAC1B,aAAO,MAAM,KAAK;;AAGpB,SAAK,qBAAqB,YAAW;AACnC,aAAO,MAAM,KAAK,aAAa,IAAI,YAAW;AAC5C,eAAO,MAAM,KAAK,YAAW;MAC/B,CAAC;IACH,GAAE;AAEF,WAAO,MAAM,KAAK;EACpB;;;;;;;EAQQ,MAAM,cAAW;AACvB,QAAI;AACF,YAAM,aAAa,UAAS,IAAK,MAAM,KAAK,YAAW,IAAK;AAC5D,WAAK,OAAO,kBAAkB,SAAS,gBAAgB,UAAU;AAEjE,UAAI,cAAe,KAAK,sBAAsB,KAAK,qBAAoB,GAAK;AAC1E,cAAM,EAAE,MAAM,MAAK,IAAK,MAAM,KAAK,mBAAmB,UAAU;AAChE,YAAI,OAAO;AACT,eAAK,OAAO,kBAAkB,oCAAoC,KAAK;AAIvE,eACE,UAAK,QAAL,UAAK,SAAA,SAAL,MAAO,aAAY,iCACnB,UAAK,QAAL,UAAK,SAAA,SAAL,MAAO,aAAY,8CACnB;AACA,mBAAO,EAAE,MAAK;;AAKhB,gBAAM,KAAK,eAAc;AAEzB,iBAAO,EAAE,MAAK;;AAGhB,cAAM,EAAE,SAAS,aAAY,IAAK;AAElC,aAAK,OACH,kBACA,2BACA,SACA,iBACA,YAAY;AAGd,cAAM,KAAK,aAAa,OAAO;AAE/B,mBAAW,YAAW;AACpB,cAAI,iBAAiB,YAAY;AAC/B,kBAAM,KAAK,sBAAsB,qBAAqB,OAAO;iBACxD;AACL,kBAAM,KAAK,sBAAsB,aAAa,OAAO;;QAEzD,GAAG,CAAC;AAEJ,eAAO,EAAE,OAAO,KAAI;;AAGtB,YAAM,KAAK,mBAAkB;AAC7B,aAAO,EAAE,OAAO,KAAI;aACb,OAAO;AACd,UAAI,YAAY,KAAK,GAAG;AACtB,eAAO,EAAE,MAAK;;AAGhB,aAAO;QACL,OAAO,IAAI,iBAAiB,0CAA0C,KAAK;;;AAG7E,YAAM,KAAK,wBAAuB;AAClC,WAAK,OAAO,kBAAkB,KAAK;;EAEvC;;;;;;EAOA,MAAM,kBAAkB,aAA0C;;AAChE,QAAI;AACF,YAAM,MAAM,MAAM,SAAS,KAAK,OAAO,QAAQ,GAAG,KAAK,GAAG,WAAW;QACnE,SAAS,KAAK;QACd,MAAM;UACJ,OAAM,MAAA,KAAA,gBAAW,QAAX,gBAAW,SAAA,SAAX,YAAa,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE,UAAI,QAAA,OAAA,SAAA,KAAI,CAAA;UACpC,sBAAsB,EAAE,gBAAe,KAAA,gBAAW,QAAX,gBAAW,SAAA,SAAX,YAAa,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE,aAAY;;QAE3E,OAAO;OACR;AACD,YAAM,EAAE,MAAM,MAAK,IAAK;AAExB,UAAI,SAAS,CAAC,MAAM;AAClB,eAAO,EAAE,MAAM,EAAE,MAAM,MAAM,SAAS,KAAI,GAAI,MAAY;;AAE5D,YAAM,UAA0B,KAAK;AACrC,YAAM,OAAoB,KAAK;AAE/B,UAAI,KAAK,SAAS;AAChB,cAAM,KAAK,aAAa,KAAK,OAAO;AACpC,cAAM,KAAK,sBAAsB,aAAa,OAAO;;AAGvD,aAAO,EAAE,MAAM,EAAE,MAAM,QAAO,GAAI,OAAO,KAAI;aACtC,OAAO;AACd,UAAI,YAAY,KAAK,GAAG;AACtB,eAAO,EAAE,MAAM,EAAE,MAAM,MAAM,SAAS,KAAI,GAAI,MAAK;;AAGrD,YAAM;;EAEV;;;;;;;;;;;EAYA,MAAM,OAAO,aAA0C;;AACrD,QAAI;AACF,UAAI;AACJ,UAAI,WAAW,aAAa;AAC1B,cAAM,EAAE,OAAO,UAAU,QAAO,IAAK;AACrC,YAAI,gBAA+B;AACnC,YAAI,sBAAqC;AACzC,YAAI,KAAK,aAAa,QAAQ;AAC5B;AAAC,WAAC,eAAe,mBAAmB,IAAI,MAAM,0BAC5C,KAAK,SACL,KAAK,UAAU;;AAGnB,cAAM,MAAM,SAAS,KAAK,OAAO,QAAQ,GAAG,KAAK,GAAG,WAAW;UAC7D,SAAS,KAAK;UACd,YAAY,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS;UACrB,MAAM;YACJ;YACA;YACA,OAAM,KAAA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,UAAI,QAAA,OAAA,SAAA,KAAI,CAAA;YACvB,sBAAsB,EAAE,eAAe,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,aAAY;YAC5D,gBAAgB;YAChB,uBAAuB;;UAEzB,OAAO;SACR;iBACQ,WAAW,aAAa;AACjC,cAAM,EAAE,OAAO,UAAU,QAAO,IAAK;AACrC,cAAM,MAAM,SAAS,KAAK,OAAO,QAAQ,GAAG,KAAK,GAAG,WAAW;UAC7D,SAAS,KAAK;UACd,MAAM;YACJ;YACA;YACA,OAAM,KAAA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,UAAI,QAAA,OAAA,SAAA,KAAI,CAAA;YACvB,UAAS,KAAA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,aAAO,QAAA,OAAA,SAAA,KAAI;YAC7B,sBAAsB,EAAE,eAAe,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,aAAY;;UAE9D,OAAO;SACR;aACI;AACL,cAAM,IAAI,4BACR,iEAAiE;;AAIrE,YAAM,EAAE,MAAM,MAAK,IAAK;AAExB,UAAI,SAAS,CAAC,MAAM;AAClB,eAAO,EAAE,MAAM,EAAE,MAAM,MAAM,SAAS,KAAI,GAAI,MAAY;;AAG5D,YAAM,UAA0B,KAAK;AACrC,YAAM,OAAoB,KAAK;AAE/B,UAAI,KAAK,SAAS;AAChB,cAAM,KAAK,aAAa,KAAK,OAAO;AACpC,cAAM,KAAK,sBAAsB,aAAa,OAAO;;AAGvD,aAAO,EAAE,MAAM,EAAE,MAAM,QAAO,GAAI,OAAO,KAAI;aACtC,OAAO;AACd,UAAI,YAAY,KAAK,GAAG;AACtB,eAAO,EAAE,MAAM,EAAE,MAAM,MAAM,SAAS,KAAI,GAAI,MAAK;;AAGrD,YAAM;;EAEV;;;;;;;;;EAUA,MAAM,mBACJ,aAA0C;AAE1C,QAAI;AACF,UAAI;AACJ,UAAI,WAAW,aAAa;AAC1B,cAAM,EAAE,OAAO,UAAU,QAAO,IAAK;AACrC,cAAM,MAAM,SAAS,KAAK,OAAO,QAAQ,GAAG,KAAK,GAAG,8BAA8B;UAChF,SAAS,KAAK;UACd,MAAM;YACJ;YACA;YACA,sBAAsB,EAAE,eAAe,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,aAAY;;UAE9D,OAAO;SACR;iBACQ,WAAW,aAAa;AACjC,cAAM,EAAE,OAAO,UAAU,QAAO,IAAK;AACrC,cAAM,MAAM,SAAS,KAAK,OAAO,QAAQ,GAAG,KAAK,GAAG,8BAA8B;UAChF,SAAS,KAAK;UACd,MAAM;YACJ;YACA;YACA,sBAAsB,EAAE,eAAe,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,aAAY;;UAE9D,OAAO;SACR;aACI;AACL,cAAM,IAAI,4BACR,iEAAiE;;AAGrE,YAAM,EAAE,MAAM,MAAK,IAAK;AAExB,UAAI,OAAO;AACT,eAAO,EAAE,MAAM,EAAE,MAAM,MAAM,SAAS,KAAI,GAAI,MAAK;iBAC1C,CAAC,QAAQ,CAAC,KAAK,WAAW,CAAC,KAAK,MAAM;AAC/C,eAAO,EAAE,MAAM,EAAE,MAAM,MAAM,SAAS,KAAI,GAAI,OAAO,IAAI,8BAA6B,EAAE;;AAE1F,UAAI,KAAK,SAAS;AAChB,cAAM,KAAK,aAAa,KAAK,OAAO;AACpC,cAAM,KAAK,sBAAsB,aAAa,KAAK,OAAO;;AAE5D,aAAO;QACL,MAAI,OAAA,OAAA,EACF,MAAM,KAAK,MACX,SAAS,KAAK,QAAO,GACjB,KAAK,gBAAgB,EAAE,cAAc,KAAK,cAAa,IAAK,IAAK;QAEvE;;aAEK,OAAO;AACd,UAAI,YAAY,KAAK,GAAG;AACtB,eAAO,EAAE,MAAM,EAAE,MAAM,MAAM,SAAS,KAAI,GAAI,MAAK;;AAErD,YAAM;;EAEV;;;;;EAMA,MAAM,gBAAgB,aAAuC;;AAC3D,WAAO,MAAM,KAAK,sBAAsB,YAAY,UAAU;MAC5D,aAAY,KAAA,YAAY,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE;MACjC,SAAQ,KAAA,YAAY,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE;MAC7B,cAAa,KAAA,YAAY,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE;MAClC,sBAAqB,KAAA,YAAY,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE;KAC3C;EACH;;;;EAKA,MAAM,uBAAuB,UAAgB;AAC3C,UAAM,KAAK;AAEX,WAAO,KAAK,aAAa,IAAI,YAAW;AACtC,aAAO,KAAK,wBAAwB,QAAQ;IAC9C,CAAC;EACH;EAEQ,MAAM,wBAAwB,UAAgB;AAOpD,UAAM,cAAc,MAAM,aAAa,KAAK,SAAS,GAAG,KAAK,UAAU,gBAAgB;AACvF,UAAM,CAAC,cAAc,YAAY,KAAM,gBAAW,QAAX,gBAAW,SAAX,cAAe,IAAe,MAAM,GAAG;AAE9E,QAAI;AACF,YAAM,EAAE,MAAM,MAAK,IAAK,MAAM,SAC5B,KAAK,OACL,QACA,GAAG,KAAK,GAAG,0BACX;QACE,SAAS,KAAK;QACd,MAAM;UACJ,WAAW;UACX,eAAe;;QAEjB,OAAO;OACR;AAEH,YAAM,gBAAgB,KAAK,SAAS,GAAG,KAAK,UAAU,gBAAgB;AACtE,UAAI,OAAO;AACT,cAAM;;AAER,UAAI,CAAC,QAAQ,CAAC,KAAK,WAAW,CAAC,KAAK,MAAM;AACxC,eAAO;UACL,MAAM,EAAE,MAAM,MAAM,SAAS,MAAM,cAAc,KAAI;UACrD,OAAO,IAAI,8BAA6B;;;AAG5C,UAAI,KAAK,SAAS;AAChB,cAAM,KAAK,aAAa,KAAK,OAAO;AACpC,cAAM,KAAK,sBAAsB,aAAa,KAAK,OAAO;;AAE5D,aAAO,EAAE,MAAI,OAAA,OAAA,OAAA,OAAA,CAAA,GAAO,IAAI,GAAA,EAAE,cAAc,iBAAY,QAAZ,iBAAY,SAAZ,eAAgB,KAAI,CAAA,GAAI,MAAK;aAC9D,OAAO;AACd,UAAI,YAAY,KAAK,GAAG;AACtB,eAAO,EAAE,MAAM,EAAE,MAAM,MAAM,SAAS,MAAM,cAAc,KAAI,GAAI,MAAK;;AAGzE,YAAM;;EAEV;;;;;EAMA,MAAM,kBAAkB,aAAyC;AAC/D,QAAI;AACF,YAAM,EAAE,SAAS,UAAU,OAAO,cAAc,MAAK,IAAK;AAE1D,YAAM,MAAM,MAAM,SAAS,KAAK,OAAO,QAAQ,GAAG,KAAK,GAAG,8BAA8B;QACtF,SAAS,KAAK;QACd,MAAM;UACJ;UACA,UAAU;UACV;UACA;UACA,sBAAsB,EAAE,eAAe,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,aAAY;;QAE9D,OAAO;OACR;AAED,YAAM,EAAE,MAAM,MAAK,IAAK;AACxB,UAAI,OAAO;AACT,eAAO,EAAE,MAAM,EAAE,MAAM,MAAM,SAAS,KAAI,GAAI,MAAK;iBAC1C,CAAC,QAAQ,CAAC,KAAK,WAAW,CAAC,KAAK,MAAM;AAC/C,eAAO;UACL,MAAM,EAAE,MAAM,MAAM,SAAS,KAAI;UACjC,OAAO,IAAI,8BAA6B;;;AAG5C,UAAI,KAAK,SAAS;AAChB,cAAM,KAAK,aAAa,KAAK,OAAO;AACpC,cAAM,KAAK,sBAAsB,aAAa,KAAK,OAAO;;AAE5D,aAAO,EAAE,MAAM,MAAK;aACb,OAAO;AACd,UAAI,YAAY,KAAK,GAAG;AACtB,eAAO,EAAE,MAAM,EAAE,MAAM,MAAM,SAAS,KAAI,GAAI,MAAK;;AAErD,YAAM;;EAEV;;;;;;;;;;;;;;;;;;EAmBA,MAAM,cAAc,aAA8C;;AAChE,QAAI;AACF,UAAI,WAAW,aAAa;AAC1B,cAAM,EAAE,OAAO,QAAO,IAAK;AAC3B,YAAI,gBAA+B;AACnC,YAAI,sBAAqC;AACzC,YAAI,KAAK,aAAa,QAAQ;AAC5B;AAAC,WAAC,eAAe,mBAAmB,IAAI,MAAM,0BAC5C,KAAK,SACL,KAAK,UAAU;;AAGnB,cAAM,EAAE,MAAK,IAAK,MAAM,SAAS,KAAK,OAAO,QAAQ,GAAG,KAAK,GAAG,QAAQ;UACtE,SAAS,KAAK;UACd,MAAM;YACJ;YACA,OAAM,KAAA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,UAAI,QAAA,OAAA,SAAA,KAAI,CAAA;YACvB,cAAa,KAAA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,sBAAgB,QAAA,OAAA,SAAA,KAAI;YAC1C,sBAAsB,EAAE,eAAe,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,aAAY;YAC5D,gBAAgB;YAChB,uBAAuB;;UAEzB,YAAY,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS;SACtB;AACD,eAAO,EAAE,MAAM,EAAE,MAAM,MAAM,SAAS,KAAI,GAAI,MAAK;;AAErD,UAAI,WAAW,aAAa;AAC1B,cAAM,EAAE,OAAO,QAAO,IAAK;AAC3B,cAAM,EAAE,MAAM,MAAK,IAAK,MAAM,SAAS,KAAK,OAAO,QAAQ,GAAG,KAAK,GAAG,QAAQ;UAC5E,SAAS,KAAK;UACd,MAAM;YACJ;YACA,OAAM,KAAA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,UAAI,QAAA,OAAA,SAAA,KAAI,CAAA;YACvB,cAAa,KAAA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,sBAAgB,QAAA,OAAA,SAAA,KAAI;YAC1C,sBAAsB,EAAE,eAAe,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,aAAY;YAC5D,UAAS,KAAA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,aAAO,QAAA,OAAA,SAAA,KAAI;;SAEhC;AACD,eAAO,EAAE,MAAM,EAAE,MAAM,MAAM,SAAS,MAAM,WAAW,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM,WAAU,GAAI,MAAK;;AAElF,YAAM,IAAI,4BAA4B,mDAAmD;aAClF,OAAO;AACd,UAAI,YAAY,KAAK,GAAG;AACtB,eAAO,EAAE,MAAM,EAAE,MAAM,MAAM,SAAS,KAAI,GAAI,MAAK;;AAGrD,YAAM;;EAEV;;;;EAKA,MAAM,UAAU,QAAuB;;AACrC,QAAI;AACF,UAAI,aAAiC;AACrC,UAAI,eAAmC;AACvC,UAAI,aAAa,QAAQ;AACvB,sBAAa,KAAA,OAAO,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE;AAC7B,wBAAe,KAAA,OAAO,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE;;AAEjC,YAAM,EAAE,MAAM,MAAK,IAAK,MAAM,SAAS,KAAK,OAAO,QAAQ,GAAG,KAAK,GAAG,WAAW;QAC/E,SAAS,KAAK;QACd,MAAI,OAAA,OAAA,OAAA,OAAA,CAAA,GACC,MAAM,GAAA,EACT,sBAAsB,EAAE,eAAe,aAAY,EAAE,CAAA;QAEvD;QACA,OAAO;OACR;AAED,UAAI,OAAO;AACT,cAAM;;AAGR,UAAI,CAAC,MAAM;AACT,cAAM,IAAI,MAAM,0CAA0C;;AAG5D,YAAM,UAA0B,KAAK;AACrC,YAAM,OAAa,KAAK;AAExB,UAAI,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,cAAc;AACzB,cAAM,KAAK,aAAa,OAAkB;AAC1C,cAAM,KAAK,sBACT,OAAO,QAAQ,aAAa,sBAAsB,aAClD,OAAO;;AAIX,aAAO,EAAE,MAAM,EAAE,MAAM,QAAO,GAAI,OAAO,KAAI;aACtC,OAAO;AACd,UAAI,YAAY,KAAK,GAAG;AACtB,eAAO,EAAE,MAAM,EAAE,MAAM,MAAM,SAAS,KAAI,GAAI,MAAK;;AAGrD,YAAM;;EAEV;;;;;;;;;;;;;;;EAgBA,MAAM,cAAc,QAAqB;;AACvC,QAAI;AACF,UAAI,gBAA+B;AACnC,UAAI,sBAAqC;AACzC,UAAI,KAAK,aAAa,QAAQ;AAC5B;AAAC,SAAC,eAAe,mBAAmB,IAAI,MAAM,0BAC5C,KAAK,SACL,KAAK,UAAU;;AAInB,aAAO,MAAM,SAAS,KAAK,OAAO,QAAQ,GAAG,KAAK,GAAG,QAAQ;QAC3D,MAAI,OAAA,OAAA,OAAA,OAAA,OAAA,OAAA,OAAA,OAAA,OAAA,OAAA,CAAA,GACE,gBAAgB,SAAS,EAAE,aAAa,OAAO,WAAU,IAAK,IAAK,GACnE,YAAY,SAAS,EAAE,QAAQ,OAAO,OAAM,IAAK,IAAK,GAAA,EAC1D,cAAa,MAAA,KAAA,OAAO,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE,gBAAU,QAAA,OAAA,SAAA,KAAI,OAAS,CAAA,KAChD,KAAA,WAAM,QAAN,WAAM,SAAA,SAAN,OAAQ,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE,gBACjB,EAAE,sBAAsB,EAAE,eAAe,OAAO,QAAQ,aAAY,EAAE,IACtE,IAAK,GAAA,EACT,oBAAoB,MACpB,gBAAgB,eAChB,uBAAuB,oBAAmB,CAAA;QAE5C,SAAS,KAAK;QACd,OAAO;OACR;aACM,OAAO;AACd,UAAI,YAAY,KAAK,GAAG;AACtB,eAAO,EAAE,MAAM,MAAM,MAAK;;AAE5B,YAAM;;EAEV;;;;;EAMA,MAAM,iBAAc;AAClB,UAAM,KAAK;AAEX,WAAO,MAAM,KAAK,aAAa,IAAI,YAAW;AAC5C,aAAO,MAAM,KAAK,gBAAe;IACnC,CAAC;EACH;EAEQ,MAAM,kBAAe;AAC3B,QAAI;AACF,aAAO,MAAM,KAAK,YAAY,OAAO,WAAU;AAC7C,cAAM,EACJ,MAAM,EAAE,QAAO,GACf,OAAO,aAAY,IACjB;AACJ,YAAI;AAAc,gBAAM;AACxB,YAAI,CAAC;AAAS,gBAAM,IAAI,wBAAuB;AAE/C,cAAM,EAAE,MAAK,IAAK,MAAM,SAAS,KAAK,OAAO,OAAO,GAAG,KAAK,GAAG,mBAAmB;UAChF,SAAS,KAAK;UACd,KAAK,QAAQ;SACd;AACD,eAAO,EAAE,MAAM,EAAE,MAAM,MAAM,SAAS,KAAI,GAAI,MAAK;MACrD,CAAC;aACM,OAAO;AACd,UAAI,YAAY,KAAK,GAAG;AACtB,eAAO,EAAE,MAAM,EAAE,MAAM,MAAM,SAAS,KAAI,GAAI,MAAK;;AAErD,YAAM;;EAEV;;;;EAKA,MAAM,OAAO,aAAyB;AACpC,QAAI;AACF,YAAM,WAAW,GAAG,KAAK,GAAG;AAC5B,UAAI,WAAW,aAAa;AAC1B,cAAM,EAAE,OAAO,MAAM,QAAO,IAAK;AACjC,cAAM,EAAE,MAAK,IAAK,MAAM,SAAS,KAAK,OAAO,QAAQ,UAAU;UAC7D,SAAS,KAAK;UACd,MAAM;YACJ;YACA;YACA,sBAAsB,EAAE,eAAe,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,aAAY;;UAE9D,YAAY,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS;SACtB;AACD,eAAO,EAAE,MAAM,EAAE,MAAM,MAAM,SAAS,KAAI,GAAI,MAAK;iBAC1C,WAAW,aAAa;AACjC,cAAM,EAAE,OAAO,MAAM,QAAO,IAAK;AACjC,cAAM,EAAE,MAAM,MAAK,IAAK,MAAM,SAAS,KAAK,OAAO,QAAQ,UAAU;UACnE,SAAS,KAAK;UACd,MAAM;YACJ;YACA;YACA,sBAAsB,EAAE,eAAe,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,aAAY;;SAE/D;AACD,eAAO,EAAE,MAAM,EAAE,MAAM,MAAM,SAAS,MAAM,WAAW,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM,WAAU,GAAI,MAAK;;AAElF,YAAM,IAAI,4BACR,6DAA6D;aAExD,OAAO;AACd,UAAI,YAAY,KAAK,GAAG;AACtB,eAAO,EAAE,MAAM,EAAE,MAAM,MAAM,SAAS,KAAI,GAAI,MAAK;;AAErD,YAAM;;EAEV;;;;;;;;;;;;EAaA,MAAM,aAAU;AACd,UAAM,KAAK;AAEX,UAAM,SAAS,MAAM,KAAK,aAAa,IAAI,YAAW;AACpD,aAAO,KAAK,YAAY,OAAOC,YAAU;AACvC,eAAOA;MACT,CAAC;IACH,CAAC;AAED,WAAO;EACT;;;;EAKQ,MAAM,aAAgB,gBAAwB,IAAoB;AACxE,SAAK,OAAO,iBAAiB,SAAS,cAAc;AAEpD,QAAI;AACF,UAAI,KAAK,cAAc;AACrB,cAAM,OAAO,KAAK,cAAc,SAC5B,KAAK,cAAc,KAAK,cAAc,SAAS,CAAC,IAChD,QAAQ,QAAO;AAEnB,cAAM,UAAU,YAAW;AACzB,gBAAM;AACN,iBAAO,MAAM,GAAE;QACjB,GAAE;AAEF,aAAK,cAAc,MAChB,YAAW;AACV,cAAI;AACF,kBAAM;mBACC,GAAQ;;QAGnB,GAAE,CAAE;AAGN,eAAO;;AAGT,aAAO,MAAM,KAAK,KAAK,QAAQ,KAAK,UAAU,IAAI,gBAAgB,YAAW;AAC3E,aAAK,OAAO,iBAAiB,iCAAiC,KAAK,UAAU;AAE7E,YAAI;AACF,eAAK,eAAe;AAEpB,gBAAM,SAAS,GAAE;AAEjB,eAAK,cAAc,MAChB,YAAW;AACV,gBAAI;AACF,oBAAM;qBACC,GAAQ;;UAGnB,GAAE,CAAE;AAGN,gBAAM;AAGN,iBAAO,KAAK,cAAc,QAAQ;AAChC,kBAAM,SAAS,CAAC,GAAG,KAAK,aAAa;AAErC,kBAAM,QAAQ,IAAI,MAAM;AAExB,iBAAK,cAAc,OAAO,GAAG,OAAO,MAAM;;AAG5C,iBAAO,MAAM;;AAEb,eAAK,OAAO,iBAAiB,iCAAiC,KAAK,UAAU;AAE7E,eAAK,eAAe;;MAExB,CAAC;;AAED,WAAK,OAAO,iBAAiB,KAAK;;EAEtC;;;;;;;EAQQ,MAAM,YACZ,IAoBe;AAEf,SAAK,OAAO,gBAAgB,OAAO;AAEnC,QAAI;AAEF,YAAM,SAAS,MAAM,KAAK,cAAa;AAEvC,aAAO,MAAM,GAAG,MAAM;;AAEtB,WAAK,OAAO,gBAAgB,KAAK;;EAErC;;;;;;EAOQ,MAAM,gBAAa;AAoBzB,SAAK,OAAO,oBAAoB,OAAO;AAEvC,QAAI,CAAC,KAAK,cAAc;AACtB,WAAK,OAAO,oBAAoB,qCAAqC,IAAI,MAAK,EAAG,KAAK;;AAGxF,QAAI;AACF,UAAI,iBAAiC;AAErC,YAAM,eAAe,MAAM,aAAa,KAAK,SAAS,KAAK,UAAU;AAErE,WAAK,OAAO,iBAAiB,wBAAwB,YAAY;AAEjE,UAAI,iBAAiB,MAAM;AACzB,YAAI,KAAK,gBAAgB,YAAY,GAAG;AACtC,2BAAiB;eACZ;AACL,eAAK,OAAO,iBAAiB,mCAAmC;AAChE,gBAAM,KAAK,eAAc;;;AAI7B,UAAI,CAAC,gBAAgB;AACnB,eAAO,EAAE,MAAM,EAAE,SAAS,KAAI,GAAI,OAAO,KAAI;;AAG/C,YAAM,aAAa,eAAe,aAC9B,eAAe,cAAc,KAAK,IAAG,IAAK,MAC1C;AAEJ,WAAK,OACH,oBACA,cAAc,aAAa,KAAK,MAAM,YACtC,cACA,eAAe,UAAU;AAG3B,UAAI,CAAC,YAAY;AACf,YAAI,KAAK,QAAQ,UAAU;AACzB,cAAI,kBAAkB,KAAK;AAC3B,gBAAM,eAAwB,IAAI,MAAM,gBAAgB;YACtD,KAAK,CAAC,QAAa,MAAc,aAAiB;AAChD,kBAAI,CAAC,mBAAmB,SAAS,QAAQ;AAEvC,wBAAQ,KACN,kWAAkW;AAEpW,kCAAkB;AAClB,qBAAK,4BAA4B;;AAEnC,qBAAO,QAAQ,IAAI,QAAQ,MAAM,QAAQ;YAC3C;WACD;AACD,2BAAiB;;AAGnB,eAAO,EAAE,MAAM,EAAE,SAAS,eAAc,GAAI,OAAO,KAAI;;AAGzD,YAAM,EAAE,SAAS,MAAK,IAAK,MAAM,KAAK,kBAAkB,eAAe,aAAa;AACpF,UAAI,OAAO;AACT,eAAO,EAAE,MAAM,EAAE,SAAS,KAAI,GAAI,MAAK;;AAGzC,aAAO,EAAE,MAAM,EAAE,QAAO,GAAI,OAAO,KAAI;;AAEvC,WAAK,OAAO,oBAAoB,KAAK;;EAEzC;;;;;;;;EASA,MAAM,QAAQ,KAAY;AACxB,QAAI,KAAK;AACP,aAAO,MAAM,KAAK,SAAS,GAAG;;AAGhC,UAAM,KAAK;AAEX,UAAM,SAAS,MAAM,KAAK,aAAa,IAAI,YAAW;AACpD,aAAO,MAAM,KAAK,SAAQ;IAC5B,CAAC;AAED,WAAO;EACT;EAEQ,MAAM,SAAS,KAAY;AACjC,QAAI;AACF,UAAI,KAAK;AACP,eAAO,MAAM,SAAS,KAAK,OAAO,OAAO,GAAG,KAAK,GAAG,SAAS;UAC3D,SAAS,KAAK;UACd;UACA,OAAO;SACR;;AAGH,aAAO,MAAM,KAAK,YAAY,OAAO,WAAU;;AAC7C,cAAM,EAAE,MAAM,MAAK,IAAK;AACxB,YAAI,OAAO;AACT,gBAAM;;AAIR,YAAI,GAAC,KAAA,KAAK,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE,iBAAgB,CAAC,KAAK,8BAA8B;AACrE,iBAAO,EAAE,MAAM,EAAE,MAAM,KAAI,GAAI,OAAO,IAAI,wBAAuB,EAAE;;AAGrE,eAAO,MAAM,SAAS,KAAK,OAAO,OAAO,GAAG,KAAK,GAAG,SAAS;UAC3D,SAAS,KAAK;UACd,MAAK,MAAA,KAAA,KAAK,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE,kBAAY,QAAA,OAAA,SAAA,KAAI;UACnC,OAAO;SACR;MACH,CAAC;aACM,OAAO;AACd,UAAI,YAAY,KAAK,GAAG;AACtB,YAAI,0BAA0B,KAAK,GAAG;AAIpC,gBAAM,KAAK,eAAc;AACzB,gBAAM,gBAAgB,KAAK,SAAS,GAAG,KAAK,UAAU,gBAAgB;AACtE,gBAAM,KAAK,sBAAsB,cAAc,IAAI;;AAGrD,eAAO,EAAE,MAAM,EAAE,MAAM,KAAI,GAAI,MAAK;;AAGtC,YAAM;;EAEV;;;;EAKA,MAAM,WACJ,YACA,UAEI,CAAA,GAAE;AAEN,UAAM,KAAK;AAEX,WAAO,MAAM,KAAK,aAAa,IAAI,YAAW;AAC5C,aAAO,MAAM,KAAK,YAAY,YAAY,OAAO;IACnD,CAAC;EACH;EAEU,MAAM,YACd,YACA,UAEI,CAAA,GAAE;AAEN,QAAI;AACF,aAAO,MAAM,KAAK,YAAY,OAAO,WAAU;AAC7C,cAAM,EAAE,MAAM,aAAa,OAAO,aAAY,IAAK;AACnD,YAAI,cAAc;AAChB,gBAAM;;AAER,YAAI,CAAC,YAAY,SAAS;AACxB,gBAAM,IAAI,wBAAuB;;AAEnC,cAAM,UAAmB,YAAY;AACrC,YAAI,gBAA+B;AACnC,YAAI,sBAAqC;AACzC,YAAI,KAAK,aAAa,UAAU,WAAW,SAAS,MAAM;AACxD;AAAC,WAAC,eAAe,mBAAmB,IAAI,MAAM,0BAC5C,KAAK,SACL,KAAK,UAAU;;AAInB,cAAM,EAAE,MAAM,OAAO,UAAS,IAAK,MAAM,SAAS,KAAK,OAAO,OAAO,GAAG,KAAK,GAAG,SAAS;UACvF,SAAS,KAAK;UACd,YAAY,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS;UACrB,MAAI,OAAA,OAAA,OAAA,OAAA,CAAA,GACC,UAAU,GAAA,EACb,gBAAgB,eAChB,uBAAuB,oBAAmB,CAAA;UAE5C,KAAK,QAAQ;UACb,OAAO;SACR;AACD,YAAI;AAAW,gBAAM;AACrB,gBAAQ,OAAO,KAAK;AACpB,cAAM,KAAK,aAAa,OAAO;AAC/B,cAAM,KAAK,sBAAsB,gBAAgB,OAAO;AACxD,eAAO,EAAE,MAAM,EAAE,MAAM,QAAQ,KAAI,GAAI,OAAO,KAAI;MACpD,CAAC;aACM,OAAO;AACd,UAAI,YAAY,KAAK,GAAG;AACtB,eAAO,EAAE,MAAM,EAAE,MAAM,KAAI,GAAI,MAAK;;AAGtC,YAAM;;EAEV;;;;EAKQ,WAAW,KAAW;AAK5B,WAAO,iBAAiB,GAAG;EAC7B;;;;;;EAOA,MAAM,WAAW,gBAGhB;AACC,UAAM,KAAK;AAEX,WAAO,MAAM,KAAK,aAAa,IAAI,YAAW;AAC5C,aAAO,MAAM,KAAK,YAAY,cAAc;IAC9C,CAAC;EACH;EAEU,MAAM,YAAY,gBAG3B;AACC,QAAI;AACF,UAAI,CAAC,eAAe,gBAAgB,CAAC,eAAe,eAAe;AACjE,cAAM,IAAI,wBAAuB;;AAGnC,YAAM,UAAU,KAAK,IAAG,IAAK;AAC7B,UAAIC,aAAY;AAChB,UAAI,aAAa;AACjB,UAAI,UAA0B;AAC9B,YAAM,UAAU,iBAAiB,eAAe,YAAY;AAC5D,UAAI,QAAQ,KAAK;AACf,QAAAA,aAAY,QAAQ;AACpB,qBAAaA,cAAa;;AAG5B,UAAI,YAAY;AACd,cAAM,EAAE,SAAS,kBAAkB,MAAK,IAAK,MAAM,KAAK,kBACtD,eAAe,aAAa;AAE9B,YAAI,OAAO;AACT,iBAAO,EAAE,MAAM,EAAE,MAAM,MAAM,SAAS,KAAI,GAAI,MAAY;;AAG5D,YAAI,CAAC,kBAAkB;AACrB,iBAAO,EAAE,MAAM,EAAE,MAAM,MAAM,SAAS,KAAI,GAAI,OAAO,KAAI;;AAE3D,kBAAU;aACL;AACL,cAAM,EAAE,MAAM,MAAK,IAAK,MAAM,KAAK,SAAS,eAAe,YAAY;AACvE,YAAI,OAAO;AACT,gBAAM;;AAER,kBAAU;UACR,cAAc,eAAe;UAC7B,eAAe,eAAe;UAC9B,MAAM,KAAK;UACX,YAAY;UACZ,YAAYA,aAAY;UACxB,YAAYA;;AAEd,cAAM,KAAK,aAAa,OAAO;AAC/B,cAAM,KAAK,sBAAsB,aAAa,OAAO;;AAGvD,aAAO,EAAE,MAAM,EAAE,MAAM,QAAQ,MAAM,QAAO,GAAI,OAAO,KAAI;aACpD,OAAO;AACd,UAAI,YAAY,KAAK,GAAG;AACtB,eAAO,EAAE,MAAM,EAAE,SAAS,MAAM,MAAM,KAAI,GAAI,MAAK;;AAGrD,YAAM;;EAEV;;;;;;;EAQA,MAAM,eAAe,gBAA0C;AAC7D,UAAM,KAAK;AAEX,WAAO,MAAM,KAAK,aAAa,IAAI,YAAW;AAC5C,aAAO,MAAM,KAAK,gBAAgB,cAAc;IAClD,CAAC;EACH;EAEU,MAAM,gBAAgB,gBAE/B;AACC,QAAI;AACF,aAAO,MAAM,KAAK,YAAY,OAAO,WAAU;;AAC7C,YAAI,CAAC,gBAAgB;AACnB,gBAAM,EAAE,MAAM,OAAAC,OAAK,IAAK;AACxB,cAAIA,QAAO;AACT,kBAAMA;;AAGR,4BAAiB,KAAA,KAAK,aAAO,QAAA,OAAA,SAAA,KAAI;;AAGnC,YAAI,EAAC,mBAAc,QAAd,mBAAc,SAAA,SAAd,eAAgB,gBAAe;AAClC,gBAAM,IAAI,wBAAuB;;AAGnC,cAAM,EAAE,SAAS,MAAK,IAAK,MAAM,KAAK,kBAAkB,eAAe,aAAa;AACpF,YAAI,OAAO;AACT,iBAAO,EAAE,MAAM,EAAE,MAAM,MAAM,SAAS,KAAI,GAAI,MAAY;;AAG5D,YAAI,CAAC,SAAS;AACZ,iBAAO,EAAE,MAAM,EAAE,MAAM,MAAM,SAAS,KAAI,GAAI,OAAO,KAAI;;AAG3D,eAAO,EAAE,MAAM,EAAE,MAAM,QAAQ,MAAM,QAAO,GAAI,OAAO,KAAI;MAC7D,CAAC;aACM,OAAO;AACd,UAAI,YAAY,KAAK,GAAG;AACtB,eAAO,EAAE,MAAM,EAAE,MAAM,MAAM,SAAS,KAAI,GAAI,MAAK;;AAGrD,YAAM;;EAEV;;;;EAKQ,MAAM,mBAAmB,YAAmB;AAOlD,QAAI;AACF,UAAI,CAAC,UAAS;AAAI,cAAM,IAAI,+BAA+B,sBAAsB;AACjF,UAAI,KAAK,aAAa,cAAc,CAAC,KAAK,qBAAoB,GAAI;AAChE,cAAM,IAAI,+BAA+B,sCAAsC;iBACtE,KAAK,YAAY,UAAU,CAAC,YAAY;AACjD,cAAM,IAAI,+BAA+B,4BAA4B;;AAGvE,YAAM,SAAS,uBAAuB,OAAO,SAAS,IAAI;AAE1D,UAAI,YAAY;AACd,YAAI,CAAC,OAAO;AAAM,gBAAM,IAAI,+BAA+B,mBAAmB;AAC9E,cAAM,EAAE,MAAAC,OAAM,OAAAD,OAAK,IAAK,MAAM,KAAK,wBAAwB,OAAO,IAAI;AACtE,YAAIA;AAAO,gBAAMA;AAEjB,cAAM,MAAM,IAAI,IAAI,OAAO,SAAS,IAAI;AACxC,YAAI,aAAa,OAAO,MAAM;AAE9B,eAAO,QAAQ,aAAa,OAAO,QAAQ,OAAO,IAAI,IAAI,SAAQ,CAAE;AAEpE,eAAO,EAAE,MAAM,EAAE,SAASC,MAAK,SAAS,cAAc,KAAI,GAAI,OAAO,KAAI;;AAG3E,UAAI,OAAO,SAAS,OAAO,qBAAqB,OAAO,YAAY;AACjE,cAAM,IAAI,+BACR,OAAO,qBAAqB,mDAC5B;UACE,OAAO,OAAO,SAAS;UACvB,MAAM,OAAO,cAAc;SAC5B;;AAIL,YAAM,EACJ,gBACA,wBACA,cACA,eACA,YACA,YACA,WAAU,IACR;AAEJ,UAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,iBAAiB,CAAC,YAAY;AACjE,cAAM,IAAI,+BAA+B,2BAA2B;;AAGtE,YAAM,UAAU,KAAK,MAAM,KAAK,IAAG,IAAK,GAAI;AAC5C,YAAM,YAAY,SAAS,UAAU;AACrC,UAAIF,aAAY,UAAU;AAE1B,UAAI,YAAY;AACd,QAAAA,aAAY,SAAS,UAAU;;AAGjC,YAAM,oBAAoBA,aAAY;AACtC,UAAI,oBAAoB,OAAQ,4BAA4B;AAC1D,gBAAQ,KACN,iEAAiE,iBAAiB,iCAAiC,SAAS,GAAG;;AAInI,YAAM,WAAWA,aAAY;AAC7B,UAAI,UAAU,YAAY,KAAK;AAC7B,gBAAQ,KACN,mGACA,UACAA,YACA,OAAO;iBAEA,UAAU,WAAW,GAAG;AACjC,gBAAQ,KACN,gHACA,UACAA,YACA,OAAO;;AAIX,YAAM,EAAE,MAAM,MAAK,IAAK,MAAM,KAAK,SAAS,YAAY;AACxD,UAAI;AAAO,cAAM;AAEjB,YAAM,UAAmB;QACvB;QACA;QACA;QACA,YAAY;QACZ,YAAYA;QACZ;QACA;QACA,MAAM,KAAK;;AAIb,aAAO,SAAS,OAAO;AACvB,WAAK,OAAO,yBAAyB,+BAA+B;AAEpE,aAAO,EAAE,MAAM,EAAE,SAAS,cAAc,OAAO,KAAI,GAAI,OAAO,KAAI;aAC3D,OAAO;AACd,UAAI,YAAY,KAAK,GAAG;AACtB,eAAO,EAAE,MAAM,EAAE,SAAS,MAAM,cAAc,KAAI,GAAI,MAAK;;AAG7D,YAAM;;EAEV;;;;EAKQ,uBAAoB;AAC1B,UAAM,SAAS,uBAAuB,OAAO,SAAS,IAAI;AAE1D,WAAO,CAAC,EAAE,UAAS,MAAO,OAAO,gBAAgB,OAAO;EAC1D;;;;EAKQ,MAAM,cAAW;AACvB,UAAM,SAAS,uBAAuB,OAAO,SAAS,IAAI;AAE1D,UAAM,wBAAwB,MAAM,aAClC,KAAK,SACL,GAAG,KAAK,UAAU,gBAAgB;AAGpC,WAAO,CAAC,EAAE,OAAO,QAAQ;EAC3B;;;;;;;;;EAUA,MAAM,QAAQ,UAAmB,EAAE,OAAO,SAAQ,GAAE;AAClD,UAAM,KAAK;AAEX,WAAO,MAAM,KAAK,aAAa,IAAI,YAAW;AAC5C,aAAO,MAAM,KAAK,SAAS,OAAO;IACpC,CAAC;EACH;EAEU,MAAM,SACd,EAAE,MAAK,IAAc,EAAE,OAAO,SAAQ,GAAE;AAExC,WAAO,MAAM,KAAK,YAAY,OAAO,WAAU;;AAC7C,YAAM,EAAE,MAAM,OAAO,aAAY,IAAK;AACtC,UAAI,cAAc;AAChB,eAAO,EAAE,OAAO,aAAY;;AAE9B,YAAM,eAAc,KAAA,KAAK,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE;AAClC,UAAI,aAAa;AACf,cAAM,EAAE,MAAK,IAAK,MAAM,KAAK,MAAM,QAAQ,aAAa,KAAK;AAC7D,YAAI,OAAO;AAGT,cACE,EACE,eAAe,KAAK,MACnB,MAAM,WAAW,OAAO,MAAM,WAAW,OAAO,MAAM,WAAW,OAEpE;AACA,mBAAO,EAAE,MAAK;;;;AAIpB,UAAI,UAAU,UAAU;AACtB,cAAM,KAAK,eAAc;AACzB,cAAM,gBAAgB,KAAK,SAAS,GAAG,KAAK,UAAU,gBAAgB;AACtE,cAAM,KAAK,sBAAsB,cAAc,IAAI;;AAErD,aAAO,EAAE,OAAO,KAAI;IACtB,CAAC;EACH;;;;;EAMA,kBACE,UAAmF;AAInF,UAAM,KAAa,KAAI;AACvB,UAAM,eAA6B;MACjC;MACA;MACA,aAAa,MAAK;AAChB,aAAK,OAAO,kBAAkB,yCAAyC,EAAE;AAEzE,aAAK,oBAAoB,OAAO,EAAE;MACpC;;AAGF,SAAK,OAAO,wBAAwB,+BAA+B,EAAE;AAErE,SAAK,oBAAoB,IAAI,IAAI,YAAY;AAC5C,KAAC,YAAW;AACX,YAAM,KAAK;AAEX,YAAM,KAAK,aAAa,IAAI,YAAW;AACrC,aAAK,oBAAoB,EAAE;MAC7B,CAAC;IACH,GAAE;AAEF,WAAO,EAAE,MAAM,EAAE,aAAY,EAAE;EACjC;EAEQ,MAAM,oBAAoB,IAAU;AAC1C,WAAO,MAAM,KAAK,YAAY,OAAO,WAAU;;AAC7C,UAAI;AACF,cAAM,EACJ,MAAM,EAAE,QAAO,GACf,MAAK,IACH;AACJ,YAAI;AAAO,gBAAM;AAEjB,gBAAM,KAAA,KAAK,oBAAoB,IAAI,EAAE,OAAC,QAAA,OAAA,SAAA,SAAA,GAAE,SAAS,mBAAmB,OAAO;AAC3E,aAAK,OAAO,mBAAmB,eAAe,IAAI,WAAW,OAAO;eAC7D,KAAK;AACZ,gBAAM,KAAA,KAAK,oBAAoB,IAAI,EAAE,OAAC,QAAA,OAAA,SAAA,SAAA,GAAE,SAAS,mBAAmB,IAAI;AACxE,aAAK,OAAO,mBAAmB,eAAe,IAAI,SAAS,GAAG;AAC9D,gBAAQ,MAAM,GAAG;;IAErB,CAAC;EACH;;;;;;;;EASA,MAAM,sBACJ,OACA,UAGI,CAAA,GAAE;AAQN,QAAI,gBAA+B;AACnC,QAAI,sBAAqC;AAEzC,QAAI,KAAK,aAAa,QAAQ;AAC5B;AAAC,OAAC,eAAe,mBAAmB,IAAI,MAAM;QAC5C,KAAK;QACL,KAAK;QACL;;;;AAGJ,QAAI;AACF,aAAO,MAAM,SAAS,KAAK,OAAO,QAAQ,GAAG,KAAK,GAAG,YAAY;QAC/D,MAAM;UACJ;UACA,gBAAgB;UAChB,uBAAuB;UACvB,sBAAsB,EAAE,eAAe,QAAQ,aAAY;;QAE7D,SAAS,KAAK;QACd,YAAY,QAAQ;OACrB;aACM,OAAO;AACd,UAAI,YAAY,KAAK,GAAG;AACtB,eAAO,EAAE,MAAM,MAAM,MAAK;;AAG5B,YAAM;;EAEV;;;;EAKA,MAAM,oBAAiB;;AASrB,QAAI;AACF,YAAM,EAAE,MAAM,MAAK,IAAK,MAAM,KAAK,QAAO;AAC1C,UAAI;AAAO,cAAM;AACjB,aAAO,EAAE,MAAM,EAAE,aAAY,KAAA,KAAK,KAAK,gBAAU,QAAA,OAAA,SAAA,KAAI,CAAA,EAAE,GAAI,OAAO,KAAI;aAC/D,OAAO;AACd,UAAI,YAAY,KAAK,GAAG;AACtB,eAAO,EAAE,MAAM,MAAM,MAAK;;AAE5B,YAAM;;EAEV;;;;;EAKA,MAAM,aAAa,aAAuC;;AACxD,QAAI;AACF,YAAM,EAAE,MAAM,MAAK,IAAK,MAAM,KAAK,YAAY,OAAO,WAAU;;AAC9D,cAAM,EAAE,MAAAE,OAAM,OAAAD,OAAK,IAAK;AACxB,YAAIA;AAAO,gBAAMA;AACjB,cAAM,MAAc,MAAM,KAAK,mBAC7B,GAAG,KAAK,GAAG,8BACX,YAAY,UACZ;UACE,aAAYE,MAAA,YAAY,aAAO,QAAAA,QAAA,SAAA,SAAAA,IAAE;UACjC,SAAQ,KAAA,YAAY,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE;UAC7B,cAAa,KAAA,YAAY,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE;UAClC,qBAAqB;SACtB;AAEH,eAAO,MAAM,SAAS,KAAK,OAAO,OAAO,KAAK;UAC5C,SAAS,KAAK;UACd,MAAK,MAAA,KAAAD,MAAK,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE,kBAAY,QAAA,OAAA,SAAA,KAAI;SACpC;MACH,CAAC;AACD,UAAI;AAAO,cAAM;AACjB,UAAI,UAAS,KAAM,GAAC,KAAA,YAAY,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE,sBAAqB;AAC5D,eAAO,SAAS,OAAO,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM,GAAG;;AAElC,aAAO,EAAE,MAAM,EAAE,UAAU,YAAY,UAAU,KAAK,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM,IAAG,GAAI,OAAO,KAAI;aACvE,OAAO;AACd,UAAI,YAAY,KAAK,GAAG;AACtB,eAAO,EAAE,MAAM,EAAE,UAAU,YAAY,UAAU,KAAK,KAAI,GAAI,MAAK;;AAErE,YAAM;;EAEV;;;;EAKA,MAAM,eAAe,UAAsB;AAOzC,QAAI;AACF,aAAO,MAAM,KAAK,YAAY,OAAO,WAAU;;AAC7C,cAAM,EAAE,MAAM,MAAK,IAAK;AACxB,YAAI,OAAO;AACT,gBAAM;;AAER,eAAO,MAAM,SACX,KAAK,OACL,UACA,GAAG,KAAK,GAAG,oBAAoB,SAAS,WAAW,IACnD;UACE,SAAS,KAAK;UACd,MAAK,MAAA,KAAA,KAAK,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE,kBAAY,QAAA,OAAA,SAAA,KAAI;SACpC;MAEL,CAAC;aACM,OAAO;AACd,UAAI,YAAY,KAAK,GAAG;AACtB,eAAO,EAAE,MAAM,MAAM,MAAK;;AAE5B,YAAM;;EAEV;;;;;EAMQ,MAAM,oBAAoB,cAAoB;AACpD,UAAM,YAAY,wBAAwB,aAAa,UAAU,GAAG,CAAC,CAAC;AACtE,SAAK,OAAO,WAAW,OAAO;AAE9B,QAAI;AACF,YAAM,YAAY,KAAK,IAAG;AAG1B,aAAO,MAAM,UACX,OAAO,YAAW;AAChB,YAAI,UAAU,GAAG;AACf,gBAAM,MAAM,MAAM,KAAK,IAAI,GAAG,UAAU,CAAC,CAAC;;AAG5C,aAAK,OAAO,WAAW,sBAAsB,OAAO;AAEpD,eAAO,MAAM,SAAS,KAAK,OAAO,QAAQ,GAAG,KAAK,GAAG,mCAAmC;UACtF,MAAM,EAAE,eAAe,aAAY;UACnC,SAAS,KAAK;UACd,OAAO;SACR;MACH,GACA,CAAC,SAAS,UAAS;AACjB,cAAM,sBAAsB,MAAM,KAAK,IAAI,GAAG,OAAO;AACrD,eACE,SACA,0BAA0B,KAAK;QAE/B,KAAK,IAAG,IAAK,sBAAsB,YAAY;MAEnD,CAAC;aAEI,OAAO;AACd,WAAK,OAAO,WAAW,SAAS,KAAK;AAErC,UAAI,YAAY,KAAK,GAAG;AACtB,eAAO,EAAE,MAAM,EAAE,SAAS,MAAM,MAAM,KAAI,GAAI,MAAK;;AAErD,YAAM;;AAEN,WAAK,OAAO,WAAW,KAAK;;EAEhC;EAEQ,gBAAgB,cAAqB;AAC3C,UAAM,iBACJ,OAAO,iBAAiB,YACxB,iBAAiB,QACjB,kBAAkB,gBAClB,mBAAmB,gBACnB,gBAAgB;AAElB,WAAO;EACT;EAEQ,MAAM,sBACZ,UACA,SAKC;AAED,UAAM,MAAc,MAAM,KAAK,mBAAmB,GAAG,KAAK,GAAG,cAAc,UAAU;MACnF,YAAY,QAAQ;MACpB,QAAQ,QAAQ;MAChB,aAAa,QAAQ;KACtB;AAED,SAAK,OAAO,4BAA4B,YAAY,UAAU,WAAW,SAAS,OAAO,GAAG;AAG5F,QAAI,UAAS,KAAM,CAAC,QAAQ,qBAAqB;AAC/C,aAAO,SAAS,OAAO,GAAG;;AAG5B,WAAO,EAAE,MAAM,EAAE,UAAU,IAAG,GAAI,OAAO,KAAI;EAC/C;;;;;EAMQ,MAAM,qBAAkB;;AAC9B,UAAM,YAAY;AAClB,SAAK,OAAO,WAAW,OAAO;AAE9B,QAAI;AACF,YAAM,iBAAiB,MAAM,aAAa,KAAK,SAAS,KAAK,UAAU;AACvE,WAAK,OAAO,WAAW,wBAAwB,cAAc;AAE7D,UAAI,CAAC,KAAK,gBAAgB,cAAc,GAAG;AACzC,aAAK,OAAO,WAAW,sBAAsB;AAC7C,YAAI,mBAAmB,MAAM;AAC3B,gBAAM,KAAK,eAAc;;AAG3B;;AAGF,YAAM,UAAU,KAAK,MAAM,KAAK,IAAG,IAAK,GAAI;AAC5C,YAAM,sBAAqB,KAAA,eAAe,gBAAU,QAAA,OAAA,SAAA,KAAI,YAAY,UAAU;AAE9E,WAAK,OACH,WACA,cAAc,oBAAoB,KAAK,MAAM,2BAA2B,aAAa,GAAG;AAG1F,UAAI,mBAAmB;AACrB,YAAI,KAAK,oBAAoB,eAAe,eAAe;AACzD,gBAAM,EAAE,MAAK,IAAK,MAAM,KAAK,kBAAkB,eAAe,aAAa;AAE3E,cAAI,OAAO;AACT,oBAAQ,MAAM,KAAK;AAEnB,gBAAI,CAAC,0BAA0B,KAAK,GAAG;AACrC,mBAAK,OACH,WACA,mEACA,KAAK;AAEP,oBAAM,KAAK,eAAc;;;;aAI1B;AAIL,cAAM,KAAK,sBAAsB,aAAa,cAAc;;aAEvD,KAAK;AACZ,WAAK,OAAO,WAAW,SAAS,GAAG;AAEnC,cAAQ,MAAM,GAAG;AACjB;;AAEA,WAAK,OAAO,WAAW,KAAK;;EAEhC;EAEQ,MAAM,kBAAkB,cAAoB;;AAClD,QAAI,CAAC,cAAc;AACjB,YAAM,IAAI,wBAAuB;;AAInC,QAAI,KAAK,oBAAoB;AAC3B,aAAO,KAAK,mBAAmB;;AAGjC,UAAM,YAAY,sBAAsB,aAAa,UAAU,GAAG,CAAC,CAAC;AAEpE,SAAK,OAAO,WAAW,OAAO;AAE9B,QAAI;AACF,WAAK,qBAAqB,IAAI,SAAQ;AAEtC,YAAM,EAAE,MAAM,MAAK,IAAK,MAAM,KAAK,oBAAoB,YAAY;AACnE,UAAI;AAAO,cAAM;AACjB,UAAI,CAAC,KAAK;AAAS,cAAM,IAAI,wBAAuB;AAEpD,YAAM,KAAK,aAAa,KAAK,OAAO;AACpC,YAAM,KAAK,sBAAsB,mBAAmB,KAAK,OAAO;AAEhE,YAAM,SAAS,EAAE,SAAS,KAAK,SAAS,OAAO,KAAI;AAEnD,WAAK,mBAAmB,QAAQ,MAAM;AAEtC,aAAO;aACA,OAAO;AACd,WAAK,OAAO,WAAW,SAAS,KAAK;AAErC,UAAI,YAAY,KAAK,GAAG;AACtB,cAAM,SAAS,EAAE,SAAS,MAAM,MAAK;AAErC,YAAI,CAAC,0BAA0B,KAAK,GAAG;AACrC,gBAAM,KAAK,eAAc;AACzB,gBAAM,KAAK,sBAAsB,cAAc,IAAI;;AAGrD,SAAA,KAAA,KAAK,wBAAkB,QAAA,OAAA,SAAA,SAAA,GAAE,QAAQ,MAAM;AAEvC,eAAO;;AAGT,OAAA,KAAA,KAAK,wBAAkB,QAAA,OAAA,SAAA,SAAA,GAAE,OAAO,KAAK;AACrC,YAAM;;AAEN,WAAK,qBAAqB;AAC1B,WAAK,OAAO,WAAW,KAAK;;EAEhC;EAEQ,MAAM,sBACZ,OACA,SACA,YAAY,MAAI;AAEhB,UAAM,YAAY,0BAA0B,KAAK;AACjD,SAAK,OAAO,WAAW,SAAS,SAAS,eAAe,SAAS,EAAE;AAEnE,QAAI;AACF,UAAI,KAAK,oBAAoB,WAAW;AACtC,aAAK,iBAAiB,YAAY,EAAE,OAAO,QAAO,CAAE;;AAGtD,YAAM,SAAgB,CAAA;AACtB,YAAM,WAAW,MAAM,KAAK,KAAK,oBAAoB,OAAM,CAAE,EAAE,IAAI,OAAO,MAAK;AAC7E,YAAI;AACF,gBAAM,EAAE,SAAS,OAAO,OAAO;iBACxB,GAAQ;AACf,iBAAO,KAAK,CAAC;;MAEjB,CAAC;AAED,YAAM,QAAQ,IAAI,QAAQ;AAE1B,UAAI,OAAO,SAAS,GAAG;AACrB,iBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK,GAAG;AACzC,kBAAQ,MAAM,OAAO,CAAC,CAAC;;AAGzB,cAAM,OAAO,CAAC;;;AAGhB,WAAK,OAAO,WAAW,KAAK;;EAEhC;;;;;EAMQ,MAAM,aAAa,SAAgB;AACzC,SAAK,OAAO,mBAAmB,OAAO;AAGtC,SAAK,4BAA4B;AACjC,UAAM,aAAa,KAAK,SAAS,KAAK,YAAY,OAAO;EAC3D;EAEQ,MAAM,iBAAc;AAC1B,SAAK,OAAO,mBAAmB;AAE/B,UAAM,gBAAgB,KAAK,SAAS,KAAK,UAAU;EACrD;;;;;;;EAQQ,mCAAgC;AACtC,SAAK,OAAO,qCAAqC;AAEjD,UAAM,WAAW,KAAK;AACtB,SAAK,4BAA4B;AAEjC,QAAI;AACF,UAAI,YAAY,UAAS,MAAM,WAAM,QAAN,WAAM,SAAA,SAAN,OAAQ,sBAAqB;AAC1D,eAAO,oBAAoB,oBAAoB,QAAQ;;aAElD,GAAG;AACV,cAAQ,MAAM,6CAA6C,CAAC;;EAEhE;;;;;EAMQ,MAAM,oBAAiB;AAC7B,UAAM,KAAK,iBAAgB;AAE3B,SAAK,OAAO,sBAAsB;AAElC,UAAM,SAAS,YAAY,MAAM,KAAK,sBAAqB,GAAI,0BAA0B;AACzF,SAAK,oBAAoB;AAEzB,QAAI,UAAU,OAAO,WAAW,YAAY,OAAO,OAAO,UAAU,YAAY;AAO9E,aAAO,MAAK;eAEH,OAAO,SAAS,eAAe,OAAO,KAAK,eAAe,YAAY;AAI/E,WAAK,WAAW,MAAM;;AAMxB,eAAW,YAAW;AACpB,YAAM,KAAK;AACX,YAAM,KAAK,sBAAqB;IAClC,GAAG,CAAC;EACN;;;;;EAMQ,MAAM,mBAAgB;AAC5B,SAAK,OAAO,qBAAqB;AAEjC,UAAM,SAAS,KAAK;AACpB,SAAK,oBAAoB;AAEzB,QAAI,QAAQ;AACV,oBAAc,MAAM;;EAExB;;;;;;;;;;;;;;;;;;;;;;;EAwBA,MAAM,mBAAgB;AACpB,SAAK,iCAAgC;AACrC,UAAM,KAAK,kBAAiB;EAC9B;;;;;;;;;EAUA,MAAM,kBAAe;AACnB,SAAK,iCAAgC;AACrC,UAAM,KAAK,iBAAgB;EAC7B;;;;EAKQ,MAAM,wBAAqB;AACjC,SAAK,OAAO,4BAA4B,OAAO;AAE/C,QAAI;AACF,YAAM,KAAK,aAAa,GAAG,YAAW;AACpC,YAAI;AACF,gBAAM,MAAM,KAAK,IAAG;AAEpB,cAAI;AACF,mBAAO,MAAM,KAAK,YAAY,OAAO,WAAU;AAC7C,oBAAM,EACJ,MAAM,EAAE,QAAO,EAAE,IACf;AAEJ,kBAAI,CAAC,WAAW,CAAC,QAAQ,iBAAiB,CAAC,QAAQ,YAAY;AAC7D,qBAAK,OAAO,4BAA4B,YAAY;AACpD;;AAIF,oBAAM,iBAAiB,KAAK,OACzB,QAAQ,aAAa,MAAO,OAAO,0BAA0B;AAGhE,mBAAK,OACH,4BACA,2BAA2B,cAAc,wBAAwB,0BAA0B,4BAA4B,2BAA2B,QAAQ;AAG5J,kBAAI,kBAAkB,6BAA6B;AACjD,sBAAM,KAAK,kBAAkB,QAAQ,aAAa;;YAEtD,CAAC;mBACM,GAAQ;AACf,oBAAQ,MACN,0EACA,CAAC;;;AAIL,eAAK,OAAO,4BAA4B,KAAK;;MAEjD,CAAC;aACM,GAAQ;AACf,UAAI,EAAE,oBAAoB,aAAa,yBAAyB;AAC9D,aAAK,OAAO,4CAA4C;aACnD;AACL,cAAM;;;EAGZ;;;;;;EAOQ,MAAM,0BAAuB;AACnC,SAAK,OAAO,4BAA4B;AAExC,QAAI,CAAC,UAAS,KAAM,EAAC,WAAM,QAAN,WAAM,SAAA,SAAN,OAAQ,mBAAkB;AAC7C,UAAI,KAAK,kBAAkB;AAEzB,aAAK,iBAAgB;;AAGvB,aAAO;;AAGT,QAAI;AACF,WAAK,4BAA4B,YAAY,MAAM,KAAK,qBAAqB,KAAK;AAElF,iBAAM,QAAN,WAAM,SAAA,SAAN,OAAQ,iBAAiB,oBAAoB,KAAK,yBAAyB;AAI3E,YAAM,KAAK,qBAAqB,IAAI;aAC7B,OAAO;AACd,cAAQ,MAAM,2BAA2B,KAAK;;EAElD;;;;EAKQ,MAAM,qBAAqB,sBAA6B;AAC9D,UAAM,aAAa,yBAAyB,oBAAoB;AAChE,SAAK,OAAO,YAAY,mBAAmB,SAAS,eAAe;AAEnE,QAAI,SAAS,oBAAoB,WAAW;AAC1C,UAAI,KAAK,kBAAkB;AAGzB,aAAK,kBAAiB;;AAGxB,UAAI,CAAC,sBAAsB;AAKzB,cAAM,KAAK;AAEX,cAAM,KAAK,aAAa,IAAI,YAAW;AACrC,cAAI,SAAS,oBAAoB,WAAW;AAC1C,iBAAK,OACH,YACA,0GAA0G;AAI5G;;AAIF,gBAAM,KAAK,mBAAkB;QAC/B,CAAC;;eAEM,SAAS,oBAAoB,UAAU;AAChD,UAAI,KAAK,kBAAkB;AACzB,aAAK,iBAAgB;;;EAG3B;;;;;;;EAQQ,MAAM,mBACZ,KACA,UACA,SAKC;AAED,UAAM,YAAsB,CAAC,YAAY,mBAAmB,QAAQ,CAAC,EAAE;AACvE,QAAI,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,YAAY;AACvB,gBAAU,KAAK,eAAe,mBAAmB,QAAQ,UAAU,CAAC,EAAE;;AAExE,QAAI,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,QAAQ;AACnB,gBAAU,KAAK,UAAU,mBAAmB,QAAQ,MAAM,CAAC,EAAE;;AAE/D,QAAI,KAAK,aAAa,QAAQ;AAC5B,YAAM,CAAC,eAAe,mBAAmB,IAAI,MAAM,0BACjD,KAAK,SACL,KAAK,UAAU;AAGjB,YAAM,aAAa,IAAI,gBAAgB;QACrC,gBAAgB,GAAG,mBAAmB,aAAa,CAAC;QACpD,uBAAuB,GAAG,mBAAmB,mBAAmB,CAAC;OAClE;AACD,gBAAU,KAAK,WAAW,SAAQ,CAAE;;AAEtC,QAAI,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,aAAa;AACxB,YAAM,QAAQ,IAAI,gBAAgB,QAAQ,WAAW;AACrD,gBAAU,KAAK,MAAM,SAAQ,CAAE;;AAEjC,QAAI,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,qBAAqB;AAChC,gBAAU,KAAK,sBAAsB,QAAQ,mBAAmB,EAAE;;AAGpE,WAAO,GAAG,GAAG,IAAI,UAAU,KAAK,GAAG,CAAC;EACtC;EAEQ,MAAM,UAAU,QAAyB;AAC/C,QAAI;AACF,aAAO,MAAM,KAAK,YAAY,OAAO,WAAU;;AAC7C,cAAM,EAAE,MAAM,aAAa,OAAO,aAAY,IAAK;AACnD,YAAI,cAAc;AAChB,iBAAO,EAAE,MAAM,MAAM,OAAO,aAAY;;AAG1C,eAAO,MAAM,SAAS,KAAK,OAAO,UAAU,GAAG,KAAK,GAAG,YAAY,OAAO,QAAQ,IAAI;UACpF,SAAS,KAAK;UACd,MAAK,KAAA,gBAAW,QAAX,gBAAW,SAAA,SAAX,YAAa,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE;SAC5B;MACH,CAAC;aACM,OAAO;AACd,UAAI,YAAY,KAAK,GAAG;AACtB,eAAO,EAAE,MAAM,MAAM,MAAK;;AAE5B,YAAM;;EAEV;;;;EAKQ,MAAM,QAAQ,QAAuB;AAC3C,QAAI;AACF,aAAO,MAAM,KAAK,YAAY,OAAO,WAAU;;AAC7C,cAAM,EAAE,MAAM,aAAa,OAAO,aAAY,IAAK;AACnD,YAAI,cAAc;AAChB,iBAAO,EAAE,MAAM,MAAM,OAAO,aAAY;;AAG1C,cAAM,OAAI,OAAA,OAAA,EACR,eAAe,OAAO,cACtB,aAAa,OAAO,WAAU,GAC1B,OAAO,eAAe,UAAU,EAAE,OAAO,OAAO,MAAK,IAAK,EAAE,QAAQ,OAAO,OAAM,CAAG;AAG1F,cAAM,EAAE,MAAM,MAAK,IAAK,MAAM,SAAS,KAAK,OAAO,QAAQ,GAAG,KAAK,GAAG,YAAY;UAChF;UACA,SAAS,KAAK;UACd,MAAK,KAAA,gBAAW,QAAX,gBAAW,SAAA,SAAX,YAAa,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE;SAC5B;AAED,YAAI,OAAO;AACT,iBAAO,EAAE,MAAM,MAAM,MAAK;;AAI5B,YAAI,OAAO,eAAe,SAAS;AACjC,iBAAO,KAAK;;AAGd,YAAI,OAAO,eAAe,YAAU,KAAA,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM,UAAI,QAAA,OAAA,SAAA,SAAA,GAAE,UAAS;AACvD,eAAK,KAAK,UAAU,4BAA4B,KAAK,KAAK,OAAO;;AAGnE,eAAO,EAAE,MAAM,OAAO,KAAI;MAC5B,CAAC;aACM,OAAO;AACd,UAAI,YAAY,KAAK,GAAG;AACtB,eAAO,EAAE,MAAM,MAAM,MAAK;;AAE5B,YAAM;;EAEV;;;;EAKQ,MAAM,QAAQ,QAAuB;AAC3C,WAAO,KAAK,aAAa,IAAI,YAAW;AACtC,UAAI;AACF,eAAO,MAAM,KAAK,YAAY,OAAO,WAAU;;AAC7C,gBAAM,EAAE,MAAM,aAAa,OAAO,aAAY,IAAK;AACnD,cAAI,cAAc;AAChB,mBAAO,EAAE,MAAM,MAAM,OAAO,aAAY;;AAG1C,gBAAM,EAAE,MAAM,MAAK,IAAK,MAAM,SAC5B,KAAK,OACL,QACA,GAAG,KAAK,GAAG,YAAY,OAAO,QAAQ,WACtC;YACE,MAAM,EAAE,MAAM,OAAO,MAAM,cAAc,OAAO,YAAW;YAC3D,SAAS,KAAK;YACd,MAAK,KAAA,gBAAW,QAAX,gBAAW,SAAA,SAAX,YAAa,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE;WAC5B;AAEH,cAAI,OAAO;AACT,mBAAO,EAAE,MAAM,MAAM,MAAK;;AAG5B,gBAAM,KAAK,aAAY,OAAA,OAAA,EACrB,YAAY,KAAK,MAAM,KAAK,IAAG,IAAK,GAAI,IAAI,KAAK,WAAU,GACxD,IAAI,CAAA;AAET,gBAAM,KAAK,sBAAsB,0BAA0B,IAAI;AAE/D,iBAAO,EAAE,MAAM,MAAK;QACtB,CAAC;eACM,OAAO;AACd,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO,EAAE,MAAM,MAAM,MAAK;;AAE5B,cAAM;;IAEV,CAAC;EACH;;;;EAKQ,MAAM,WAAW,QAA0B;AACjD,WAAO,KAAK,aAAa,IAAI,YAAW;AACtC,UAAI;AACF,eAAO,MAAM,KAAK,YAAY,OAAO,WAAU;;AAC7C,gBAAM,EAAE,MAAM,aAAa,OAAO,aAAY,IAAK;AACnD,cAAI,cAAc;AAChB,mBAAO,EAAE,MAAM,MAAM,OAAO,aAAY;;AAG1C,iBAAO,MAAM,SACX,KAAK,OACL,QACA,GAAG,KAAK,GAAG,YAAY,OAAO,QAAQ,cACtC;YACE,MAAM,EAAE,SAAS,OAAO,QAAO;YAC/B,SAAS,KAAK;YACd,MAAK,KAAA,gBAAW,QAAX,gBAAW,SAAA,SAAX,YAAa,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE;WAC5B;QAEL,CAAC;eACM,OAAO;AACd,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO,EAAE,MAAM,MAAM,MAAK;;AAE5B,cAAM;;IAEV,CAAC;EACH;;;;EAKQ,MAAM,oBACZ,QAAmC;AAKnC,UAAM,EAAE,MAAM,eAAe,OAAO,eAAc,IAAK,MAAM,KAAK,WAAW;MAC3E,UAAU,OAAO;KAClB;AACD,QAAI,gBAAgB;AAClB,aAAO,EAAE,MAAM,MAAM,OAAO,eAAc;;AAG5C,WAAO,MAAM,KAAK,QAAQ;MACxB,UAAU,OAAO;MACjB,aAAa,cAAc;MAC3B,MAAM,OAAO;KACd;EACH;;;;EAKQ,MAAM,eAAY;AAExB,UAAM,EACJ,MAAM,EAAE,KAAI,GACZ,OAAO,UAAS,IACd,MAAM,KAAK,QAAO;AACtB,QAAI,WAAW;AACb,aAAO,EAAE,MAAM,MAAM,OAAO,UAAS;;AAGvC,UAAM,WAAU,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM,YAAW,CAAA;AACjC,UAAM,OAAO,QAAQ,OACnB,CAAC,WAAW,OAAO,gBAAgB,UAAU,OAAO,WAAW,UAAU;AAE3E,UAAM,QAAQ,QAAQ,OACpB,CAAC,WAAW,OAAO,gBAAgB,WAAW,OAAO,WAAW,UAAU;AAG5E,WAAO;MACL,MAAM;QACJ,KAAK;QACL;QACA;;MAEF,OAAO;;EAEX;;;;EAKQ,MAAM,kCAA+B;AAC3C,WAAO,KAAK,aAAa,IAAI,YAAW;AACtC,aAAO,MAAM,KAAK,YAAY,OAAO,WAAU;;AAC7C,cAAM,EACJ,MAAM,EAAE,QAAO,GACf,OAAO,aAAY,IACjB;AACJ,YAAI,cAAc;AAChB,iBAAO,EAAE,MAAM,MAAM,OAAO,aAAY;;AAE1C,YAAI,CAAC,SAAS;AACZ,iBAAO;YACL,MAAM,EAAE,cAAc,MAAM,WAAW,MAAM,8BAA8B,CAAA,EAAE;YAC7E,OAAO;;;AAIX,cAAM,UAAU,KAAK,WAAW,QAAQ,YAAY;AAEpD,YAAI,eAAoD;AAExD,YAAI,QAAQ,KAAK;AACf,yBAAe,QAAQ;;AAGzB,YAAI,YAAiD;AAErD,cAAM,mBACJ,MAAA,KAAA,QAAQ,KAAK,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE,OAAO,CAAC,WAAmB,OAAO,WAAW,UAAU,OAAC,QAAA,OAAA,SAAA,KAAI,CAAA;AAEpF,YAAI,gBAAgB,SAAS,GAAG;AAC9B,sBAAY;;AAGd,cAAM,+BAA+B,QAAQ,OAAO,CAAA;AAEpD,eAAO,EAAE,MAAM,EAAE,cAAc,WAAW,6BAA4B,GAAI,OAAO,KAAI;MACvF,CAAC;IACH,CAAC;EACH;;AAn5Ee,aAAA,iBAAiB;;;ACpHlC,IAAM,eAAe;AAErB,IAAA,uBAAe;;;ACFf,IAAM,aAAa;AAEnB,IAAA,qBAAe;;;ACDT,IAAO,qBAAP,cAAkC,mBAAU;EAChD,YAAY,SAAkC;AAC5C,UAAM,OAAO;EACf;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACwBF,IAAqB,iBAArB,MAAmC;;;;;;;;;;;;;EAuCjC,YACY,aACA,aACV,SAA2C;;AAFjC,SAAA,cAAA;AACA,SAAA,cAAA;AAGV,QAAI,CAAC;AAAa,YAAM,IAAI,MAAM,0BAA0B;AAC5D,QAAI,CAAC;AAAa,YAAM,IAAI,MAAM,0BAA0B;AAE5D,UAAM,eAAe,mBAAmB,WAAW;AAEnD,SAAK,cAAc,GAAG,YAAY,eAAe,QAAQ,UAAU,IAAI;AACvE,SAAK,UAAU,GAAG,YAAY;AAC9B,SAAK,aAAa,GAAG,YAAY;AACjC,SAAK,eAAe,GAAG,YAAY;AAGnC,UAAM,oBAAoB,MAAM,IAAI,IAAI,KAAK,OAAO,EAAE,SAAS,MAAM,GAAG,EAAE,CAAC,CAAC;AAC5E,UAAM,WAAW;MACf,IAAI;MACJ,UAAU;MACV,MAAI,OAAA,OAAA,OAAA,OAAA,CAAA,GAAO,oBAAoB,GAAA,EAAE,YAAY,kBAAiB,CAAA;MAC9D,QAAQ;;AAGV,UAAM,WAAW,qBAAqB,YAAO,QAAP,YAAO,SAAP,UAAW,CAAA,GAAI,QAAQ;AAE7D,SAAK,cAAa,KAAA,SAAS,KAAK,gBAAU,QAAA,OAAA,SAAA,KAAI;AAC9C,SAAK,WAAU,KAAA,SAAS,OAAO,aAAO,QAAA,OAAA,SAAA,KAAI,CAAA;AAE1C,QAAI,CAAC,SAAS,aAAa;AACzB,WAAK,OAAO,KAAK,yBACf,KAAA,SAAS,UAAI,QAAA,OAAA,SAAA,KAAI,CAAA,GACjB,KAAK,SACL,SAAS,OAAO,KAAK;WAElB;AACL,WAAK,cAAc,SAAS;AAE5B,WAAK,OAAO,IAAI,MAA0B,CAAA,GAAW;QACnD,KAAK,CAAC,GAAG,SAAQ;AACf,gBAAM,IAAI,MACR,6GAA6G,OAC3G,IAAI,CACL,kBAAkB;QAEvB;OACD;;AAGH,SAAK,QAAQ,cAAc,aAAa,KAAK,gBAAgB,KAAK,IAAI,GAAG,SAAS,OAAO,KAAK;AAE9F,SAAK,WAAW,KAAK,oBAAmB,OAAA,OAAA,EAAG,SAAS,KAAK,QAAO,GAAK,SAAS,QAAQ,CAAA;AACtF,SAAK,OAAO,IAAI,gBAAgB,GAAG,YAAY,YAAY;MACzD,SAAS,KAAK;MACd,QAAQ,SAAS,GAAG;MACpB,OAAO,KAAK;KACb;AAED,QAAI,CAAC,SAAS,aAAa;AACzB,WAAK,qBAAoB;;EAE7B;;;;EAKA,IAAI,YAAS;AACX,WAAO,IAAI,gBAAgB,KAAK,cAAc;MAC5C,SAAS,KAAK;MACd,aAAa,KAAK;KACnB;EACH;;;;EAKA,IAAI,UAAO;AACT,WAAO,IAAI,cAAsB,KAAK,YAAY,KAAK,SAAS,KAAK,KAAK;EAC5E;;;;;;EAeA,KAAK,UAAgB;AACnB,WAAO,KAAK,KAAK,KAAK,QAAQ;EAChC;;;;;;;;;EAUA,OACE,QAAqB;AAMrB,WAAO,KAAK,KAAK,OAAsB,MAAM;EAC/C;;;;;;;;;;;;;;;;;;;;;;;;;EA0BA,IACE,IACA,OAAmB,CAAA,GACnB,UAII,CAAA,GAAE;AAUN,WAAO,KAAK,KAAK,IAAI,IAAI,MAAM,OAAO;EACxC;;;;;;;;EASA,QAAQ,MAAc,OAA+B,EAAE,QAAQ,CAAA,EAAE,GAAE;AACjE,WAAO,KAAK,SAAS,QAAQ,MAAM,IAAI;EACzC;;;;EAKA,cAAW;AACT,WAAO,KAAK,SAAS,YAAW;EAClC;;;;;;;EAQA,cAAc,SAAwB;AACpC,WAAO,KAAK,SAAS,cAAc,OAAO;EAC5C;;;;EAKA,oBAAiB;AACf,WAAO,KAAK,SAAS,kBAAiB;EACxC;EAEc,kBAAe;;;AAC3B,UAAI,KAAK,aAAa;AACpB,eAAO,MAAM,KAAK,YAAW;;AAG/B,YAAM,EAAE,KAAI,IAAK,MAAM,KAAK,KAAK,WAAU;AAE3C,cAAO,MAAA,KAAA,KAAK,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE,kBAAY,QAAA,OAAA,SAAA,KAAI;;;EAG/B,wBACN,EACE,kBACA,gBACA,oBACA,SACA,YACA,UACA,MACA,MAAK,GAEP,SACAE,QAAa;;AAEb,UAAM,cAAc;MAClB,eAAe,UAAU,KAAK,WAAW;MACzC,QAAQ,GAAG,KAAK,WAAW;;AAE7B,WAAO,IAAI,mBAAmB;MAC5B,KAAK,KAAK;MACV,SAAO,OAAA,OAAA,OAAA,OAAA,CAAA,GAAO,WAAW,GAAK,OAAO;MACrC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,OAAAA;;;MAGA,+BAA8B,KAAA,mBAAmB,KAAK,aAAO,QAAA,OAAA,SAAA,KAAI;KAClE;EACH;EAEQ,oBAAoB,SAA8B;AACxD,WAAO,IAAI,eAAe,KAAK,aAAW,OAAA,OAAA,OAAA,OAAA,CAAA,GACrC,OAAO,GAAA,EACV,QAAM,OAAA,OAAO,EAAE,QAAQ,KAAK,YAAW,GAAO,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,MAAM,EAAA,CAAA,CAAA;EAEjE;EAEQ,uBAAoB;AAC1B,QAAI,OAAO,KAAK,KAAK,kBAAkB,CAAC,OAAO,YAAW;AACxD,WAAK,oBAAoB,OAAO,UAAU,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,YAAY;IACjE,CAAC;AACD,WAAO;EACT;EAEQ,oBACN,OACA,QACA,OAAc;AAEd,SACG,UAAU,qBAAqB,UAAU,gBAC1C,KAAK,uBAAuB,OAC5B;AAEA,WAAK,SAAS,QAAQ,UAAK,QAAL,UAAK,SAAL,QAAS,IAAI;AAEnC,WAAK,qBAAqB;eACjB,UAAU,cAAc;AAEjC,WAAK,SAAS,QAAQ,KAAK,WAAW;AACtC,UAAI,UAAU;AAAW,aAAK,KAAK,QAAO;AAC1C,WAAK,qBAAqB;;EAE9B;;;;AC5TK,IAAM,eAAe,CAS1B,aACA,aACA,YACgD;AAChD,SAAO,IAAI,eAA6C,aAAa,aAAa,OAAO;AAC3F;", "names": ["PostgrestBuilder", "res", "PostgrestTransformBuilder", "PostgrestFilterBuilder", "PostgrestQueryBuilder", "fetch", "head", "PostgrestClient", "fetch", "head", "get", "fetch", "FunctionRegion", "index", "SOCKET_STATES", "CHANNEL_STATES", "CHANNEL_EVENTS", "TRANSPORTS", "CONNECTION_STATE", "PostgresTypes", "REALTIME_PRESENCE_LISTEN_EVENTS", "REALTIME_POSTGRES_CHANGES_LISTEN_EVENT", "REALTIME_LISTEN_TYPES", "REALTIME_SUBSCRIBE_STATES", "_a", "_b", "type", "noop", "fetch", "resolveFetch", "fetch", "__awaiter", "__awaiter", "fetch", "resolveFetch", "version", "DEFAULT_HEADERS", "version", "fetch", "DEFAULT_HEADERS", "resolveFetch", "fetch", "version", "DEFAULT_HEADERS", "version", "resolveFetch", "Headers", "fetch", "__awaiter", "DEFAULT_DB_OPTIONS", "DEFAULT_AUTH_OPTIONS", "DEFAULT_REALTIME_OPTIONS", "DEFAULT_GLOBAL_OPTIONS", "__awaiter", "version", "DEFAULT_HEADERS", "version", "resolveFetch", "fetch", "_getErrorMessage", "handleError", "_getRequestParams", "_handleRequest", "fetch", "resolveFetch", "__rest", "DEFAULT_HEADERS", "resolveFetch", "version", "result", "expiresAt", "error", "data", "_a", "fetch"]}