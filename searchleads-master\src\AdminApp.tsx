import { Route, Routes } from "react-router-dom";
import Adminhome from "@/components/admin/Adminhome";
import Navbar from "./components/common/AdminNavbar";
import Users from "./components/admin/UserList";
import UserLogsList from "./components/admin/UserLogsList";
import Analytics from "./components/admin/Analytics";
import InvoiceLogs from "./components/admin/Invoices"
export default function App() {
  return (
    <>
      <Navbar />
      <div className="grow overflow-y-auto container mx-auto sm:px-8 md:pl-16 min-h-screen">
        <Routes>
          <Route path="/" element={<Adminhome />} />
          <Route path="/user" element={<Users />} />
          <Route path="/user-logs" element={<UserLogsList />} />
          <Route path="/analytics" element={<Analytics />} />
          <Route path="invoice-logs" element={<InvoiceLogs />} />
        </Routes>
      </div>
    </>
  );
}
