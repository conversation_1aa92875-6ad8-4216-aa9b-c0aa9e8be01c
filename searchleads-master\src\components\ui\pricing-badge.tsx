import React from 'react';
import { Badge } from '@/components/ui/badge';

interface PricingBadgeProps {
  pricingModel: string;
  className?: string;
  showPrice?: boolean;
}

export const PricingBadge: React.FC<PricingBadgeProps> = ({ 
  pricingModel, 
  className = "",
  showPrice = true 
}) => {
  const isSubscription = pricingModel === "subscription";
  
  return (
    <Badge 
      variant={isSubscription ? "default" : "secondary"}
      className={`${className} ${isSubscription ? "bg-green-100 text-green-800" : "bg-blue-100 text-blue-800"}`}
    >
      {isSubscription ? "Subscription" : "Pay-as-you-go"}
      {showPrice && (
        <span className="ml-1 text-xs">
          (${isSubscription ? "20" : "30"}/10K)
        </span>
      )}
    </Badge>
  );
};

export default PricingBadge;
