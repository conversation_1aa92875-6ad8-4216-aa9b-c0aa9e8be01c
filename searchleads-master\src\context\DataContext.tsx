/* eslint-disable react-refresh/only-export-components */
import React, { createContext, useState, useContext } from "react";

interface DataContextType {
  refreshData: boolean;
  setRefreshData: React.Dispatch<React.SetStateAction<boolean>>;
  globalCredits: number;
  setGlobalCredits: React.Dispatch<React.SetStateAction<number>>;
}

const DataContext = createContext<DataContextType | undefined>(undefined);

export const DataProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [refreshData, setRefreshData] = useState(false);
  const [globalCredits, setGlobalCredits] = useState(0);

  return (
    <DataContext.Provider value={{ refreshData, setRefreshData, globalCredits, setGlobalCredits }}>
      {children}
    </DataContext.Provider>
  );
};
export const useDataContext = () => {
  const context = useContext(DataContext);
  if (!context) {
    throw new Error("useDataContext must be used within a DataProvider");
  }
  return context;
};

