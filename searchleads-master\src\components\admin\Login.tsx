/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState } from 'react';
import { useAdminAuth } from '@/context/AdminAuthContext'
import { Input} from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { toast } from 'react-toastify';
import axois from '@/axois';

function Login() {
    const adminAuth = useAdminAuth();

    const [loading, setLoading] = useState(false)
    const [email, setEmail] = useState('')
    const [password, setPassword] = useState('')

    const handleSubmit = async () => {
        try
        {
            if(email === '' || password === '')
            {
                throw new Error('Email and password are required')
            }
            
            const response = await axois.post('/api/admin/login', {email, password})
            
            if(response.status === 200)
            {
                adminAuth.setToken(response.data.token)
                setLoading(false)
                return
            }
        }
        catch(e:any)
        {
            toast.error(e.message);
            console.error(e)
            setTimeout(() => {
                setLoading(false)
            }, 1000);
        }
    }

    return (
        <div className="p-10 min-h-screen grid place-items-center bg-background">
            <div className={`${loading?"":"pointer-events-none opacity-0"} absolute h-screen w-screen bg-black/50 z-40 duration-300`}></div>
            <div className='card max-w-lg bg-background2 w-full'>
                <h2 className='text-2xl font-medium tracking-tighter'>Admin Login</h2>
                <form onSubmit={(e)=>{
                    e.preventDefault();
                    handleSubmit();
                    setLoading(true);
                }} className="flex flex-col gap-4 p-4">
                    <Input type='email' value={email} onChange={e=>setEmail(e.target.value)} placeholder="Email"/>
                    <Input type='password' value={password} onChange={e=>setPassword(e.target.value)} placeholder="Password"/>
                    <Button className='mt-6'>Login</Button>
                </form>
            </div>
        </div>
    )
}

export default Login